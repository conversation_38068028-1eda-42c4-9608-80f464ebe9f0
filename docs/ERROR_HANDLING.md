# Error Handling Guide

This document outlines the error handling strategy implemented in the restaurant scheduling application, following Code Complete principles for robust error management.

## Error Boundary Components

### ErrorBoundary

The main error boundary component provides comprehensive error handling for any part of the application.

**Features:**
- Catches JavaScript errors and unhandled promise rejections
- Provides user-friendly error messages
- Includes retry functionality
- Logs errors for debugging
- Supports custom error handlers
- Shows technical details in development mode

**Usage:**
```svelte
<script>
  import { ErrorBoundary } from '$lib/components/ui';
</script>

<ErrorBoundary 
  onError={(error) => console.log('Custom error handler:', error)}
  showDetails={true}
>
  {#snippet children()}
    <!-- Your component content here -->
    <YourComponent />
  {/snippet}
</ErrorBoundary>
```

**With Custom Fallback:**
```svelte
<ErrorBoundary>
  {#snippet children()}
    <YourComponent />
  {/snippet}
  
  {#snippet fallback(error)}
    <div class="custom-error">
      <h2>Custom Error UI</h2>
      <p>{error.message}</p>
    </div>
  {/snippet}
</ErrorBoundary>
```

### PageErrorBoundary

A lightweight error boundary designed for page-level error handling.

**Features:**
- Simplified error UI for page-level errors
- Navigation back to dashboard
- User-friendly error messages
- Automatic error pattern recognition

**Usage:**
```svelte
<script>
  import { PageErrorBoundary } from '$lib/components/ui';
</script>

<PageErrorBoundary pageTitle="Employee Management">
  {#snippet children()}
    <!-- Your page content here -->
    <EmployeeManagementPage />
  {/snippet}
</PageErrorBoundary>
```

## Implementation Strategy

### 1. Application-Level Error Boundary

Wrap the main application in an error boundary to catch any unhandled errors:

```svelte
<!-- src/app.html or main layout -->
<ErrorBoundary className="min-h-screen">
  {#snippet children()}
    <main>
      <!-- App content -->
    </main>
  {/snippet}
</ErrorBoundary>
```

### 2. Page-Level Error Boundaries

Wrap individual pages to provide page-specific error handling:

```svelte
<!-- src/routes/employees/+page.svelte -->
<PageErrorBoundary pageTitle="Employee Management">
  {#snippet children()}
    <EmployeeList />
    <EmployeeForm />
  {/snippet}
</PageErrorBoundary>
```

### 3. Component-Level Error Boundaries

Wrap critical components that might fail:

```svelte
<!-- For data-heavy components -->
<ErrorBoundary>
  {#snippet children()}
    <ScheduleCalendar />
  {/snippet}
</ErrorBoundary>
```

## Error Types and Handling

### Network Errors
- Automatic retry functionality
- User-friendly "connection error" messages
- Fallback to cached data when available

### Authentication Errors
- Redirect to login page
- Clear session data
- Show appropriate error messages

### Validation Errors
- Field-level error display
- Form-level error summaries
- Clear error recovery instructions

### Server Errors
- Generic "server error" messages
- Automatic error reporting
- Retry mechanisms

## Best Practices

### 1. Error Logging
All errors are automatically logged with context:
```javascript
console.error('🚨 ErrorBoundary: Error caught:', error);
```

### 2. User-Friendly Messages
Production errors show user-friendly messages instead of technical details:
```javascript
const userFriendlyMessages = {
  'NetworkError': 'Network connection error. Please check your internet connection.',
  'TypeError': 'A technical error occurred. Please try again.',
  // ... more mappings
};
```

### 3. Development vs Production
- Development: Show full error details and stack traces
- Production: Show user-friendly messages and hide technical details

### 4. Error Recovery
- Provide "Try Again" buttons
- Allow navigation to safe pages (dashboard)
- Clear error state when appropriate

## Bundle Size Analysis

The application now includes bundle analysis tools to monitor and optimize bundle size:

### Commands
```bash
# Analyze bundle size with visual report
npm run analyze

# Start analysis server for interactive exploration
npm run analyze:server
```

### Monitoring
- Regular bundle size monitoring in CI/CD
- Alerts for significant size increases
- Tree-shaking optimization verification

## Integration with Existing Architecture

### Multi-Tenant Support
Error boundaries respect the multi-tenant architecture:
- Organization context preserved in error states
- User permissions maintained during error recovery
- Proper data isolation in error scenarios

### RPC-Only Pattern
Error boundaries work seamlessly with the RPC-only data access pattern:
- Service layer errors properly caught and handled
- Supabase RPC errors transformed to user-friendly messages
- Consistent error handling across all data operations

### Design System Integration
Error boundaries use the established design system:
- Consistent typography and spacing
- Proper color usage for error states
- Responsive design for all screen sizes
- Accessibility compliance (ARIA labels, keyboard navigation)

## Testing Error Boundaries

### Manual Testing
1. Trigger network errors (disconnect internet)
2. Cause JavaScript errors (modify code to throw)
3. Test unhandled promise rejections
4. Verify error recovery functionality

### Automated Testing
Error boundaries can be tested using component testing frameworks:
```javascript
// Example test structure
test('ErrorBoundary catches and displays errors', () => {
  // Test implementation
});
```

## Future Enhancements

### Error Reporting Service Integration
Consider integrating with services like:
- Sentry for error tracking
- LogRocket for session replay
- Custom analytics for error patterns

### Advanced Error Recovery
- Automatic retry with exponential backoff
- Partial page recovery (recover specific components)
- Offline mode support

### Performance Monitoring
- Error boundary performance impact measurement
- Bundle size impact of error handling code
- User experience metrics during error states

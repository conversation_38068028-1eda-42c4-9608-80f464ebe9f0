# 🚀 Deployment Guide

This guide covers deploying the Restaurant Scheduler application to various platforms.

## 📋 Pre-Deployment Checklist

### Environment Setup

- [ ] Supabase project configured and tested
- [ ] All RPC functions deployed and working
- [ ] Environment variables configured
- [ ] Database populated with initial data
- [ ] Application tested locally

### Code Quality

- [ ] All tests passing
- [ ] No console errors or warnings
- [ ] TypeScript compilation successful
- [ ] Accessibility compliance verified
- [ ] Performance optimized

## 🌐 Vercel Deployment (Recommended)

Vercel provides excellent SvelteKit support with zero configuration.

### 1. GitHub Integration

```bash
# Push your code to GitHub
git add .
git commit -m "feat: ready for deployment"
git push origin main
```

### 2. Vercel Setup

1. Go to [vercel.com](https://vercel.com)
2. Sign in with GitHub
3. Click "New Project"
4. Import your repository
5. Vercel auto-detects SvelteKit

### 3. Environment Variables

In Vercel dashboard:

1. Go to Project Settings → Environment Variables
2. Add your environment variables:
   ```
   PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   ```

### 4. Deploy

```bash
# Automatic deployment on push to main
git push origin main
```

### 5. Custom Domain (Optional)

1. Go to Project Settings → Domains
2. Add your custom domain
3. Configure DNS records as instructed

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build
RUN npm prune --production

FROM node:18-alpine AS runner

WORKDIR /app
COPY --from=builder /app/build build/
COPY --from=builder /app/node_modules node_modules/
COPY package.json .

EXPOSE 3000
ENV NODE_ENV=production
CMD ["node", "build"]
```

### Docker Compose

```yaml
version: '3.8'
services:
  restaurant-scheduler:
    build: .
    ports:
      - '3000:3000'
    environment:
      - PUBLIC_SUPABASE_URL=${PUBLIC_SUPABASE_URL}
      - PUBLIC_SUPABASE_ANON_KEY=${PUBLIC_SUPABASE_ANON_KEY}
    restart: unless-stopped
```

### Deploy with Docker

```bash
# Build and run
docker-compose up -d

# Or with Docker directly
docker build -t restaurant-scheduler .
docker run -p 3000:3000 \
  -e PUBLIC_SUPABASE_URL=your-url \
  -e PUBLIC_SUPABASE_ANON_KEY=your-key \
  restaurant-scheduler
```

## ☁️ Netlify Deployment

### 1. Build Configuration

Create `netlify.toml`:

```toml
[build]
  command = "npm run build"
  publish = "build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 2. Deploy

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login and deploy
netlify login
netlify deploy --prod --dir=build
```

## 🔧 Self-Hosted Deployment

### Prerequisites

- Node.js 18+
- PM2 (process manager)
- Nginx (reverse proxy)
- SSL certificate

### 1. Server Setup

```bash
# Install dependencies
sudo apt update
sudo apt install nodejs npm nginx

# Install PM2
npm install -g pm2
```

### 2. Application Setup

```bash
# Clone and build
git clone your-repo
cd restaurant-scheduler
npm install
npm run build

# Start with PM2
pm2 start build/index.js --name restaurant-scheduler
pm2 startup
pm2 save
```

### 3. Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 4. SSL with Let's Encrypt

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d your-domain.com
```

## 📊 Database Deployment

### Supabase Production Setup

1. **Create Production Project**

   - New Supabase project for production
   - Different from development project

2. **Deploy Schema**

   ```sql
   -- Run in production Supabase SQL Editor
   -- Create your tables and RPC functions directly in the cloud
   ```

3. **Configure RLS**

   ```sql
   -- Enable Row Level Security
   ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
   ALTER TABLE venues ENABLE ROW LEVEL SECURITY;
   ALTER TABLE shifts ENABLE ROW LEVEL SECURITY;

   -- Create policies based on your auth requirements
   ```

4. **Backup Strategy**
   - Enable automatic backups in Supabase dashboard
   - Set up monitoring and alerts

## 🔒 Security Considerations

### Environment Variables

- Never commit `.env` files
- Use different keys for production
- Rotate keys regularly
- Use Supabase service role key only for admin operations

### Database Security

- Enable Row Level Security (RLS)
- Create appropriate policies
- Use least privilege principle
- Monitor database access logs

### Application Security

- Enable HTTPS in production
- Set secure headers
- Implement rate limiting
- Monitor for security vulnerabilities

## 📈 Performance Optimization

### Build Optimization

```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer build

# Optimize images
npm install -D @sveltejs/adapter-auto
```

### Runtime Optimization

- Enable gzip compression
- Set up CDN for static assets
- Implement caching strategies
- Monitor Core Web Vitals

## 📊 Monitoring and Logging

### Application Monitoring

```javascript
// Add to app.html
<script>
	// Error tracking window.addEventListener('error', (e) =>{' '}
	{
		// Send to monitoring service
	}
	);
</script>
```

### Database Monitoring

- Monitor Supabase dashboard
- Set up alerts for high usage
- Track slow queries
- Monitor connection pool

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - run: npm ci
      - run: npm run build
      - run: npm run test

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## 🆘 Troubleshooting

### Common Issues

**Build Failures**

- Check Node.js version compatibility
- Verify all dependencies installed
- Check for TypeScript errors

**Runtime Errors**

- Verify environment variables
- Check Supabase connection
- Monitor browser console

**Performance Issues**

- Analyze bundle size
- Check database query performance
- Monitor network requests

### Rollback Strategy

```bash
# Vercel rollback
vercel --prod --rollback

# PM2 rollback
pm2 stop restaurant-scheduler
git checkout previous-version
npm run build
pm2 restart restaurant-scheduler
```

## 📞 Support

For deployment issues:

- Check application logs
- Verify environment configuration
- Test database connectivity
- Review security settings

Remember to test thoroughly in a staging environment before deploying to production! 🚀

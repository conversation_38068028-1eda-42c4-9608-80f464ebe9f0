# 🚀 Leave/Unavailability & Tip Pooling Implementation Guide

This guide provides step-by-step instructions for implementing the new Leave/Unavailability Tracking and Tip Pooling/KPI systems in your restaurant scheduling application.

## 📋 Overview

The schema extensions add two major feature sets:

1. **Leave/Unavailability Tracking System**

   - Employee leave requests with approval workflow
   - Recurring and one-time unavailability patterns
   - Integration with shift scheduling

2. **Tip Pooling & KPI-Linked Bonus System**
   - Tip distribution with multiple calculation methods
   - KPI tracking and performance measurement
   - Automated bonus calculations based on performance

## 🗄️ Database Implementation

### Step 1: Execute Schema Extensions

Run the following SQL files in your Supabase SQL Editor in this order:

1. **Schema Extensions** (`docs/database/SCHEMA_EXTENSIONS.sql`)

   ```sql
   -- Creates all new tables, enums, indexes, and triggers
   -- Estimated execution time: 2-3 minutes
   ```

2. **RLS Policies** (`docs/database/RLS_POLICIES.sql`)

   ```sql
   -- Enables Row Level Security and creates access policies
   -- Estimated execution time: 1-2 minutes
   ```

3. **Leave RPC Functions** (`docs/database/RPC_FUNCTIONS_LEAVE.sql`)

   ```sql
   -- Creates RPC functions for leave/unavailability management
   -- Estimated execution time: 2-3 minutes
   ```

4. **Tip/KPI RPC Functions** (`docs/database/RPC_FUNCTIONS_TIPS_KPI.sql`)
   ```sql
   -- Creates RPC functions for tip pooling and KPI management
   -- Estimated execution time: 3-4 minutes
   ```

### Step 2: Verify Database Setup

After running all SQL files, verify the setup:

```sql
-- Check that all tables exist
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN (
  'leave_requests', 'employee_unavailability', 'tip_pools',
  'tip_distributions', 'kpi_metrics', 'employee_kpi_records',
  'bonus_rules', 'employee_bonuses'
);

-- Check that RPC functions exist
SELECT routine_name FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_name LIKE '%leave%' OR routine_name LIKE '%tip%' OR routine_name LIKE '%kpi%';
```

## 💻 Application Integration

### Step 3: Service Layer Integration

The new services are already created and exported:

- `leaveService` - Leave requests and unavailability management
- `tipService` - Tip pooling and distribution
- `kpiService` - KPI metrics and bonus calculations

**Usage Examples:**

```typescript
import { leaveService, tipService, kpiService } from '$lib/services';

// Create a leave request
const leaveRequest = await leaveService.createLeaveRequest(organizationId, {
	employeeId: 'employee-uuid',
	requestType: 'vacation',
	startDate: '2024-02-01',
	endDate: '2024-02-05',
	reason: 'Family vacation'
});

// Create a tip pool
const tipPoolId = await tipService.createTipPool(organizationId, {
	locationId: 'location-uuid',
	poolDate: '2024-01-31',
	totalTips: 250.0,
	distributionMethod: 'hours_worked'
});

// Record KPI measurement
const kpiRecordId = await kpiService.recordEmployeeKpi(organizationId, {
	employeeId: 'employee-uuid',
	kpiMetricId: 'metric-uuid',
	measurementDate: '2024-01-31',
	actualValue: 95.5,
	notes: 'Excellent customer service'
});
```

### Step 4: Type Safety

All TypeScript types are defined in `src/lib/types.ts`:

- Leave system: `LeaveRequest`, `EmployeeUnavailability`, etc.
- Tip system: `TipPool`, `TipDistribution`, etc.
- KPI system: `KpiMetric`, `EmployeeKpiRecord`, `BonusRule`, etc.

### Step 5: Integration with Existing Features

#### Shift Scheduling Integration

The leave system integrates with shift scheduling through the `check_employee_availability` RPC function:

```typescript
// Before assigning a shift, check availability
const isAvailable = await leaveService.checkEmployeeAvailability(
	employeeId,
	shiftDate,
	startTime,
	endTime
);

if (!isAvailable) {
	throw new Error('Employee is not available for this shift');
}
```

#### Payroll Integration

Bonuses can be linked to existing payroll through the `employee_payments` table:

```sql
-- Link bonus to payment
UPDATE employee_bonuses
SET is_paid = true,
    paid_at = NOW(),
    paid_with_payment_id = 'payment-uuid'
WHERE id = 'bonus-uuid';
```

## 🎨 UI Components (Recommended)

### Leave Management Components

1. **LeaveRequestForm.svelte** - Create new leave requests
2. **LeaveRequestList.svelte** - Display and manage leave requests
3. **UnavailabilityManager.svelte** - Manage recurring/one-time unavailability
4. **LeaveCalendar.svelte** - Visual calendar view of leave requests

### Tip Pooling Components

1. **TipPoolForm.svelte** - Create and edit tip pools
2. **TipDistributionModal.svelte** - Distribute tips to employees
3. **TipPoolList.svelte** - View tip pools and distributions
4. **TipSummaryDashboard.svelte** - Analytics and summaries

### KPI & Bonus Components

1. **KpiMetricManager.svelte** - Create and manage KPI metrics
2. **KpiRecordForm.svelte** - Record employee KPI measurements
3. **BonusRuleManager.svelte** - Create and manage bonus rules
4. **BonusCalculator.svelte** - Calculate and view employee bonuses
5. **PerformanceDashboard.svelte** - KPI analytics and trends

## 🔧 Configuration & Setup

### Environment Variables

No additional environment variables are required. The system uses your existing Supabase configuration.

### Organization Setup

For each organization, you may want to:

1. **Create default KPI metrics:**

   ```typescript
   await kpiService.createKpiMetric(organizationId, {
   	metricName: 'Customer Satisfaction',
   	metricType: 'customer_rating',
   	targetValue: 4.5,
   	measurementUnit: 'stars'
   });
   ```

2. **Set up default bonus rules:**
   ```typescript
   await kpiService.createBonusRule(organizationId, {
   	ruleName: 'Monthly Performance Bonus',
   	calculationType: 'percentage',
   	kpiMetricId: 'satisfaction-metric-uuid',
   	percentageRate: 5.0,
   	minimumAchievementPercentage: 90.0,
   	effectiveFrom: '2024-01-01'
   });
   ```

## 📊 Data Migration (If Needed)

If you have existing data that needs to be migrated:

### Existing Leave Data

```sql
-- Example: Migrate from a simple 'employee_time_off' table
INSERT INTO leave_requests (
  organization_id, employee_id, request_type, start_date, end_date,
  status, requested_by, reason, created_at
)
SELECT
  e.organization_id, eto.employee_id, 'vacation', eto.start_date, eto.end_date,
  CASE WHEN eto.approved THEN 'approved'::leave_request_status ELSE 'pending'::leave_request_status END,
  eto.employee_id, eto.reason, eto.created_at
FROM employee_time_off eto
JOIN employees e ON eto.employee_id = e.id;
```

### Existing Tip Data

```sql
-- Example: Migrate from a simple 'daily_tips' table
INSERT INTO tip_pools (
  organization_id, location_id, pool_date, total_tips,
  distribution_method, is_distributed, created_at
)
SELECT
  l.organization_id, dt.location_id, dt.tip_date, dt.total_amount,
  'hours_worked'::tip_distribution_method, true, dt.created_at
FROM daily_tips dt
JOIN locations l ON dt.location_id = l.id;
```

## 🧪 Testing

### Database Testing

Test RPC functions directly in Supabase SQL Editor:

```sql
-- Test leave request creation
SELECT * FROM create_leave_request(
  'org-uuid', 'employee-uuid', 'vacation',
  '2024-02-01', '2024-02-05', 'Family vacation'
);

-- Test tip pool creation
SELECT * FROM create_tip_pool(
  'org-uuid', 'location-uuid', '2024-01-31',
  250.00, 'hours_worked', 'Good night'
);
```

### Application Testing

1. **Leave System:**

   - Create leave requests as employee
   - Approve/deny requests as manager
   - Set up recurring unavailability
   - Verify shift conflict detection

2. **Tip System:**

   - Create tip pools
   - Distribute tips with different methods
   - Verify calculations are correct

3. **KPI System:**
   - Create KPI metrics
   - Record measurements
   - Calculate bonuses
   - Verify bonus calculations

## 🚨 Important Notes

### Performance Considerations

1. **Indexes:** All necessary indexes are created automatically
2. **RLS Policies:** Ensure proper organization-based access control
3. **Data Retention:** Consider archiving old records periodically

### Security Considerations

1. **RLS Policies:** All tables have proper Row Level Security
2. **User Permissions:** Only authorized users can approve leave/bonuses
3. **Audit Trail:** All changes are logged with user information

### Backup Considerations

1. **Critical Data:** Leave requests, tip distributions, and bonuses are critical
2. **Regular Backups:** Ensure Supabase automatic backups are enabled
3. **Export Capability:** Consider implementing data export features

## 📞 Support

If you encounter issues during implementation:

1. **Database Errors:** Check Supabase logs and verify RPC function syntax
2. **TypeScript Errors:** Ensure all types are properly imported
3. **RLS Issues:** Verify user has proper organization membership
4. **Performance Issues:** Check query execution plans and indexes

The schema extensions follow the same patterns as your existing codebase and should integrate seamlessly with your current multi-tenant architecture.

## 🎯 Enhanced Scheduling Integration Guide

### New Components Created

The enhanced workforce scheduling system includes these new components:

#### **Core Components:**

- `EnhancedScheduleGrid.svelte` - Main scheduling grid with leave integration
- `LeaveRequestModal.svelte` - Create/edit/approve leave requests
- `EmployeeAvailabilityCard.svelte` - Show availability status and conflicts
- `ShiftReassignmentPanel.svelte` - Handle shift coverage workflows
- `LeaveManagementDashboard.svelte` - Comprehensive leave management

#### **Enhanced Services:**

- `enhancedScheduleService.ts` - Integrates leave, availability, and performance data
- Extended type definitions for enhanced scheduling features

### Integration Steps

#### **Step 1: Update Your Main Schedule View**

Replace your existing ScheduleView.svelte with the enhanced version:

```typescript
// In your main scheduling page
import { EnhancedScheduleGrid, LeaveManagementDashboard } from '$lib/components';
import { enhancedScheduleService } from '$lib/services';

// Load enhanced schedule data
const enhancedSchedule = await enhancedScheduleService.loadEnhancedWeeklySchedule(
	organizationId,
	weekStartDate
);
```

#### **Step 2: Add Leave Management Tab**

Add a new tab to your scheduling interface:

```svelte
<!-- Add to your tab navigation -->
<button
	onclick={() => (activeTab = 'leave')}
	class="tab-button {activeTab === 'leave' ? 'active' : ''}"
>
	Leave Management
	{#if pendingLeaveCount > 0}
		<span class="badge">{pendingLeaveCount}</span>
	{/if}
</button>

<!-- Add to your tab content -->
{#if activeTab === 'leave'}
	<LeaveManagementDashboard
		{organizationId}
		{employees}
		{currentUserId}
		{userRole}
		onrefresh={handleDataRefresh}
		onconflictDetected={handleLeaveConflict}
	/>
{/if}
```

#### **Step 3: Integrate Conflict Detection**

Handle scheduling conflicts when leave is approved:

```typescript
function handleLeaveConflict(event) {
	const { leaveRequest } = event.detail;

	// Find affected shifts
	const affectedShifts = findShiftsForEmployee(
		leaveRequest.employeeId,
		leaveRequest.startDate,
		leaveRequest.endDate
	);

	if (affectedShifts.length > 0) {
		// Open reassignment panel
		openReassignmentPanel(affectedShifts, leaveRequest);
	}
}
```

#### **Step 4: Enable Availability Overlay**

Add toggle for showing unavailability patterns:

```svelte
<!-- Add to your toolbar -->
<button
	onclick={() => (uiState.showUnavailabilityOverlay = !uiState.showUnavailabilityOverlay)}
	class="toolbar-button {uiState.showUnavailabilityOverlay ? 'active' : ''}"
>
	<svg class="icon"><!-- availability icon --></svg>
	Show Availability
</button>
```

### Mobile Responsiveness

The enhanced components include mobile-optimized views:

- **Card-based layout** for mobile devices
- **Swipe navigation** between dates
- **Simplified conflict indicators**
- **Touch-friendly interactions**

### Performance Considerations

- **Lazy loading** of availability data
- **Caching** of conflict checks
- **Optimistic updates** for better UX
- **Batch operations** for multiple reassignments

### Testing Checklist

- [ ] Leave request creation and approval workflow
- [ ] Availability checking prevents conflicts
- [ ] Shift reassignment suggestions work correctly
- [ ] Mobile interface is fully functional
- [ ] Real-time updates reflect across all views
- [ ] Performance is acceptable with large datasets

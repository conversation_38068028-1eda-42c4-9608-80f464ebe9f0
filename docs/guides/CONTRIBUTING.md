# 🤝 Contributing to Restaurant Scheduler

Thank you for your interest in contributing! This document provides guidelines and information for contributors.

## 🏗️ Development Principles

### Non-Negotiable Standards

1. **RPC-Only Data Access** - All Supabase queries must use RPC functions
2. **Reusable Component Structure** - UI components in `src/lib/components/ui/`
3. **Code Complete Principles** - Modularity, clarity, single responsibility

### Code Quality Standards

- **TypeScript** for all new code
- **Accessibility first** - WCAG 2.1 AA compliance
- **Mobile responsive** - Mobile-first design approach
- **Component-driven** - Reusable, testable components
- **Service layer** - Business logic separated from UI

## 🚀 Getting Started

### 1. Fork and <PERSON>lone

```bash
git clone https://github.com/your-username/restaurant-scheduler.git
cd restaurant-scheduler
npm install
```

### 2. Set Up Environment

```bash
cp .env.example .env
# Add your Supabase credentials
```

### 3. Set Up Database

Create your tables and RPC functions directly in your Supabase project's SQL Editor.

### 4. Start Development

```bash
npm run dev
```

## 📝 Development Workflow

### Branch Naming

- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring
- `test/description` - Adding tests

### Commit Messages

Follow conventional commits format:

```
type(scope): description

feat(shifts): add drag-and-drop functionality
fix(modal): resolve keyboard navigation issue
docs(api): update RPC function documentation
refactor(services): improve error handling
test(components): add Button component tests
```

### Pull Request Process

1. Create feature branch from `main`
2. Make changes following code standards
3. Test thoroughly (manual and automated)
4. Update documentation if needed
5. Submit PR with clear description
6. Address review feedback
7. Squash and merge when approved

## 🧪 Testing Guidelines

### Manual Testing Checklist

- [ ] Application loads without errors
- [ ] All CRUD operations work
- [ ] Responsive design on mobile/tablet/desktop
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] No console errors or warnings

### Code Testing

- Write unit tests for utility functions
- Test component props and events
- Test service layer functions
- Test error handling scenarios

## 🎨 UI/UX Guidelines

### Design Principles

- **Accessibility first** - Screen readers, keyboard navigation
- **Mobile responsive** - Touch-friendly, readable on small screens
- **Consistent** - Use design system components
- **Intuitive** - Clear labels, logical flow
- **Fast** - Optimized performance, minimal loading

### Component Standards

```typescript
// Example component structure
<script lang="ts">
  // Props interface
  interface Props {
    required: string;
    optional?: boolean;
  }

  // Props with defaults
  let { required, optional = false }: Props = $props();

  // Event handlers
  function handleClick() {
    // Implementation
  }
</script>

<!-- Accessible markup -->
<button
  onclick={handleClick}
  aria-label="Descriptive label"
  class="consistent-styling"
>
  {required}
</button>
```

### Styling Guidelines

- Use Tailwind CSS utility classes
- Follow mobile-first responsive design
- Maintain consistent spacing (4px grid)
- Use semantic color names
- Ensure sufficient color contrast (4.5:1 minimum)

## 🗄️ Database Guidelines

### RPC Function Standards

- All data access through RPC functions
- Consistent naming: `verb_noun_modifier` (e.g., `get_shifts_for_week`)
- Proper error handling and validation
- Return consistent data structures
- Include comprehensive documentation

### Schema Changes

1. Create migration script
2. Update RPC functions
3. Update TypeScript types
4. Test thoroughly
5. Document changes

## 📋 Code Review Checklist

### Functionality

- [ ] Code works as intended
- [ ] Edge cases handled
- [ ] Error handling implemented
- [ ] Performance considerations addressed

### Code Quality

- [ ] Follows TypeScript best practices
- [ ] Proper error handling
- [ ] Clear variable/function names
- [ ] Appropriate comments for complex logic
- [ ] No console.log statements in production code

### Accessibility

- [ ] Proper ARIA labels
- [ ] Keyboard navigation support
- [ ] Screen reader compatibility
- [ ] Color contrast compliance
- [ ] Focus management

### Architecture

- [ ] Follows project structure
- [ ] Uses RPC functions for data access
- [ ] Reusable components when appropriate
- [ ] Separation of concerns maintained

## 🐛 Bug Reports

### Before Reporting

1. Check existing issues
2. Reproduce the bug
3. Test in different browsers/devices
4. Check console for errors

### Bug Report Template

```markdown
## Bug Description

Clear description of the issue

## Steps to Reproduce

1. Go to...
2. Click on...
3. See error

## Expected Behavior

What should happen

## Actual Behavior

What actually happens

## Environment

- Browser: Chrome 120
- OS: macOS 14
- Device: Desktop/Mobile
- Screen size: 1920x1080

## Additional Context

Screenshots, console errors, etc.
```

## 💡 Feature Requests

### Feature Request Template

```markdown
## Feature Description

Clear description of the proposed feature

## Problem Statement

What problem does this solve?

## Proposed Solution

How should this work?

## Alternatives Considered

Other approaches considered

## Additional Context

Mockups, examples, etc.
```

## 📚 Resources

- [SvelteKit Documentation](https://kit.svelte.dev/)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

## 🎉 Recognition

Contributors will be recognized in:

- README.md contributors section
- Release notes for significant contributions
- GitHub contributors graph

Thank you for helping make Restaurant Scheduler better! 🍽️

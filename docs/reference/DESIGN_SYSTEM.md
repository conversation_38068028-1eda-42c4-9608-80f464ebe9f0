# 🎨 Restaurant Scheduler Design System

A comprehensive design language system for the restaurant scheduling application, built with consistency, accessibility, and maintainability in mind.

## 📋 Table of Contents

- [Overview](#overview)
- [Design Principles](#design-principles)
- [Typography](#typography)
- [Color Palette](#color-palette)
- [Spacing & Layout](#spacing--layout)
- [Components](#components)
- [Usage Guidelines](#usage-guidelines)
- [Implementation](#implementation)

## 🎯 Overview

The Restaurant Scheduler Design System provides a unified visual language and component library that ensures consistency across all user interfaces. Built on top of Tailwind CSS v4 and integrated with our SvelteKit application.

### Key Features

- **Consistent Visual Language** - Unified typography, colors, and spacing
- **Accessible by Default** - WCAG 2.1 AA compliant components
- **Mobile-First Responsive** - Optimized for all device sizes
- **Type-Safe** - Full TypeScript support with design token types
- **Maintainable** - Centralized configuration and utilities

## 🎨 Design Principles

### 1. Accessibility First

- Screen reader compatibility
- Keyboard navigation support
- Sufficient color contrast (4.5:1 minimum)
- Focus states for all interactive elements

### 2. Mobile-First Responsive

- Touch-friendly interface elements
- Responsive breakpoints: Mobile (≤768px), Tablet (769px-1199px), Desktop (≥1200px)
- Adaptive layouts and navigation

### 3. Flat Design Approach

- No shadows or gradients
- Clean, minimal aesthetic
- Focus on content and functionality

### 4. Consistent Interactions

- Standard 0.2s transitions
- Predictable hover and focus states
- Unified interaction patterns

## ✍️ Typography

### Font Stack

```css
font-family:
	-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell,
	'Helvetica Neue', sans-serif;
```

### Font Sizes

- **XS**: 0.75rem (12px)
- **SM**: 0.875rem (14px)
- **MD**: 1rem (16px) - Base size
- **LG**: 1.125rem (18px)
- **XL**: 1.25rem (20px)
- **2XL**: 1.5rem (24px)
- **3XL**: 1.75rem (28px) - Page titles

### Font Weights

- **Normal**: 400 - Body text
- **Medium**: 500 - UI elements
- **Semibold**: 600 - Headings
- **Bold**: 700 - Emphasis

### Line Heights

- **Tight**: 1.2 - Headings
- **Normal**: 1.5 - Body text

## 🌈 Color Palette

### Primary Colors

- **Primary**: #3b82f6 (Blue)
- **Primary Dark**: #0068F8
- **Primary Light**: #dbeafe

### Semantic Colors

- **Success**: #10b981 / Light: #dcfce7
- **Warning**: #f59e0b / Light: #fef3c7
- **Danger**: #ef4444 / Light: #fee2e2
- **Purple**: #8b5cf6 / Light: #ede9fe
- **Teal**: #58CEE1
- **Sky**: #7EB3FC

### Text Colors

- **Primary**: #1e293b - Main content
- **Secondary**: #475569 - Supporting text
- **Tertiary**: #64748b - Subtle text
- **Muted**: #94a3b8 - Placeholder text

### Background Colors

- **Primary**: #ffffff - Cards, modals
- **Secondary**: #fffeff - Alternative backgrounds
- **Tertiary**: #f1f5f9 - Page backgrounds

### Border Colors

- **Standard**: #e2e8f0 - Default borders

## 📐 Spacing & Layout

### Spacing Scale

- **XS**: 0.5rem (8px)
- **SM**: 0.75rem (12px)
- **MD**: 1rem (16px) - Base unit
- **LG**: 1.5rem (24px)
- **XL**: 2rem (32px)

### Border Radius

- **None**: 0px
- **Small**: 4px - Badges, small elements
- **Medium**: 8px - Primary radius for buttons, cards
- **Large**: 12px - Large containers
- **Full**: 9999px - Pills, circular elements

### Layout Dimensions

- **Header Height**: 60px
- **Sidebar Width**: 250px

## 🧩 Components

### Buttons

```svelte
<Button variant="primary" size="md">Primary Action</Button>
<Button variant="secondary" size="sm">Secondary</Button>
<Button variant="danger" size="lg">Delete</Button>
```

**Variants**: primary, secondary, danger, ghost, success, warning
**Sizes**: sm, md, lg

### Inputs

```svelte
<Input id="email" type="email" label="Email Address" />
<Input id="name" variant="error" error="Required field" />
```

**Variants**: default, error, success
**Sizes**: sm, md, lg

### Badges

```svelte
<Badge variant="success">Active</Badge>
<Badge variant="warning">Pending</Badge>
<Badge variant="danger">Inactive</Badge>
```

**Variants**: primary, secondary, success, warning, danger, purple

### Modals

```svelte
<Modal isOpen={true} title="Edit Employee" size="lg">
	<!-- Modal content -->
</Modal>
```

**Sizes**: sm, md, lg, xl

## 📖 Usage Guidelines

### CSS Classes

The design system provides utility classes that can be used directly:

```css
/* Typography */
.text-heading     /* Semibold weight, tight line-height */
.text-body        /* Normal line-height */
.text-page-title  /* 1.75rem, semibold, tight */

/* Components */
.card-base        /* White background, border, padding */
.button-base      /* Base button styling */
.input-base       /* Base input styling */
.badge-base       /* Base badge styling */

/* Layout */
.layout-header    /* Fixed header styling */
.layout-sidebar   /* Fixed sidebar styling */

/* Utilities */
.focus-ring       /* Accessible focus styling */
.animate-fade-in  /* Fade in animation */
```

### Design Tokens

Access design tokens programmatically:

```typescript
import { designTokens, getColor, getSpacing } from '$lib/design-system';

const primaryColor = getColor('primary', 'primary');
const mediumSpacing = getSpacing('md');
```

## 🛠️ Implementation

### File Structure

```
src/lib/design-system/
├── index.ts          # Main export file
├── types.ts          # TypeScript definitions
├── config.ts         # Design tokens configuration
└── utils.ts          # Utility functions
```

### Integration

The design system is integrated into:

1. **Global Styles** (`src/app.css`) - CSS custom properties and base classes
2. **Tailwind Config** (`tailwind.config.js`) - Extended theme configuration
3. **UI Components** (`src/lib/components/ui/`) - Updated to use design system
4. **Layout Components** - Consistent styling across layouts

### Development Workflow

1. **Use Design System Components** - Always prefer design system components over custom styling
2. **Follow Naming Conventions** - Use semantic naming for variants and sizes
3. **Maintain Consistency** - Stick to defined spacing, colors, and typography scales
4. **Test Accessibility** - Verify keyboard navigation and screen reader compatibility
5. **Document Changes** - Update this documentation when adding new components or tokens

### Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Graceful degradation for older browsers

---

For questions or contributions to the design system, please refer to the [Contributing Guidelines](../guides/CONTRIBUTING.md).

# 🔌 API Documentation

## RPC Functions Reference

All data access in this application is done through Supabase RPC (Remote Procedure Call) functions. This ensures consistent data access patterns and better security.

## 👥 Employee Functions

### `get_all_employees()`

Returns all employees in the system.

**Returns:**

```typescript
{
  id: UUID,
  name: string,
  role: string,
  default_daily_rate: number,
  is_active: boolean,
  created_at: timestamp,
  updated_at: timestamp | null
}[]
```

### `get_active_employees()`

Returns only active employees.

**Returns:** Same as `get_all_employees()` but filtered for `is_active = true`

### `create_employee(p_name, p_role, p_default_daily_rate, p_is_active)`

Creates a new employee.

**Parameters:**

- `p_name: string` - Employee's full name
- `p_role: string` - Job role (e.g., "Chef", "Server")
- `p_default_daily_rate: number` - Default hourly rate
- `p_is_active: boolean` - Whether employee is active (default: true)

**Returns:** Created employee object

### `update_employee(p_employee_id, p_name, p_role, p_default_daily_rate, p_is_active)`

Updates an existing employee.

**Parameters:**

- `p_employee_id: UUID` - Employee ID to update
- Other parameters same as `create_employee`

**Returns:** Updated employee object

### `delete_employee(p_employee_id)`

Soft deletes an employee (sets `is_active = false`).

**Parameters:**

- `p_employee_id: UUID` - Employee ID to delete

**Returns:** void

## 🏢 Venue Functions

### `get_all_venues()`

Returns all venues in the system.

**Returns:**

```typescript
{
  id: UUID,
  name: string,
  color: string,
  address: string | null,
  created_at: timestamp,
  updated_at: timestamp | null
}[]
```

### `create_venue(p_name, p_color, p_address)`

Creates a new venue.

**Parameters:**

- `p_name: string` - Venue name
- `p_color: string` - Hex color code (e.g., "#3B82F6")
- `p_address: string | null` - Optional address

**Returns:** Created venue object

### `update_venue(p_venue_id, p_name, p_color, p_address)`

Updates an existing venue.

**Parameters:**

- `p_venue_id: UUID` - Venue ID to update
- Other parameters same as `create_venue`

**Returns:** Updated venue object

### `delete_venue(p_venue_id)`

Hard deletes a venue.

**Parameters:**

- `p_venue_id: UUID` - Venue ID to delete

**Returns:** void

## 📅 Shift Functions

### `get_shifts_for_week(p_week_start)`

Returns all shifts for a specific week.

**Parameters:**

- `p_week_start: DATE` - Start date of the week (Monday)

**Returns:**

```typescript
{
  id: UUID,
  employee_id: UUID | null,
  venue_id: UUID,
  shift_date: DATE,
  start_time: TIME,
  end_time: TIME,
  total_hours: number,
  daily_rate: number,
  is_paid: boolean,
  advance_deduction: number,
  notes: string | null,
  created_at: timestamp,
  updated_at: timestamp | null
}[]
```

### `get_shifts_for_employee(p_employee_id, p_start_date, p_end_date)`

Returns shifts for a specific employee within a date range.

**Parameters:**

- `p_employee_id: UUID` - Employee ID
- `p_start_date: DATE | null` - Start date filter (optional)
- `p_end_date: DATE | null` - End date filter (optional)

**Returns:** Array of shift objects

### `get_shifts_for_venue(p_venue_id, p_start_date, p_end_date)`

Returns shifts for a specific venue within a date range.

**Parameters:**

- `p_venue_id: UUID` - Venue ID
- `p_start_date: DATE | null` - Start date filter (optional)
- `p_end_date: DATE | null` - End date filter (optional)

**Returns:** Array of shift objects

### `create_shift(p_employee_id, p_venue_id, p_shift_date, p_start_time, p_end_time, p_daily_rate, p_is_paid, p_advance_deduction)`

Creates a new shift.

**Parameters:**

- `p_employee_id: UUID | null` - Employee ID (null for unassigned)
- `p_venue_id: UUID` - Venue ID
- `p_shift_date: DATE` - Shift date
- `p_start_time: TIME` - Start time
- `p_end_time: TIME` - End time
- `p_daily_rate: number` - Hourly rate for this shift
- `p_is_paid: boolean` - Payment status
- `p_advance_deduction: number` - Advance deduction amount

**Returns:** Created shift object

### `update_shift(p_shift_id, ...)`

Updates an existing shift.

**Parameters:**

- `p_shift_id: UUID` - Shift ID to update
- Other parameters same as `create_shift`

**Returns:** Updated shift object

### `delete_shift(p_shift_id)`

Hard deletes a shift.

**Parameters:**

- `p_shift_id: UUID` - Shift ID to delete

**Returns:** void

### `duplicate_week_shifts(p_source_week_start, p_target_week_starts, p_conflict_resolution, p_organization_id)`

Duplicates all shifts from a source week to one or more target weeks.

**Parameters:**

- `p_source_week_start: DATE` - Start date of the source week (Monday)
- `p_target_week_starts: DATE[]` - Array of start dates for target weeks
- `p_conflict_resolution: TEXT` - Conflict resolution mode: 'merge' or 'replace' (default: 'merge')
- `p_organization_id: UUID` - Organization ID (optional, uses user's organization if null)

**Returns:**

```typescript
{
  success: boolean,
  totalShiftsDuplicated: number,
  conflictsDetected: number,
  conflictsResolved: number,
  targetWeeksProcessed: number,
  message: string,
  details: [
    {
      targetWeekStart: string,
      shiftsDuplicated: number,
      conflictsFound: ConflictInfo[],
      conflictsResolved: number
    }
  ],
  organizationId: UUID,
  sourceWeekStart: DATE,
  conflictResolution: string
}
```

**Conflict Resolution Modes:**

- `merge`: Skip creating shifts when conflicts are detected (default)
- `replace`: Replace existing shifts with duplicated ones when conflicts occur

### `mark_shift_as_paid(p_shift_id)`

Marks a shift as paid.

**Parameters:**

- `p_shift_id: UUID` - Shift ID to mark as paid

**Returns:** Updated shift object

### `mark_multiple_shifts_as_paid(p_shift_ids)`

Marks multiple shifts as paid.

**Parameters:**

- `p_shift_ids: UUID[]` - Array of shift IDs to mark as paid

**Returns:** Array of updated shift objects

### `assign_shift_to_employee(p_shift_id, p_employee_id)`

Assigns an unassigned shift to an employee.

**Parameters:**

- `p_shift_id: UUID` - Shift ID to assign
- `p_employee_id: UUID` - Employee ID to assign to

**Returns:** Updated shift object

## 🔒 Security Notes

- All RPC functions respect Row Level Security (RLS) policies
- Authentication is handled by Supabase Auth
- Input validation is performed at the database level
- All functions use parameterized queries to prevent SQL injection

## 📝 Usage Examples

```typescript
// Get active employees
const employees = await supabase.rpc('get_active_employees');

// Get shifts for current week
const weekStart = new Date();
weekStart.setDate(weekStart.getDate() - weekStart.getDay() + 1); // Monday
const shifts = await supabase.rpc('get_shifts_for_week', {
	p_week_start: weekStart.toISOString().split('T')[0]
});

// Create a new shift
const newShift = await supabase.rpc('create_shift', {
	p_employee_id: 'employee-uuid',
	p_venue_id: 'venue-uuid',
	p_shift_date: '2024-01-15',
	p_start_time: '09:00',
	p_end_time: '17:00',
	p_daily_rate: 25.0,
	p_is_paid: false,
	p_advance_deduction: 0.0
});

// Duplicate week to next week (merge mode - skip conflicts)
const duplicateResult = await supabase.rpc('duplicate_week_shifts', {
	p_source_week_start: '2024-01-15', // Monday of source week
	p_target_week_starts: ['2024-01-22'], // Monday of target week
	p_conflict_resolution: 'merge' // Skip conflicts
});

// Duplicate week to multiple weeks (replace mode - overwrite conflicts)
const bulkDuplicate = await supabase.rpc('duplicate_week_shifts', {
	p_source_week_start: '2024-01-15',
	p_target_week_starts: ['2024-01-22', '2024-01-29', '2024-02-05'],
	p_conflict_resolution: 'replace' // Overwrite conflicts
});
```

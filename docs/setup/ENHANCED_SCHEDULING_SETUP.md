# Enhanced Weekly Scheduling System Setup Guide

This guide provides step-by-step instructions for implementing the enhanced scheduling system with leave management, unavailability tracking, and KPI monitoring.

## 🎯 Overview

The enhanced scheduling system adds the following features to your existing restaurant scheduling application:

- **Leave Management**: Employee leave requests with approval workflows
- **Unavailability Tracking**: Recurring and one-time unavailability patterns
- **Conflict Detection**: Real-time scheduling conflict identification
- **KPI Monitoring**: Performance tracking and metrics
- **Multi-tenant Architecture**: Organization-based data isolation
- **Enhanced UI**: Tabbed interface with availability overlays

## 📋 Prerequisites

- Existing Tallyca restaurant scheduling application
- Supabase project with authentication enabled
- Node.js and npm/pnpm installed
- VS Code or similar editor

## 🗄️ Database Setup

### Step 1: Execute Database Migrations

Execute the following SQL files in your Supabase SQL Editor **in order**:

#### 1. Enhanced Schema (`database/migrations/001_enhanced_scheduling_schema.sql`)

This creates the core tables for the enhanced scheduling system:

```sql
-- Copy and paste the contents of 001_enhanced_scheduling_schema.sql
-- into your Supabase SQL Editor and execute
```

**Tables Created:**
- `organizations` - Multi-tenant organization management
- `organization_memberships` - User roles within organizations
- `locations` - Enhanced venues with organization support
- `leave_requests` - Employee leave request management
- `employee_unavailability` - Recurring and one-time unavailability
- `kpi_definitions` - KPI metric definitions
- `kpi_records` - KPI measurement records
- `shifts` - Enhanced shifts table (if not exists)

#### 2. RPC Functions (`database/migrations/002_enhanced_scheduling_rpc_functions.sql`)

This creates the business logic functions:

```sql
-- Copy and paste the contents of 002_enhanced_scheduling_rpc_functions.sql
-- into your Supabase SQL Editor and execute
```

**Functions Created:**
- `create_leave_request()` - Create new leave requests
- `update_leave_request_status()` - Approve/deny leave requests
- `get_leave_requests()` - Fetch leave requests with filters
- `check_employee_availability()` - Check availability for time slots
- `get_employee_unavailability()` - Fetch unavailability patterns
- `record_kpi_measurement()` - Record KPI measurements
- `get_employee_kpi_records()` - Fetch KPI records
- `get_shifts_with_conflicts()` - Get shifts with conflict detection

#### 3. RLS Policies (`database/migrations/003_enhanced_scheduling_rls_policies.sql`)

This sets up Row Level Security for data protection:

```sql
-- Copy and paste the contents of 003_enhanced_scheduling_rls_policies.sql
-- into your Supabase SQL Editor and execute
```

**Security Features:**
- Multi-tenant data isolation
- Role-based access control (viewer, editor, admin, superadmin)
- User can only access their organization's data
- Proper permissions for leave management and KPI tracking

### Step 2: Verify Database Setup

After executing all migrations, verify the setup:

1. **Check Tables**: Ensure all tables are created in your Supabase dashboard
2. **Test RPC Functions**: Try calling a simple function like `check_employee_availability`
3. **Verify RLS**: Ensure RLS is enabled on all tables

## 🔧 Application Configuration

### Step 3: Update Environment Variables

Ensure your `.env.local` file has the correct Supabase configuration:

```env
PUBLIC_SUPABASE_URL=your_supabase_url
PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Step 4: Install Dependencies

The enhanced scheduling system uses existing dependencies. No additional packages are required.

### Step 5: Update Type Definitions

The enhanced types are already included in `src/lib/types.ts`. Key new types:

- `EnhancedWeeklySchedule`
- `LeaveRequest`
- `EmployeeUnavailability`
- `ShiftConflict`
- `EmployeeAvailabilityStatus`

## 🚀 Testing the Enhanced System

### Step 6: Test the Enhanced Features

1. **Start the Development Server**:
   ```bash
   npm run dev
   ```

2. **Access the Test Page**:
   Navigate to `http://localhost:5174/test-enhanced`

3. **Test Enhanced Schedule View**:
   Navigate to `http://localhost:5174/` and look for:
   - Tab navigation (Schedule / Leave Management)
   - Enhanced mode toggle
   - Availability overlay toggle

### Step 7: Create Test Data

Use the test page to create sample data:

1. **Create Sample Leave Request**: Click "Create Sample Leave" button
2. **Test Enhanced Grid**: Switch to enhanced mode and observe leave indicators
3. **Test Leave Management**: Switch to the Leave Management tab

## 🎨 UI Features

### Enhanced Schedule Grid

- **Color-coded Leave Types**:
  - 🟡 Vacation (Yellow)
  - 🔴 Sick Leave (Red)
  - 🔵 Personal (Blue)
  - 🟣 Emergency (Purple)

- **Availability Overlay**: Toggle to show/hide employee availability status
- **Conflict Detection**: Real-time warnings for scheduling conflicts
- **Drag & Drop Enhancement**: Availability validation during shift assignment

### Leave Management Dashboard

- **Leave Request Creation**: Modal for creating leave requests
- **Approval Workflow**: Admin/manager approval interface
- **Conflict Resolution**: Automatic shift reassignment suggestions
- **Status Tracking**: Visual status indicators for all requests

## 🔐 Security Considerations

### Row Level Security (RLS)

The system implements comprehensive RLS policies:

- **Organization Isolation**: Users can only access their organization's data
- **Role-based Permissions**: Different access levels based on user roles
- **Data Protection**: All sensitive operations require proper authorization

### User Roles

- **Viewer**: Can view schedules and their own leave requests
- **Editor**: Can create/edit schedules and leave requests
- **Admin**: Can approve leave requests and manage KPIs
- **Superadmin**: Full access to organization management

## 📊 KPI Tracking

### KPI Types

- **Sales**: Revenue and sales metrics
- **Customer Satisfaction**: Service quality metrics
- **Efficiency**: Operational efficiency metrics
- **Attendance**: Employee attendance tracking
- **Custom**: Organization-specific metrics

### Recording KPIs

Use the `record_kpi_measurement()` function to track performance:

```sql
SELECT record_kpi_measurement(
    'org_id',
    'kpi_definition_id',
    'employee_id',
    'location_id',
    'shift_id',
    actual_value,
    measurement_date,
    'notes'
);
```

## 🔄 Migration from Existing System

### Backward Compatibility

The enhanced system maintains full backward compatibility:

- Existing restaurant/venue data works with new location system
- Current employee and shift data is preserved
- Authentication and user management unchanged

### Data Migration

If you have existing data:

1. **Organizations**: Create organizations for existing restaurants
2. **Memberships**: Add users to appropriate organizations
3. **Locations**: Migrate venues to locations table
4. **Shifts**: Update shifts to reference organization_id

## 🐛 Troubleshooting

### Common Issues

1. **RLS Errors**: Ensure user has proper organization membership
2. **Function Errors**: Check that all migrations were executed in order
3. **Type Errors**: Verify TypeScript types are up to date
4. **Permission Errors**: Check user roles and organization access

### Debug Mode

Enable debug logging by checking browser console for:
- `🔄` Loading operations
- `✅` Successful operations
- `❌` Error operations

## 📚 API Reference

### Key RPC Functions

```typescript
// Create leave request
const leaveRequest = await supabase.rpc('create_leave_request', {
  p_organization_id: 'org_id',
  p_employee_id: 'employee_id',
  p_request_type: 'vacation',
  p_start_date: '2024-01-01',
  p_end_date: '2024-01-05',
  p_reason: 'Family vacation'
});

// Check availability
const isAvailable = await supabase.rpc('check_employee_availability', {
  p_employee_id: 'employee_id',
  p_date: '2024-01-01',
  p_start_time: '09:00',
  p_end_time: '17:00'
});

// Get leave requests
const { data: leaveRequests } = await supabase.rpc('get_leave_requests', {
  p_organization_id: 'org_id',
  p_status: 'pending'
});
```

## 🎉 Next Steps

After successful setup:

1. **Train Users**: Introduce staff to new leave management features
2. **Configure KPIs**: Set up relevant performance metrics
3. **Customize UI**: Adjust colors and branding as needed
4. **Monitor Performance**: Use built-in analytics and logging
5. **Gather Feedback**: Collect user feedback for improvements

## 📞 Support

For issues or questions:

1. Check the troubleshooting section above
2. Review the Code Complete principles in the codebase
3. Examine browser console for detailed error messages
4. Verify database setup and RLS policies

The enhanced scheduling system follows Code Complete best practices with clear separation of concerns, comprehensive error handling, and maintainable architecture.

# 📚 Restaurant Scheduler Documentation

Welcome to the comprehensive documentation for the Restaurant Scheduler application.

## 📁 Documentation Structure

### 🚀 **Setup & Configuration**
- [`setup/ENHANCED_SCHEDULING_SETUP.md`](setup/ENHANCED_SCHEDULING_SETUP.md) - Enhanced scheduling system setup guide

### 📖 **Guides & Tutorials**
- [`guides/TESTING_GUIDE.md`](guides/TESTING_GUIDE.md) - UI/UX testing procedures and manual testing
- [`guides/IMPLEMENTATION_GUIDE.md`](guides/IMPLEMENTATION_GUIDE.md) - Leave/unavailability & tip pooling implementation
- [`guides/CONTRIBUTING.md`](guides/CONTRIBUTING.md) - Contribution guidelines and development standards

### 📋 **Reference Documentation**
- [`reference/API.md`](reference/API.md) - API reference and RPC function documentation
- [`reference/DESIGN_SYSTEM.md`](reference/DESIGN_SYSTEM.md) - Design system and UI component guidelines
- [`reference/ENHANCED_SCHEDULING_README.md`](reference/ENHANCED_SCHEDULING_README.md) - Enhanced scheduling features documentation

### 🚀 **Deployment**
- [`deployment/DEPLOYMENT.md`](deployment/DEPLOYMENT.md) - Production deployment instructions

### 🗄️ **Database**
- [`database/SCHEMA_EXTENSIONS.sql`](database/SCHEMA_EXTENSIONS.sql) - Database schema extensions
- [`database/RLS_POLICIES.sql`](database/RLS_POLICIES.sql) - Row Level Security policies
- [`database/RPC_FUNCTIONS_LEAVE.sql`](database/RPC_FUNCTIONS_LEAVE.sql) - Leave management RPC functions
- [`database/RPC_FUNCTIONS_TIPS_KPI.sql`](database/RPC_FUNCTIONS_TIPS_KPI.sql) - Tip pooling and KPI RPC functions

## 🎯 Quick Navigation

### For New Developers
1. Start with the main [`README.md`](../README.md) in the project root
2. Follow the [`setup/ENHANCED_SCHEDULING_SETUP.md`](setup/ENHANCED_SCHEDULING_SETUP.md) for enhanced features
3. Review [`guides/CONTRIBUTING.md`](guides/CONTRIBUTING.md) for development standards
4. Use [`guides/TESTING_GUIDE.md`](guides/TESTING_GUIDE.md) for testing procedures

### For Implementation
1. [`guides/IMPLEMENTATION_GUIDE.md`](guides/IMPLEMENTATION_GUIDE.md) - Complete implementation guide
2. [`database/`](database/) - All SQL files for database setup
3. [`reference/API.md`](reference/API.md) - API reference for development

### For Design & UI
1. [`reference/DESIGN_SYSTEM.md`](reference/DESIGN_SYSTEM.md) - Design system guidelines
2. [`reference/ENHANCED_SCHEDULING_README.md`](reference/ENHANCED_SCHEDULING_README.md) - Enhanced UI features

### For Deployment
1. [`deployment/DEPLOYMENT.md`](deployment/DEPLOYMENT.md) - Production deployment guide

---

## 📞 Support

For questions about the documentation or the application:
- Check the relevant guide in this documentation
- Review the main project [`README.md`](../README.md)
- Follow the troubleshooting sections in the guides
- Examine browser console for detailed error messages

The documentation follows Code Complete best practices with clear organization and comprehensive coverage of all application features.

# 🍽️ Restaurant Scheduler

A modern, accessible restaurant scheduling assistant built with SvelteKit and Supabase. Features drag-and-drop calendar UI, employee management, shift editing, payroll tracking, and mobile responsiveness.

## ✨ Features

### **Core Features**

- 📅 **Weekly Schedule View** - Intuitive calendar interface for shift management
- 👥 **Employee Management** - Add, edit, and manage staff with roles and rates
- 🏢 **Multi-Venue Support** - Manage shifts across multiple restaurant locations
- 💰 **Payroll Tracking** - Track paid/unpaid shifts and advance deductions
- 📱 **Mobile Responsive** - Works seamlessly on all devices
- ♿ **Accessibility First** - WCAG compliant with screen reader support
- 🔒 **Secure** - Built with Supabase RLS and proper authentication

### **🌟 Enhanced Features** _(NEW)_

- 🏖️ **Leave Management** - Complete leave request workflows with approval processes
- 🚨 **Conflict Detection** - Automatic identification of scheduling conflicts
- 📊 **Availability Tracking** - Real-time employee availability with visual overlays
- 🎯 **Performance Integration** - KPI tracking and performance-based scheduling
- 🔄 **Smart Reassignment** - Intelligent shift reassignment suggestions
- 🎨 **Color-Coded Indicators** - Visual leave type identification (vacation=yellow, sick=red, personal=blue, emergency=purple)
- 🏢 **Multi-Tenant Architecture** - Organization-based data isolation with role-based access control

## 🏗️ Architecture

### **Non-Negotiable Standards**

1. **RPC-Only Data Access** - All Supabase queries via remote procedure calls
2. **Reusable Component Structure** - Modular UI components in `src/lib/components/ui/`
3. **Code Complete Principles** - Modularity, clarity, single responsibility

## 🛠️ Tech Stack

- [SvelteKit](https://kit.svelte.dev/) - Web framework
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Supabase](https://supabase.com/) - Backend and database
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [Vite](https://vitejs.dev/) - Build tool
- [ESLint](https://eslint.org/) - Code linting
- [Prettier](https://prettier.io/) - Code formatting

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm/yarn/pnpm
- Supabase account

### 1. Clone and Install

```bash
git clone https://github.com/MetaZucchiniberg/work_scheduler.git
cd work_scheduler
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
# Edit .env with your Supabase credentials
```

### 3. Database Setup

#### **Enhanced Setup (Recommended)**

```bash
# Run the automated database setup
node scripts/setup-database.js

# Execute the SQL migrations in your Supabase SQL Editor:
# 1. database/migrations/001_enhanced_scheduling_schema.sql
# 2. database/migrations/002_enhanced_scheduling_rpc_functions.sql
# 3. database/migrations/003_enhanced_scheduling_rls_policies.sql

# Create sample data (requires authentication)
node scripts/create-sample-data.js
```

#### **Manual Setup**

1. Create a new Supabase project
2. Go to SQL Editor in your Supabase dashboard
3. Execute the migration files in order
4. Verify tables and RPC functions are created

### 4. Start Development

```bash
npm run dev
```

Visit `http://localhost:5173` to see your application!

## 📊 Database Schema

### Tables

- **employees** - Staff information with roles and rates
- **venues** - Restaurant locations with colors and addresses
- **shifts** - Work schedules with pay tracking

### RPC Functions

- `get_active_employees()` - Fetch active staff
- `get_all_venues()` - Fetch all locations
- `get_shifts_for_week(date)` - Fetch weekly schedule
- `create_shift(...)` - Add new shifts
- `update_shift(...)` - Modify existing shifts
- `mark_shift_as_paid(id)` - Update payroll status

## 🛠️ Development

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run check        # Run type checking
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
```

### Code Standards

- **TypeScript** for type safety
- **ESLint + Prettier** for code formatting
- **Accessibility first** - proper ARIA labels and keyboard navigation
- **Component-driven** - reusable UI components
- **Service layer** - all business logic in services/

### Project Structure

```
src/lib/
├── services/           # Business logic (RPC calls only)
├── components/         # UI components
│   └── ui/            # Reusable components (Button, Modal, etc.)
├── types.ts           # TypeScript definitions
└── utils.ts           # Helper functions
```

## 🧪 Testing

### **Enhanced Testing Suite**

```bash
# Run comprehensive system tests
node scripts/test-enhanced-features.js

# Test individual components at /test-enhanced
npm run dev
# Visit http://localhost:5174/test-enhanced
```

### **Manual Testing**

1. Test your RPC functions directly in Supabase SQL Editor
2. Verify all functions return data without errors
3. Test application loads at `http://localhost:5174`
4. Test enhanced features:
   - Leave request creation and approval
   - Conflict detection and resolution
   - Availability tracking and overlays
   - Performance metrics integration

### **Expected Results**

- ✅ Employee list displays sample data
- ✅ Location list shows multiple venues
- ✅ Weekly schedule loads without errors
- ✅ Leave management workflows function correctly
- ✅ Conflict detection identifies scheduling issues
- ✅ Availability overlays display correctly
- ✅ No console errors about missing functions

## 🚀 Deployment

### Vercel (Recommended)

```bash
npm run build
# Deploy to Vercel via GitHub integration
```

### Manual Deployment

```bash
npm run build
# Upload dist/ folder to your hosting provider
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 🆘 Troubleshooting

### Common Issues

**"Function not found" errors**

- Ensure RPC functions have been created in Supabase SQL Editor
- Check Supabase project is active
- Verify environment variables are correct

**Import/module errors**

- Ensure all dependencies are installed: `npm install`
- Check file paths use proper `.js` extensions for imports

**Accessibility warnings**

- All form inputs have associated labels
- Buttons have descriptive text or aria-labels
- Modal dialogs support keyboard navigation

## 📞 Support

- 📧 Create an issue for bug reports
- 💬 Discussions for feature requests
- 📖 Check Supabase documentation for database help

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

Built with ❤️ using SvelteKit, Supabase, and modern web standards.

#!/usr/bin/env node

/**
 * Test Script for Employee and Shift Creation
 * This script tests the fixed RPC functions and services
 * Following Code Complete principles: Clear testing, comprehensive validation
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Test authentication and organization context
 */
async function testAuthentication() {
    console.log('\n🔐 Testing Authentication...');
    
    try {
        // Sign in with test user
        const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
            email: '<EMAIL>',
            password: 'password123' // You'll need to use the actual password
        });

        if (authError) {
            console.error('❌ Authentication failed:', authError.message);
            return false;
        }

        console.log('✅ Authentication successful');
        console.log('🔍 User ID:', authData.user.id);
        
        return true;
    } catch (error) {
        console.error('❌ Authentication error:', error);
        return false;
    }
}

/**
 * Test organization membership
 */
async function testOrganizationMembership() {
    console.log('\n🏢 Testing Organization Membership...');
    
    try {
        const { data, error } = await supabase.rpc('ensure_user_organization_membership');
        
        if (error) {
            console.error('❌ Organization membership failed:', error.message);
            return null;
        }

        console.log('✅ Organization membership ensured');
        console.log('🔍 Organization ID:', data);
        
        return data;
    } catch (error) {
        console.error('❌ Organization membership error:', error);
        return null;
    }
}

/**
 * Test employee creation
 */
async function testEmployeeCreation() {
    console.log('\n👥 Testing Employee Creation...');
    
    try {
        const { data, error } = await supabase.rpc('create_employee', {
            p_name: `Test Employee ${Date.now()}`,
            p_role: 'Server',
            p_default_daily_rate: 120.00,
            p_restaurant_id: null,
            p_is_active: true
        });

        if (error) {
            console.error('❌ Employee creation failed:', error.message);
            return null;
        }

        console.log('✅ Employee created successfully');
        console.log('🔍 Employee:', data[0]);
        
        return data[0];
    } catch (error) {
        console.error('❌ Employee creation error:', error);
        return null;
    }
}

/**
 * Test fetching employees
 */
async function testFetchEmployees() {
    console.log('\n📋 Testing Fetch Employees...');
    
    try {
        const { data, error } = await supabase.rpc('fetch_active_employees');
        
        if (error) {
            console.error('❌ Fetch employees failed:', error.message);
            return [];
        }

        console.log('✅ Employees fetched successfully');
        console.log('🔍 Employee count:', data.length);
        
        return data;
    } catch (error) {
        console.error('❌ Fetch employees error:', error);
        return [];
    }
}

/**
 * Test shift creation
 */
async function testShiftCreation(employeeId, locationId) {
    console.log('\n📅 Testing Shift Creation...');
    
    try {
        const { data, error } = await supabase.rpc('create_shift', {
            p_employee_id: employeeId,
            p_location_id: locationId,
            p_shift_date: '2025-01-16',
            p_start_time: '10:00',
            p_end_time: '18:00',
            p_daily_rate: 120.00,
            p_hours_worked: 8.0,
            p_is_paid: false,
            p_advance_deduction: 0,
            p_notes: 'Test shift from script'
        });

        if (error) {
            console.error('❌ Shift creation failed:', error.message);
            return null;
        }

        console.log('✅ Shift created successfully');
        console.log('🔍 Shift:', data[0]);
        
        return data[0];
    } catch (error) {
        console.error('❌ Shift creation error:', error);
        return null;
    }
}

/**
 * Get available locations
 */
async function getLocations() {
    console.log('\n📍 Getting Available Locations...');
    
    try {
        const { data, error } = await supabase
            .from('locations')
            .select('id, name')
            .eq('is_active', true)
            .limit(1);

        if (error) {
            console.error('❌ Get locations failed:', error.message);
            return [];
        }

        console.log('✅ Locations retrieved');
        console.log('🔍 Available locations:', data);
        
        return data;
    } catch (error) {
        console.error('❌ Get locations error:', error);
        return [];
    }
}

/**
 * Main test function
 */
async function runTests() {
    console.log('🚀 Starting Employee and Shift Creation Tests...');
    
    // Test authentication
    const authSuccess = await testAuthentication();
    if (!authSuccess) {
        console.error('❌ Authentication failed, stopping tests');
        return;
    }

    // Test organization membership
    const orgId = await testOrganizationMembership();
    if (!orgId) {
        console.error('❌ Organization membership failed, stopping tests');
        return;
    }

    // Test employee creation
    const employee = await testEmployeeCreation();
    if (!employee) {
        console.error('❌ Employee creation failed, stopping tests');
        return;
    }

    // Test fetching employees
    const employees = await testFetchEmployees();

    // Get locations for shift creation
    const locations = await getLocations();
    if (locations.length === 0) {
        console.error('❌ No locations available for shift creation');
        return;
    }

    // Test shift creation
    const shift = await testShiftCreation(employee.id, locations[0].id);

    // Summary
    console.log('\n📊 Test Summary:');
    console.log('✅ Authentication:', authSuccess ? 'PASSED' : 'FAILED');
    console.log('✅ Organization Membership:', orgId ? 'PASSED' : 'FAILED');
    console.log('✅ Employee Creation:', employee ? 'PASSED' : 'FAILED');
    console.log('✅ Fetch Employees:', employees.length > 0 ? 'PASSED' : 'FAILED');
    console.log('✅ Shift Creation:', shift ? 'PASSED' : 'FAILED');

    if (authSuccess && orgId && employee && employees.length > 0 && shift) {
        console.log('\n🎉 All tests PASSED! Employee and shift creation is working correctly.');
    } else {
        console.log('\n❌ Some tests FAILED. Please check the errors above.');
    }

    // Sign out
    await supabase.auth.signOut();
    console.log('\n🔐 Signed out successfully');
}

// Run the tests
runTests().catch(console.error);

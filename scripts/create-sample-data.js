/**
 * Sample Data Creation Script
 * Creates realistic test data for the enhanced scheduling system
 * Following Code Complete principles: Comprehensive test data, clear organization
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '..', '.env') });

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Sample data templates
 * Following Code Complete: Realistic, comprehensive test data
 */
const sampleData = {
    organizations: [
        {
            name: 'Demo Restaurant Group',
            plan: 'premium',
            logo_url: null
        }
    ],
    
    locations: [
        {
            name: 'Downtown Location',
            address_line1: '123 Main Street',
            city: 'Demo City',
            postal_code: '12345',
            country: 'US',
            contact_name: 'John Manager',
            contact_email: '<EMAIL>',
            contact_phone: '******-0123'
        },
        {
            name: 'Uptown Location',
            address_line1: '456 Oak Avenue',
            city: 'Demo City',
            postal_code: '12346',
            country: 'US',
            contact_name: 'Jane Manager',
            contact_email: '<EMAIL>',
            contact_phone: '******-0124'
        }
    ],
    
    employees: [
        {
            name: 'Alice Johnson',
            full_name: 'Alice Marie Johnson',
            role: 'FOH',
            default_daily_rate: 120.00,
            default_hourly_rate: 15.00,
            default_daily_working_hours: 8.0,
            is_active: true
        },
        {
            name: 'Bob Smith',
            full_name: 'Robert James Smith',
            role: 'BOH',
            default_daily_rate: 140.00,
            default_hourly_rate: 17.50,
            default_daily_working_hours: 8.0,
            is_active: true
        },
        {
            name: 'Carol Davis',
            full_name: 'Carol Anne Davis',
            role: 'FOH',
            default_daily_rate: 110.00,
            default_hourly_rate: 13.75,
            default_daily_working_hours: 8.0,
            is_active: true
        },
        {
            name: 'David Wilson',
            full_name: 'David Michael Wilson',
            role: 'DELIVERY',
            default_daily_rate: 100.00,
            default_hourly_rate: 12.50,
            default_daily_working_hours: 8.0,
            is_active: true
        }
    ],
    
    kpiDefinitions: [
        {
            metric_name: 'Customer Satisfaction',
            metric_description: 'Average customer rating out of 5',
            unit_of_measurement: 'rating',
            target_value: 4.5,
            measurement_frequency: 'daily'
        },
        {
            metric_name: 'Order Accuracy',
            metric_description: 'Percentage of orders completed correctly',
            unit_of_measurement: 'percentage',
            target_value: 95.0,
            measurement_frequency: 'daily'
        },
        {
            metric_name: 'Sales Target',
            metric_description: 'Daily sales target achievement',
            unit_of_measurement: 'currency',
            target_value: 2000.00,
            measurement_frequency: 'daily'
        }
    ]
};

/**
 * Create sample organization and get ID
 * Following Code Complete: Clear data creation workflow
 */
async function createSampleOrganization() {
    console.log('🏢 Creating sample organization...');
    
    try {
        // Note: This requires authentication to work properly
        console.log('   ⚠️  Organization creation requires authenticated user');
        console.log('   Please create an organization manually through the application');
        
        // For testing, we'll try to find an existing organization
        const { data: existingOrgs, error } = await supabase
            .from('organizations')
            .select('*')
            .limit(1);
        
        if (error) {
            console.log('   ⚠️  Cannot access organizations table:', error.message);
            return null;
        }
        
        if (existingOrgs && existingOrgs.length > 0) {
            console.log(`   ✅ Using existing organization: ${existingOrgs[0].name}`);
            return existingOrgs[0].id;
        }
        
        console.log('   ⚠️  No organizations found. Please create one through the application.');
        return null;
        
    } catch (error) {
        console.error('   ❌ Failed to create organization:', error.message);
        return null;
    }
}

/**
 * Create sample locations
 * Following Code Complete: Comprehensive location data
 */
async function createSampleLocations(organizationId) {
    if (!organizationId) return [];
    
    console.log('📍 Creating sample locations...');
    
    const createdLocations = [];
    
    for (const locationData of sampleData.locations) {
        try {
            const { data, error } = await supabase
                .from('locations')
                .insert({
                    ...locationData,
                    organization_id: organizationId
                })
                .select()
                .single();
            
            if (error) {
                console.log(`   ⚠️  Failed to create location ${locationData.name}:`, error.message);
            } else {
                console.log(`   ✅ Created location: ${data.name}`);
                createdLocations.push(data);
            }
            
        } catch (error) {
            console.log(`   ⚠️  Error creating location ${locationData.name}:`, error.message);
        }
    }
    
    return createdLocations;
}

/**
 * Create sample employees
 * Following Code Complete: Realistic employee data
 */
async function createSampleEmployees(organizationId) {
    if (!organizationId) return [];
    
    console.log('👥 Creating sample employees...');
    
    const createdEmployees = [];
    
    for (const employeeData of sampleData.employees) {
        try {
            const { data, error } = await supabase
                .from('employees')
                .insert({
                    ...employeeData,
                    organization_id: organizationId
                })
                .select()
                .single();
            
            if (error) {
                console.log(`   ⚠️  Failed to create employee ${employeeData.name}:`, error.message);
            } else {
                console.log(`   ✅ Created employee: ${data.name}`);
                createdEmployees.push(data);
            }
            
        } catch (error) {
            console.log(`   ⚠️  Error creating employee ${employeeData.name}:`, error.message);
        }
    }
    
    return createdEmployees;
}

/**
 * Create sample KPI definitions
 * Following Code Complete: Comprehensive KPI setup
 */
async function createSampleKPIDefinitions(organizationId) {
    if (!organizationId) return [];
    
    console.log('📊 Creating sample KPI definitions...');
    
    const createdKPIs = [];
    
    for (const kpiData of sampleData.kpiDefinitions) {
        try {
            const { data, error } = await supabase
                .from('kpi_definitions')
                .insert({
                    ...kpiData,
                    organization_id: organizationId
                })
                .select()
                .single();
            
            if (error) {
                console.log(`   ⚠️  Failed to create KPI ${kpiData.metric_name}:`, error.message);
            } else {
                console.log(`   ✅ Created KPI: ${data.metric_name}`);
                createdKPIs.push(data);
            }
            
        } catch (error) {
            console.log(`   ⚠️  Error creating KPI ${kpiData.metric_name}:`, error.message);
        }
    }
    
    return createdKPIs;
}

/**
 * Create sample shifts for the current week
 * Following Code Complete: Realistic scheduling data
 */
async function createSampleShifts(employees, locations) {
    if (!employees.length || !locations.length) return [];
    
    console.log('📅 Creating sample shifts...');
    
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay()); // Start from Sunday
    
    const createdShifts = [];
    
    // Create shifts for the next 7 days
    for (let dayOffset = 0; dayOffset < 7; dayOffset++) {
        const shiftDate = new Date(startOfWeek);
        shiftDate.setDate(startOfWeek.getDate() + dayOffset);
        
        // Skip if it's more than 3 days in the past
        if (shiftDate < new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)) {
            continue;
        }
        
        // Create 2-3 shifts per day
        const shiftsPerDay = Math.floor(Math.random() * 2) + 2;
        
        for (let shiftIndex = 0; shiftIndex < shiftsPerDay; shiftIndex++) {
            const employee = employees[Math.floor(Math.random() * employees.length)];
            const location = locations[Math.floor(Math.random() * locations.length)];
            
            const startHour = 9 + (shiftIndex * 4); // 9am, 1pm, 5pm
            const endHour = startHour + 8; // 8-hour shifts
            
            try {
                const { data, error } = await supabase
                    .from('shifts')
                    .insert({
                        employee_id: employee.id,
                        location_id: location.id,
                        date: shiftDate.toISOString().split('T')[0],
                        start_time: `${startHour.toString().padStart(2, '0')}:00:00`,
                        end_time: `${endHour.toString().padStart(2, '0')}:00:00`,
                        daily_rate: employee.default_daily_rate,
                        total_hours: 8.0,
                        is_paid: false
                    })
                    .select()
                    .single();
                
                if (error) {
                    console.log(`   ⚠️  Failed to create shift:`, error.message);
                } else {
                    createdShifts.push(data);
                }
                
            } catch (error) {
                console.log(`   ⚠️  Error creating shift:`, error.message);
            }
        }
    }
    
    console.log(`   ✅ Created ${createdShifts.length} sample shifts`);
    return createdShifts;
}

/**
 * Main sample data creation function
 * Following Code Complete: Comprehensive setup workflow
 */
async function main() {
    console.log('🎭 Enhanced Scheduling Sample Data Creation');
    console.log('==========================================');
    
    // Create organization
    const organizationId = await createSampleOrganization();
    
    if (!organizationId) {
        console.log('\n⚠️  Cannot proceed without organization. Please:');
        console.log('1. Sign up/login to the application');
        console.log('2. Create an organization through the UI');
        console.log('3. Run this script again');
        return;
    }
    
    // Create locations
    const locations = await createSampleLocations(organizationId);
    
    // Create employees
    const employees = await createSampleEmployees(organizationId);
    
    // Create KPI definitions
    const kpiDefinitions = await createSampleKPIDefinitions(organizationId);
    
    // Create sample shifts
    const shifts = await createSampleShifts(employees, locations);
    
    console.log('\n🎉 Sample data creation completed!');
    console.log(`📊 Summary:`);
    console.log(`   - Organization: ${organizationId ? '1' : '0'}`);
    console.log(`   - Locations: ${locations.length}`);
    console.log(`   - Employees: ${employees.length}`);
    console.log(`   - KPI Definitions: ${kpiDefinitions.length}`);
    console.log(`   - Shifts: ${shifts.length}`);
    
    console.log('\n📋 Next steps:');
    console.log('1. Test the enhanced scheduling features in the application');
    console.log('2. Create leave requests through the UI');
    console.log('3. Test the availability checking and conflict detection');
    console.log('4. Verify the enhanced schedule grid displays correctly');
}

// Run the sample data creation
main().catch(error => {
    console.error('❌ Sample data creation failed:', error);
    process.exit(1);
});

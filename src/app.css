@import 'tailwindcss';

/* ===== RESTAURANT SCHEDULER DESIGN SYSTEM ===== */

/* Design Tokens - CSS Custom Properties */
:root {
  /* Typography */
  --font-family-primary: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-md: 1rem;       /* 16px - base */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.75rem;   /* 28px */

  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;

  /* Colors - Primary */
  --color-primary: #3b82f6;
  --color-primary-dark: #0068F8;
  --color-primary-light: #dbeafe;

  /* Colors - Semantic */
  --color-success: #10b981;
  --color-success-light: #dcfce7;
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  --color-danger: #ef4444;
  --color-danger-light: #fee2e2;
  --color-purple: #8b5cf6;
  --color-purple-light: #ede9fe;
  --color-teal: #58CEE1;
  --color-sky: #7EB3FC;

  /* Colors - Text */
  --color-text-primary: #1e293b;
  --color-text-secondary: #475569;
  --color-text-tertiary: #64748b;
  --color-text-muted: #94a3b8;

  /* Colors - Backgrounds */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #fffeff;
  --color-bg-tertiary: #f1f5f9;

  /* Colors - Borders */
  --color-border-standard: #e2e8f0;

  /* Spacing Scale */
  --spacing-xs: 0.5rem;   /* 8px */
  --spacing-sm: 0.75rem;  /* 12px */
  --spacing-md: 1rem;     /* 16px - base */
  --spacing-lg: 1.5rem;   /* 24px */
  --spacing-xl: 2rem;     /* 32px */

  /* Border Radius */
  --radius-none: 0px;
  --radius-small: 4px;
  --radius-medium: 8px;    /* primary */
  --radius-large: 12px;
  --radius-full: 9999px;

  /* Layout Dimensions */
  --header-height: 60px;
  --sidebar-width: 250px;

  /* Transitions */
  --transition-standard: 0.2s ease-in-out;
}

/* Base Typography Styles */
.text-heading {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
}

.text-body {
  line-height: var(--line-height-normal);
}

.text-page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
}

/* Component Base Styles */
.card-base {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-standard);
  border-radius: var(--radius-medium);
  padding: var(--spacing-md);
}

.button-base {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-standard);
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.button-base:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.btn-danger {
  background-color: var(--color-danger);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

.btn-ghost {
  background-color: transparent;
  color: #374151;
}

.btn-ghost:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.btn-success {
  background-color: var(--color-success);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #059669;
}

.btn-warning {
  background-color: var(--color-warning);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--color-primary);
  color: white;
}

/* Button Sizes */
.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: var(--font-size-sm);
}

.btn-md {
  padding: 0.5rem 1rem;
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: var(--font-size-md);
}

/* Utility Classes */
.w-full {
  width: 100%;
}

.input-base {
  width: 100%;
  padding: var(--spacing-xs);
  border-radius: var(--radius-medium);
  border: 1px solid var(--color-border-standard);
  font-size: var(--font-size-sm);
  transition: all var(--transition-standard);
}

.input-base:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

/* Status Badge Base */
.badge-base {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* Layout Utilities */
.layout-header {
  height: var(--header-height);
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-border-standard);
}

.layout-sidebar {
  width: var(--sidebar-width);
  background-color: var(--color-bg-primary);
  border-right: 1px solid var(--color-border-standard);
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform var(--transition-standard);
  }

  .layout-sidebar.open {
    transform: translateX(0);
  }

  .layout-header {
    position: relative;
  }
}

/* Focus Styles for Accessibility */
.focus-ring:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Animation Utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tooltip Utilities */
.tooltip-container {
  position: relative;
}

.tooltip-container:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Disable tooltips during drag operations */
.tooltip-container.drag-disabled:hover .tooltip-text {
  visibility: hidden;
  opacity: 0;
}

.tooltip-text {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  z-index: 1000;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #1f2937;
  color: white;
  text-align: center;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  transition: opacity 0s; /* Remove delay for instant tooltips */
  pointer-events: none;
}

/* Instant tooltip for shift action buttons */
.shift-action-tooltip {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  z-index: 10000; /* Higher than action overlay */
  bottom: calc(100% + 8px); /* More space above button */
  left: 50%;
  transform: translateX(-50%);
  background-color: #1f2937;
  color: white;
  text-align: center;
  padding: 6px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  transition: none; /* No transition for instant appearance */
  pointer-events: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  /* Ensure tooltip stays within viewport */
  max-width: 200px;
  word-wrap: break-word;
}

.shift-action-tooltip.show {
  visibility: visible;
  opacity: 1;
}

/* Arrow for shift action tooltips */
.shift-action-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -6px;
  border-width: 6px;
  border-style: solid;
  border-color: #1f2937 transparent transparent transparent;
}

/* Fallback positioning for tooltips near viewport edges */
.shift-action-tooltip.tooltip-left {
  left: 0;
  transform: translateX(0);
}

.shift-action-tooltip.tooltip-right {
  right: 0;
  left: auto;
  transform: translateX(0);
}

/* Ensure tooltips can extend beyond schedule grid cells */
.schedule-grid-cell {
  position: relative;
}

.schedule-grid-cell:hover {
  overflow: visible !important;
  z-index: 100;
}

/* Ensure shift blocks can show tooltips above overflow containers */
.shift-block {
  position: relative;
}

.shift-block:hover {
  z-index: 200;
}

/* Override overflow hidden on table cells when hovering shift blocks */
td:has(.shift-block:hover) {
  overflow: visible !important;
  z-index: 150;
}

/* Fallback for browsers that don't support :has() */
.tooltip-active {
  overflow: visible !important;
  z-index: 150;
}

.tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #1f2937 transparent transparent transparent;
}

/* Drag Creation Utilities */
.drag-create-preview {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.3), rgba(59, 130, 246, 0.1));
  border: 2px dashed #3b82f6;
  border-radius: 4px;
  pointer-events: none;
  z-index: 10;
}

.drag-create-target {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border: 2px dashed #3b82f6 !important;
  transition: all 0.15s ease-in-out;
}

.drag-create-source {
  background-color: rgba(34, 197, 94, 0.1) !important;
  border: 2px solid #22c55e !important;
}

.drag-create-invalid {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border: 2px dashed #ef4444 !important;
}

.drag-create-cursor {
  cursor: crosshair !important;
}

.drag-create-active {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Vertical Drag Creation Utilities */
.drag-create-vertical-preview {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(34, 197, 94, 0.3), rgba(34, 197, 94, 0.1));
  border: 2px dashed #22c55e;
  border-radius: 4px;
  pointer-events: none;
  z-index: 10;
}

.drag-create-vertical-target {
  background-color: rgba(34, 197, 94, 0.1) !important;
  border: 2px dashed #22c55e !important;
  transition: all 0.15s ease-in-out;
}

.drag-create-horizontal-target {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border: 2px dashed #3b82f6 !important;
  transition: all 0.15s ease-in-out;
}

/* Frozen header styles for schedule grids */
.schedule-grid-container {
  /* Ensure proper scrolling behavior */
  position: relative;
  overflow: hidden;
}

.schedule-grid-container .sticky {
  /* Ensure sticky positioning works on all browsers */
  position: -webkit-sticky;
  position: sticky;
}

/* Enhanced frozen header with blue underline */
.schedule-grid-frozen-header {
  border-bottom: 1.5px solid #3b82f6 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 3px;
}

/* Mobile responsiveness for frozen headers */
@media (max-width: 768px) {
  .schedule-grid-container {
    max-height: calc(100vh - 120px);
  }

  /* Adjust z-index for mobile to prevent overlap issues */
  .schedule-grid-container thead {
    z-index: 25 !important;
  }

  .schedule-grid-container th.sticky {
    z-index: 35 !important;
  }

  .schedule-grid-container td.sticky {
    z-index: 15 !important;
  }

  /* Reduce header height on mobile */
  .schedule-grid-container th {
    padding: 0.5rem 0.25rem !important;
    font-size: 0.75rem !important;
  }

  /* Adjust employee column width on mobile */
  .schedule-grid-container th:first-child,
  .schedule-grid-container td:first-child {
    min-width: 100px !important;
    width: 100px !important;
  }
}

/* Tablet responsiveness */
@media (max-width: 1024px) and (min-width: 769px) {
  .schedule-grid-container {
    max-height: calc(100vh - 160px);
  }
}

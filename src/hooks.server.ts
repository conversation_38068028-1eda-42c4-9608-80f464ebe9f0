/**
 * Server-Side Security Hooks
 * Implements comprehensive route protection and session validation
 * Following Code Complete principles: Security-first, clear error handling
 */

import { redirect, type Handle } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '$lib/env.js';

// ============================================================================
// CONFIGURATION
// ============================================================================

/**
 * Routes that don't require authentication
 * Following Code Complete: Explicit configuration, easy to maintain
 */
const PUBLIC_ROUTES = [
	'/',
	'/auth',
	'/auth/signin',
	'/auth/signup',
	'/auth/reset-password',
	'/auth/callback'
];

/**
 * Routes that require organization context
 * Following Code Complete: Clear business rule definition
 */
const ORGANIZATION_REQUIRED_ROUTES = [
	'/scheduler',
	'/scheduler/schedule',
	'/scheduler/employees'
];

/**
 * Security configuration
 * Following Code Complete: Centralized configuration
 */
const SECURITY_CONFIG = {
	sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours in milliseconds
	tokenRefreshThreshold: 5 * 60 * 1000, // 5 minutes before expiry
	maxFailedAttempts: 5,
	rateLimitWindow: 60 * 1000, // 1 minute
	maxRequestsPerWindow: 100
};

// ============================================================================
// TYPES
// ============================================================================

interface SecurityContext {
	isAuthenticated: boolean;
	user: any | null;
	session: any | null;
	organizationId: string | null;
	userRole: string | null;
	sessionExpiry: number | null;
	needsRefresh: boolean;
}

interface SecurityEvent {
	type: 'auth_check' | 'token_refresh' | 'access_denied' | 'session_expired';
	userId?: string;
	route: string;
	timestamp: number;
	details?: any;
}

// ============================================================================
// SECURITY UTILITIES
// ============================================================================

/**
 * Create Supabase client for server-side operations
 * Following Code Complete: Consistent client creation
 */
function createServerSupabaseClient(accessToken?: string) {
	const client = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
	
	if (accessToken) {
		client.auth.setSession({
			access_token: accessToken,
			refresh_token: '', // Not needed for validation
			expires_at: 0,
			expires_in: 0,
			token_type: 'bearer',
			user: null
		});
	}
	
	return client;
}

/**
 * Check if route is public (doesn't require authentication)
 * Following Code Complete: Pure function, clear logic
 */
function isPublicRoute(pathname: string): boolean {
	return PUBLIC_ROUTES.some(route => {
		if (route === '/') return pathname === '/';
		return pathname.startsWith(route);
	});
}

/**
 * Check if route requires organization context
 * Following Code Complete: Business rule validation
 */
function requiresOrganization(pathname: string): boolean {
	return ORGANIZATION_REQUIRED_ROUTES.some(route => pathname.startsWith(route));
}

/**
 * Validate session and extract security context
 * Following Code Complete: Comprehensive validation, error handling
 */
async function validateSecurityContext(
	accessToken: string | null,
	refreshToken: string | null
): Promise<SecurityContext> {
	const defaultContext: SecurityContext = {
		isAuthenticated: false,
		user: null,
		session: null,
		organizationId: null,
		userRole: null,
		sessionExpiry: null,
		needsRefresh: false
	};

	if (!accessToken) {
		return defaultContext;
	}

	try {
		const supabase = createServerSupabaseClient(accessToken);
		
		// Validate current session
		const { data: { user }, error: userError } = await supabase.auth.getUser();
		
		if (userError || !user) {
			console.warn('🔒 hooks.server: Invalid session token');
			return defaultContext;
		}

		// Check token expiration
		const tokenPayload = JSON.parse(atob(accessToken.split('.')[1]));
		const expiryTime = tokenPayload.exp * 1000;
		const currentTime = Date.now();
		const timeUntilExpiry = expiryTime - currentTime;
		
		// Check if token needs refresh (5 minutes before expiry)
		const needsRefresh = timeUntilExpiry < SECURITY_CONFIG.tokenRefreshThreshold;
		
		if (timeUntilExpiry <= 0) {
			console.warn('🔒 hooks.server: Token expired');
			return defaultContext;
		}

		// Get user's organization context
		let organizationId: string | null = null;
		let userRole: string | null = null;

		try {
			const { data: organizations, error: orgError } = await supabase.rpc('get_user_organizations');
			
			if (!orgError && organizations && organizations.length > 0) {
				organizationId = organizations[0].id;
				userRole = organizations[0].user_role;
			}
		} catch (orgError) {
			console.warn('🔒 hooks.server: Could not load organization context:', orgError);
			// Continue without organization context for now
		}

		return {
			isAuthenticated: true,
			user,
			session: { access_token: accessToken, refresh_token: refreshToken },
			organizationId,
			userRole,
			sessionExpiry: expiryTime,
			needsRefresh
		};

	} catch (error) {
		console.error('🔒 hooks.server: Security context validation failed:', error);
		return defaultContext;
	}
}

/**
 * Log security events for monitoring
 * Following Code Complete: Comprehensive logging, structured data
 */
function logSecurityEvent(event: SecurityEvent): void {
	const logEntry = {
		...event,
		timestamp: new Date(event.timestamp).toISOString(),
		userAgent: 'server-side',
		ip: 'server-context'
	};
	
	// In production, send to monitoring service
	console.log('🔒 Security Event:', JSON.stringify(logEntry));
}

// ============================================================================
// MAIN SECURITY HANDLE
// ============================================================================

/**
 * Main security handle for SvelteKit
 * Implements comprehensive route protection and session validation
 * Following Code Complete: Single responsibility, clear flow
 */
export const handle: Handle = async ({ event, resolve }) => {
	const { url, cookies, request } = event;
	const pathname = url.pathname;
	
	// Skip security checks for static assets
	if (pathname.startsWith('/_app/') || pathname.startsWith('/favicon')) {
		return resolve(event);
	}

	// Extract tokens from cookies
	const accessToken = cookies.get('sb-access-token') || null;
	const refreshToken = cookies.get('sb-refresh-token') || null;

	// Validate security context
	const securityContext = await validateSecurityContext(accessToken, refreshToken);
	
	// Log authentication check
	logSecurityEvent({
		type: 'auth_check',
		userId: securityContext.user?.id,
		route: pathname,
		timestamp: Date.now(),
		details: {
			isAuthenticated: securityContext.isAuthenticated,
			hasOrganization: !!securityContext.organizationId,
			needsRefresh: securityContext.needsRefresh
		}
	});

	// Check if route requires authentication
	if (!isPublicRoute(pathname)) {
		if (!securityContext.isAuthenticated) {
			logSecurityEvent({
				type: 'access_denied',
				route: pathname,
				timestamp: Date.now(),
				details: { reason: 'not_authenticated' }
			});
			
			// Redirect to login with return URL
			const returnUrl = encodeURIComponent(pathname + url.search);
			throw redirect(302, `/auth/signin?returnUrl=${returnUrl}`);
		}

		// Check session expiry
		if (securityContext.sessionExpiry && securityContext.sessionExpiry <= Date.now()) {
			logSecurityEvent({
				type: 'session_expired',
				userId: securityContext.user?.id,
				route: pathname,
				timestamp: Date.now()
			});
			
			// Clear expired session cookies
			cookies.delete('sb-access-token', { path: '/' });
			cookies.delete('sb-refresh-token', { path: '/' });
			
			throw redirect(302, '/auth/signin?expired=true');
		}

		// Check organization context for routes that require it
		if (requiresOrganization(pathname) && !securityContext.organizationId) {
			logSecurityEvent({
				type: 'access_denied',
				userId: securityContext.user?.id,
				route: pathname,
				timestamp: Date.now(),
				details: { reason: 'no_organization_context' }
			});
			
			throw redirect(302, '/setup/organization');
		}
	}

	// Add security context to locals for use in load functions
	event.locals.security = securityContext;
	
	// Set security headers
	const response = await resolve(event);
	
	// Add security headers
	response.headers.set('X-Frame-Options', 'DENY');
	response.headers.set('X-Content-Type-Options', 'nosniff');
	response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
	response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
	
	// Add CSRF protection for non-GET requests
	if (request.method !== 'GET') {
		const origin = request.headers.get('origin');
		const host = request.headers.get('host');
		
		if (!origin || !host || !origin.includes(host)) {
			console.warn('🔒 hooks.server: CSRF protection triggered');
			return new Response('Forbidden', { status: 403 });
		}
	}

	return response;
};

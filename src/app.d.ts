// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces

/**
 * Security context interface for server-side authentication
 * Following Code Complete principles: Clear type definitions
 */
interface SecurityContext {
	isAuthenticated: boolean;
	user: any | null;
	session: any | null;
	organizationId: string | null;
	userRole: string | null;
	sessionExpiry: number | null;
	needsRefresh: boolean;
}

declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			security: SecurityContext;
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

export {};

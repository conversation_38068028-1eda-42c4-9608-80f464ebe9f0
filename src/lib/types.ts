// TypeScript types for the restaurant scheduling system

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface User {
	id: string;
	email: string;
	created_at: string;
	updated_at?: string;
}

export interface AuthSession {
	user: User;
	access_token: string;
	refresh_token: string;
	expires_at: number;
}

export interface Restaurant {
	id: string;
	name: string;
	owner_id: string;
	created_at: string;
	updated_at?: string;
}

export interface UserRestaurant {
	id: string;
	user_id: string;
	restaurant_id: string;
	role: 'owner' | 'manager' | 'staff';
	created_at: string;
}

export interface AuthState {
	user: User | null;
	session: AuthSession | null;
	currentRestaurant: Restaurant | null;
	userRestaurants: UserRestaurant[];
	isLoading: boolean;
	isAuthenticated: boolean;
	needsRestaurantSetup?: boolean;
}

// ============================================================================
// CORE TYPES
// ============================================================================

// Organization types
export interface Organization {
	id: string;
	name: string;
	ownerUserId: string;
	plan: string;
	logoUrl?: string;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface OrganizationMembership {
	id: string;
	userId: string;
	organizationId: string;
	role: 'viewer' | 'editor' | 'admin' | 'superadmin';
	isActive: boolean;
	createdAt: Date;
}

// Employee location junction
export interface EmployeeLocation {
	id: string;
	employeeId: string;
	locationId: string;
	isPrimary: boolean;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
}

// Payment types
export interface EmployeePayment {
	id: string;
	employeeId: string;
	weekStartDate: Date;
	totalDue: number;
	totalPaid: number;
	paidAt?: Date;
	paymentMethod?: 'CASH' | 'TRANSFER' | 'OTHER';
	notes?: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface EmployeePaymentLog {
	id: string;
	employeePaymentId: string;
	userId: string;
	action: 'CREATED' | 'UPDATED' | 'PAID' | 'CANCELLED' | 'REVERSED' | 'ADJUSTED';
	amount?: number;
	notes?: string;
	createdAt: Date;
}

export interface Employee {
	id: string;
	organizationId: string;
	fullName: string;
	name?: string; // Keep for backward compatibility
	role: 'FOH' | 'BOH' | 'DELIVERY' | 'OTHER';
	defaultDailyRate: number;
	defaultDailyWorkingHours: number;
	defaultHourlyRate: number;
	userId?: string;
	active: boolean;
	isActive?: boolean; // Keep for backward compatibility
	createdAt: Date;
	updatedAt: Date;
}

// Location types - Replaces Venue
export interface Location {
	id: string;
	organizationId: string;
	name: string;
	addressLine1?: string;
	addressLine2?: string;
	city?: string;
	postalCode?: string;
	country?: string;
	contactName?: string;
	contactEmail?: string;
	contactPhone?: string;
	isActive: boolean;
	sortOrder: number;
	barsy?: any; // JSON data for Barsy integration
	createdAt: Date;
	updatedAt: Date;
}

// Keep Venue interface for backward compatibility
export interface Venue {
	id: string;
	name: string;
	color: string; // For UI color coding
	address?: string;
}

export interface Shift {
	id: string;
	employeeId: string | null; // null for unassigned shifts
	locationId: string; // Updated from venueId
	venueId?: string; // Keep for backward compatibility
	date: Date;
	startTime: string; // Format: "HH:MM"
	endTime: string; // Format: "HH:MM"
	hoursWorked: number; // New field from schema
	totalHours?: number; // Keep for backward compatibility
	dailyRate: number; // Can override employee's default rate
	dailyRateOverride?: number; // New field from schema
	isPaid: boolean;
	advanceDeduction: number; // Amount deducted as advance payment
	notes?: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface WeeklySchedule {
	weekStartDate: Date; // Monday of the week
	shifts: Shift[];
	employees: Employee[];
	venues: Venue[];
}

// ============================================================================
// LEAVE/UNAVAILABILITY TRACKING TYPES
// ============================================================================

export type LeaveRequestStatus = 'pending' | 'approved' | 'denied' | 'cancelled';
export type LeaveRequestType = 'vacation' | 'sick' | 'personal' | 'emergency' | 'other';
export type UnavailabilityType = 'recurring' | 'one_time';
export type UnavailabilityDayOfWeek = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';

export interface LeaveRequest {
	id: string;
	organizationId: string;
	employeeId: string;
	employeeName?: string;
	requestType: LeaveRequestType;
	startDate: Date;
	endDate: Date;
	status: LeaveRequestStatus;
	requestedBy: string;
	requestedByName?: string;
	approvedBy?: string;
	approvedByName?: string;
	approvedAt?: Date;
	reason?: string;
	managerNotes?: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface LeaveRequestFormData {
	employeeId: string;
	requestType: LeaveRequestType;
	startDate: string; // ISO date string
	endDate: string; // ISO date string
	reason?: string;
}

export interface EmployeeUnavailability {
	id: string;
	organizationId: string;
	employeeId: string;
	employeeName?: string;
	unavailabilityType: UnavailabilityType;
	// For recurring unavailability
	dayOfWeek?: UnavailabilityDayOfWeek;
	startTime?: string; // HH:MM format
	endTime?: string; // HH:MM format
	// For one-time unavailability
	specificDate?: Date;
	specificStartTime?: string; // HH:MM format
	specificEndTime?: string; // HH:MM format
	reason?: string;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface RecurringUnavailabilityFormData {
	employeeId: string;
	dayOfWeek: UnavailabilityDayOfWeek;
	startTime: string; // HH:MM format
	endTime: string; // HH:MM format
	reason?: string;
}

export interface OneTimeUnavailabilityFormData {
	employeeId: string;
	specificDate: string; // ISO date string
	specificStartTime?: string; // HH:MM format
	specificEndTime?: string; // HH:MM format
	reason?: string;
}

// ============================================================================
// TIP POOLING & KPI SYSTEM TYPES
// ============================================================================

export type TipDistributionMethod = 'equal' | 'hours_worked' | 'performance_weighted' | 'custom';
export type KpiMetricType = 'sales_target' | 'customer_rating' | 'attendance_rate' | 'punctuality' | 'custom';
export type BonusCalculationType = 'percentage' | 'fixed_amount' | 'tiered';

export interface TipPool {
	id: string;
	organizationId: string;
	locationId: string;
	locationName?: string;
	poolDate: Date;
	totalTips: number;
	distributionMethod: TipDistributionMethod;
	isDistributed: boolean;
	distributedAt?: Date;
	distributedBy?: string;
	distributedByName?: string;
	notes?: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface TipDistribution {
	id: string;
	tipPoolId: string;
	employeeId: string;
	employeeName?: string;
	hoursWorked: number;
	performanceMultiplier: number;
	tipAmount: number;
	createdAt: Date;
}

export interface TipPoolFormData {
	locationId: string;
	poolDate: string; // ISO date string
	totalTips: number;
	distributionMethod: TipDistributionMethod;
	notes?: string;
}

export interface TipDistributionInput {
	employeeId: string;
	hoursWorked: number;
	performanceMultiplier?: number;
}

export interface KpiMetric {
	id: string;
	organizationId: string;
	metricName: string;
	metricType: KpiMetricType;
	targetValue?: number;
	measurementUnit?: string;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface EmployeeKpiRecord {
	id: string;
	organizationId: string;
	employeeId: string;
	employeeName?: string;
	kpiMetricId: string;
	metricName?: string;
	measurementDate: Date;
	actualValue: number;
	targetValue?: number;
	achievementPercentage?: number;
	notes?: string;
	recordedBy: string;
	recordedByName?: string;
	createdAt: Date;
}

export interface KpiMetricFormData {
	metricName: string;
	metricType: KpiMetricType;
	targetValue?: number;
	measurementUnit?: string;
}

export interface KpiRecordFormData {
	employeeId: string;
	kpiMetricId: string;
	measurementDate: string; // ISO date string
	actualValue: number;
	targetValue?: number;
	notes?: string;
}

// Bonus system types
export interface BonusTier {
	min: number;
	max: number;
	amount: number;
}

export interface BonusRule {
	id: string;
	organizationId: string;
	ruleName: string;
	calculationType: BonusCalculationType;
	kpiMetricId?: string;
	metricName?: string;
	percentageRate?: number;
	fixedAmount?: number;
	tierStructure?: BonusTier[];
	minimumAchievementPercentage: number;
	isActive: boolean;
	effectiveFrom: Date;
	effectiveUntil?: Date;
	createdAt: Date;
	updatedAt: Date;
}

export interface EmployeeBonus {
	id: string;
	organizationId: string;
	employeeId: string;
	employeeName?: string;
	bonusRuleId: string;
	bonusRuleName?: string;
	calculationPeriodStart: Date;
	calculationPeriodEnd: Date;
	kpiAchievementPercentage?: number;
	bonusAmount: number;
	isPaid: boolean;
	paidAt?: Date;
	paidWithPaymentId?: string;
	calculatedBy: string;
	calculatedByName?: string;
	notes?: string;
	createdAt: Date;
}

export interface BonusRuleFormData {
	ruleName: string;
	calculationType: BonusCalculationType;
	kpiMetricId?: string;
	percentageRate?: number;
	fixedAmount?: number;
	tierStructure?: BonusTier[];
	minimumAchievementPercentage?: number;
	effectiveFrom: string; // ISO date string
	effectiveUntil?: string; // ISO date string
}

export interface BonusCalculationFormData {
	employeeId: string;
	bonusRuleId: string;
	calculationPeriodStart: string; // ISO date string
	calculationPeriodEnd: string; // ISO date string
	notes?: string;
}

export interface ShiftSummary {
	totalHours: number;
	totalPay: number;
	paidAmount: number;
	unpaidAmount: number;
	advanceDeductions: number;
}

export interface EmployeeWeeklySummary extends ShiftSummary {
	employeeId: string;
	employeeName: string;
	shiftsCount: number;
}

export interface VenueSummary extends ShiftSummary {
	venueId: string;
	venueName: string;
	shiftsCount: number;
}

export interface WeeklySummary extends ShiftSummary {
	employeeSummaries: EmployeeWeeklySummary[];
	venueSummaries: VenueSummary[];
	unassignedShifts: number;
}

// UI State types
export interface DragState {
	isDragging: boolean;
	draggedShift: Shift | null;
	draggedEmployee: Employee | null;
	dropTarget: {
		employeeId: string;
		date: Date;
	} | null;
}

export interface DragCreateState {
	isActive: boolean;
	mode: 'empty-cell-horizontal' | 'empty-cell-vertical' | 'duplicate-shift-horizontal' | 'duplicate-shift-vertical' | 'unassigned-horizontal' | 'unassigned-vertical' | null;
	direction: 'horizontal' | 'vertical' | null;
	sourceEmployee: Employee | null;
	sourceDate: Date | null;
	sourceShift: Shift | null; // For duplicate mode
	targetDates: Date[]; // For horizontal drag
	targetEmployees: Employee[]; // For vertical drag
	previewCells: { employeeId: string | null; date: Date }[]; // Allow null for unassigned shifts
}

export interface ModalState {
	isOpen: boolean;
	mode: 'create' | 'edit';
	shift: Shift | null;
	employee: Employee | null;
}

export interface UIState {
	selectedWeek: Date;
	dragState: DragState;
	dragCreateState: DragCreateState;
	shiftModal: ModalState;
	employeeModal: ModalState;
	leaveModal: ModalState;
	unavailabilityModal: ModalState;
	reassignmentPanel: ReassignmentPanelState;
	isEmployeePanelCollapsed: boolean;
	isMobileView: boolean;
	showLocationManagement: boolean;
	showPaymentManagement: boolean;
	showLeaveManagement: boolean;
	showUnavailabilityOverlay: boolean;
}

// Form types
export interface ShiftFormData {
	employeeId: string | null;
	venueId: string;
	date: string; // ISO date string
	startTime: string;
	endTime: string;
	totalHours?: number;
	dailyRate: number;
	isPaid: boolean;
	advanceDeduction: number;
	notes?: string;
}

export interface EmployeeFormData {
	name: string;
	defaultDailyRate: number;
	role: string;
	isActive: boolean;
}

// API Response types
export interface ApiResponse<T> {
	success: boolean;
	data?: T;
	error?: string;
	message?: string;
}

// Export types
export interface ExportOptions {
	weekStartDate: Date;
	venueId?: string;
	format: 'csv' | 'pdf';
	includeUnpaid: boolean;
	includePaid: boolean;
}

// Notification types
export interface Notification {
	id: string;
	type: 'info' | 'warning' | 'error' | 'success';
	title: string;
	message: string;
	duration?: number; // Auto-dismiss after milliseconds
	actions?: NotificationAction[];
}

export interface NotificationAction {
	label: string;
	action: () => void;
	style?: 'primary' | 'secondary';
}

// Duplicate Week types
export interface DuplicateWeekRequest {
	sourceWeekStart: Date;
	targetWeekStarts: Date[];
	conflictResolution: 'merge' | 'replace';
	organizationId?: string; // Made optional to handle cases where it's determined from auth context
}

export interface DuplicateWeekResult {
	success: boolean;
	totalShiftsDuplicated: number;
	conflictsDetected: number;
	conflictsResolved: number;
	targetWeeksProcessed: number;
	message: string;
	details: DuplicateWeekDetails[];
}

export interface DuplicateWeekDetails {
	targetWeekStart: string;
	shiftsDuplicated: number;
	conflictsFound: ShiftConflictInfo[];
	conflictsResolved: number;
}

export interface ShiftConflictInfo {
	employeeId: string;
	employeeName: string;
	date: string;
	existingShift: {
		id: string;
		startTime: string;
		endTime: string;
		venueId: string;
		venueName: string;
	};
	duplicateShift: {
		startTime: string;
		endTime: string;
		venueId: string;
		venueName: string;
	};
	resolution: 'skipped' | 'replaced' | 'merged';
}

// Color coding for shifts
export enum ShiftStatus {
	UNASSIGNED = 'unassigned', // Grey
	SCHEDULED = 'scheduled',   // Blue
	PAID = 'paid'             // Green
}

// Days of the week
export enum DayOfWeek {
	MONDAY = 0,
	TUESDAY = 1,
	WEDNESDAY = 2,
	THURSDAY = 3,
	FRIDAY = 4,
	SATURDAY = 5,
	SUNDAY = 6
}

// Time utilities
export interface TimeSlot {
	start: string;
	end: string;
	duration: number; // in hours
}

// Validation types
export interface ValidationError {
	field: string;
	message: string;
}

export interface ValidationResult {
	isValid: boolean;
	errors: ValidationError[];
}

// ============================================================================
// ENHANCED SCHEDULING TYPES
// ============================================================================

// Shift reassignment panel state
export interface ReassignmentPanelState {
	isOpen: boolean;
	affectedShifts: Shift[];
	leaveRequest?: LeaveRequest;
	suggestedReplacements: EmployeeReplacement[];
}

// Employee replacement suggestion
export interface EmployeeReplacement {
	employee: Employee;
	availabilityScore: number; // 0-100
	performanceScore?: number; // Based on KPIs
	tipEarnings?: number; // Recent tip pool earnings
	conflictReason?: string; // If not fully available
	isRecommended: boolean;
}

// Employee availability status for a specific date/time
export interface EmployeeAvailabilityStatus {
	employeeId: string;
	date: Date;
	isAvailable: boolean;
	unavailabilityReasons: UnavailabilityReason[];
	leaveRequests: LeaveRequest[];
	recurringPatterns: EmployeeUnavailability[];
}

// Reason for unavailability
export interface UnavailabilityReason {
	type: 'leave' | 'recurring' | 'one_time';
	reason: string;
	timeRange?: {
		start: string;
		end: string;
	};
	status?: LeaveRequestStatus; // For leave requests
}

// Enhanced weekly schedule with leave integration
export interface EnhancedWeeklySchedule extends WeeklySchedule {
	leaveRequests: LeaveRequest[];
	employeeUnavailability: EmployeeUnavailability[];
	employeeAvailability: Map<string, EmployeeAvailabilityStatus[]>; // employeeId -> availability by date
}

// Shift conflict information
export interface ShiftConflict {
	shiftId: string;
	employeeId: string;
	conflictType: 'leave_request' | 'unavailability' | 'double_booking';
	conflictDetails: string;
	severity: 'warning' | 'error';
	suggestedActions: string[];
}

// Leave management filter options
export interface LeaveManagementFilters {
	status?: LeaveRequestStatus[];
	requestType?: LeaveRequestType[];
	employeeId?: string;
	dateRange?: {
		start: Date;
		end: Date;
	};
}

// Performance metrics for scheduling decisions
export interface EmployeePerformanceMetrics {
	employeeId: string;
	kpiAchievementAverage: number;
	recentTipEarnings: number;
	attendanceRate: number;
	punctualityScore: number;
	lastUpdated: Date;
}

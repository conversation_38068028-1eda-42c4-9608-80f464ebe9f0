/**
 * Authentication Service
 * Handles all authentication-related RPC calls and business logic
 * Following Code Complete principles: Single responsibility, clear error handling
 */

import { supabase } from '$lib/supabaseClient.js';
import type { Restaurant, UserRestaurant } from '$lib/types.js';

// ============================================================================
// RESTAURANT MANAGEMENT
// ============================================================================

/**
 * Create a new restaurant for the current user
 */
export async function createRestaurant(name: string): Promise<Restaurant> {
	try {
		const { data, error } = await supabase.rpc('create_restaurant', {
			p_name: name
		});

		if (error) {
			throw new Error(`Failed to create restaurant: ${error.message}`);
		}

		if (!data || data.length === 0) {
			throw new Error('No data returned from create_restaurant');
		}

		return {
			id: data[0].id,
			name: data[0].name,
			owner_id: data[0].owner_id,
			created_at: data[0].created_at,
			updated_at: data[0].updated_at
		};
	} catch (error) {
		console.error('❌ Failed to create restaurant:', error);
		throw error;
	}
}

/**
 * Get restaurants for current user
 */
export async function getUserRestaurants(): Promise<UserRestaurant[]> {
	try {
		const { data, error } = await supabase.rpc('get_user_restaurants');

		if (error) {
			throw new Error(`Failed to get user restaurants: ${error.message}`);
		}

		return (data || []).map((item: any) => ({
			id: item.id,
			user_id: item.user_id,
			restaurant_id: item.restaurant_id,
			role: item.role,
			created_at: item.created_at
		}));
	} catch (error) {
		console.error('❌ Failed to get user restaurants:', error);
		throw error;
	}
}

/**
 * Get restaurant details by ID
 */
export async function getRestaurant(restaurantId: string): Promise<Restaurant | null> {
	try {
		const { data, error } = await supabase.rpc('get_restaurant', {
			p_restaurant_id: restaurantId
		});

		if (error) {
			throw new Error(`Failed to get restaurant: ${error.message}`);
		}

		if (!data || data.length === 0) {
			return null;
		}

		return {
			id: data[0].id,
			name: data[0].name,
			owner_id: data[0].owner_id,
			created_at: data[0].created_at,
			updated_at: data[0].updated_at
		};
	} catch (error) {
		console.error('❌ Failed to get restaurant:', error);
		throw error;
	}
}

/**
 * Update restaurant details
 */
export async function updateRestaurant(restaurantId: string, name: string): Promise<Restaurant> {
	try {
		const { data, error } = await supabase.rpc('update_restaurant', {
			p_restaurant_id: restaurantId,
			p_name: name
		});

		if (error) {
			throw new Error(`Failed to update restaurant: ${error.message}`);
		}

		if (!data || data.length === 0) {
			throw new Error('No data returned from update_restaurant');
		}

		return {
			id: data[0].id,
			name: data[0].name,
			owner_id: data[0].owner_id,
			created_at: data[0].created_at,
			updated_at: data[0].updated_at
		};
	} catch (error) {
		console.error('❌ Failed to update restaurant:', error);
		throw error;
	}
}

/**
 * Delete restaurant (owner only)
 */
export async function deleteRestaurant(restaurantId: string): Promise<void> {
	try {
		const { error } = await supabase.rpc('delete_restaurant', {
			p_restaurant_id: restaurantId
		});

		if (error) {
			throw new Error(`Failed to delete restaurant: ${error.message}`);
		}
	} catch (error) {
		console.error('❌ Failed to delete restaurant:', error);
		throw error;
	}
}

// ============================================================================
// USER MANAGEMENT
// ============================================================================

/**
 * Invite user to restaurant
 */
export async function inviteUserToRestaurant(
	email: string, 
	restaurantId: string, 
	role: 'manager' | 'staff'
): Promise<UserRestaurant> {
	try {
		const { data, error } = await supabase.rpc('invite_user_to_restaurant', {
			p_email: email,
			p_restaurant_id: restaurantId,
			p_role: role
		});

		if (error) {
			throw new Error(`Failed to invite user: ${error.message}`);
		}

		if (!data || data.length === 0) {
			throw new Error('No data returned from invite_user_to_restaurant');
		}

		return {
			id: data[0].id,
			user_id: data[0].user_id,
			restaurant_id: data[0].restaurant_id,
			role: data[0].role,
			created_at: data[0].created_at
		};
	} catch (error) {
		console.error('❌ Failed to invite user to restaurant:', error);
		throw error;
	}
}

/**
 * Remove user from restaurant
 */
export async function removeUserFromRestaurant(
	userId: string, 
	restaurantId: string
): Promise<void> {
	try {
		const { error } = await supabase.rpc('remove_user_from_restaurant', {
			p_user_id: userId,
			p_restaurant_id: restaurantId
		});

		if (error) {
			throw new Error(`Failed to remove user from restaurant: ${error.message}`);
		}
	} catch (error) {
		console.error('❌ Failed to remove user from restaurant:', error);
		throw error;
	}
}

/**
 * Update user role in restaurant
 */
export async function updateUserRole(
	userId: string, 
	restaurantId: string, 
	role: 'manager' | 'staff'
): Promise<UserRestaurant> {
	try {
		const { data, error } = await supabase.rpc('update_user_role', {
			p_user_id: userId,
			p_restaurant_id: restaurantId,
			p_role: role
		});

		if (error) {
			throw new Error(`Failed to update user role: ${error.message}`);
		}

		if (!data || data.length === 0) {
			throw new Error('No data returned from update_user_role');
		}

		return {
			id: data[0].id,
			user_id: data[0].user_id,
			restaurant_id: data[0].restaurant_id,
			role: data[0].role,
			created_at: data[0].created_at
		};
	} catch (error) {
		console.error('❌ Failed to update user role:', error);
		throw error;
	}
}

// ============================================================================
// PROFILE MANAGEMENT
// ============================================================================

/**
 * Update user profile
 */
export async function updateProfile(email: string): Promise<void> {
	try {
		const { error } = await supabase.auth.updateUser({
			email
		});

		if (error) {
			throw new Error(`Failed to update profile: ${error.message}`);
		}
	} catch (error) {
		console.error('❌ Failed to update profile:', error);
		throw error;
	}
}

/**
 * Change user password
 */
export async function changePassword(newPassword: string): Promise<void> {
	try {
		const { error } = await supabase.auth.updateUser({
			password: newPassword
		});

		if (error) {
			throw new Error(`Failed to change password: ${error.message}`);
		}
	} catch (error) {
		console.error('❌ Failed to change password:', error);
		throw error;
	}
}

/**
 * Send password reset email
 */
export async function resetPassword(email: string): Promise<void> {
	try {
		const { error } = await supabase.auth.resetPasswordForEmail(email);

		if (error) {
			throw new Error(`Failed to send reset email: ${error.message}`);
		}
	} catch (error) {
		console.error('❌ Failed to send reset email:', error);
		throw error;
	}
}

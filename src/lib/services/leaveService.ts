/**
 * Leave/Unavailability Service
 * Following Code Complete principles: Clear naming, error handling, single responsibility
 * Uses RPC calls exclusively as per requirements
 */

import { supabase } from '$lib/supabaseClient.js';
import type {
	LeaveRequest,
	LeaveRequestFormData,
	LeaveRequestStatus,
	EmployeeUnavailability,
	RecurringUnavailabilityFormData,
	OneTimeUnavailabilityFormData,
	UnavailabilityType,
	UnavailabilityDayOfWeek
} from '$lib/types.js';

/**
 * Database leave request type for RPC responses
 * Following Code Complete: Clear type definitions
 */
interface DbLeaveRequest {
	id: string;
	organization_id: string;
	employee_id: string;
	employee_name?: string;
	request_type: string;
	start_date: string;
	end_date: string;
	status: string;
	requested_by: string;
	requested_by_name?: string;
	approved_by?: string;
	approved_by_name?: string;
	approved_at?: string;
	reason?: string;
	manager_notes?: string;
	created_at: string;
	updated_at: string;
}

/**
 * Database unavailability type for RPC responses
 * Following Code Complete: Clear type definitions
 */
interface DbEmployeeUnavailability {
	id: string;
	organization_id: string;
	employee_id: string;
	employee_name?: string;
	unavailability_type: string;
	day_of_week?: string;
	start_time?: string;
	end_time?: string;
	specific_date?: string;
	specific_start_time?: string;
	specific_end_time?: string;
	reason?: string;
	is_active: boolean;
	created_at: string;
	updated_at: string;
}

/**
 * Transform database leave request to application leave request
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbLeaveRequest(dbLeaveRequest: DbLeaveRequest): LeaveRequest {
	return {
		id: dbLeaveRequest.id,
		organizationId: dbLeaveRequest.organization_id,
		employeeId: dbLeaveRequest.employee_id,
		employeeName: dbLeaveRequest.employee_name,
		requestType: dbLeaveRequest.request_type as any,
		startDate: new Date(dbLeaveRequest.start_date),
		endDate: new Date(dbLeaveRequest.end_date),
		status: dbLeaveRequest.status as LeaveRequestStatus,
		requestedBy: dbLeaveRequest.requested_by,
		requestedByName: dbLeaveRequest.requested_by_name,
		approvedBy: dbLeaveRequest.approved_by,
		approvedByName: dbLeaveRequest.approved_by_name,
		approvedAt: dbLeaveRequest.approved_at ? new Date(dbLeaveRequest.approved_at) : undefined,
		reason: dbLeaveRequest.reason,
		managerNotes: dbLeaveRequest.manager_notes,
		createdAt: new Date(dbLeaveRequest.created_at),
		updatedAt: new Date(dbLeaveRequest.updated_at)
	};
}

/**
 * Transform database unavailability to application unavailability
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbUnavailability(dbUnavailability: DbEmployeeUnavailability): EmployeeUnavailability {
	return {
		id: dbUnavailability.id,
		organizationId: dbUnavailability.organization_id,
		employeeId: dbUnavailability.employee_id,
		employeeName: dbUnavailability.employee_name,
		unavailabilityType: dbUnavailability.unavailability_type as UnavailabilityType,
		dayOfWeek: dbUnavailability.day_of_week as UnavailabilityDayOfWeek,
		startTime: dbUnavailability.start_time,
		endTime: dbUnavailability.end_time,
		specificDate: dbUnavailability.specific_date ? new Date(dbUnavailability.specific_date) : undefined,
		specificStartTime: dbUnavailability.specific_start_time,
		specificEndTime: dbUnavailability.specific_end_time,
		reason: dbUnavailability.reason,
		isActive: dbUnavailability.is_active,
		createdAt: new Date(dbUnavailability.created_at),
		updatedAt: new Date(dbUnavailability.updated_at)
	};
}

// ============================================================================
// LEAVE REQUEST FUNCTIONS
// ============================================================================

/**
 * Fetch leave requests using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchLeaveRequests(
	organizationId?: string,
	employeeId?: string,
	status?: LeaveRequestStatus,
	startDate?: Date,
	endDate?: Date
): Promise<LeaveRequest[]> {
	console.log('🔄 leaveService.fetchLeaveRequests: Starting fetch...', { organizationId, employeeId, status });

	const { data, error } = await supabase.rpc('get_leave_requests', {
		p_organization_id: organizationId || null,
		p_employee_id: employeeId || null,
		p_status: status || null,
		p_start_date: startDate?.toISOString().split('T')[0] || null,
		p_end_date: endDate?.toISOString().split('T')[0] || null
	});

	if (error) {
		console.error('❌ leaveService.fetchLeaveRequests: RPC error:', error);
		throw new Error(`Failed to fetch leave requests: ${error.message}`);
	}

	console.log('✅ leaveService.fetchLeaveRequests: Raw data from RPC:', data);

	const leaveRequests = (data ?? []).map(transformDbLeaveRequest);
	console.log('✅ leaveService.fetchLeaveRequests: Transformed leave requests:', leaveRequests);

	return leaveRequests;
}

/**
 * Create new leave request using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createLeaveRequest(
	organizationId: string,
	leaveRequestData: LeaveRequestFormData
): Promise<LeaveRequest> {
	console.log('🔄 leaveService.createLeaveRequest: Creating leave request:', leaveRequestData);

	const { data, error } = await supabase.rpc('create_leave_request', {
		p_organization_id: organizationId,
		p_employee_id: leaveRequestData.employeeId,
		p_request_type: leaveRequestData.requestType,
		p_start_date: leaveRequestData.startDate,
		p_end_date: leaveRequestData.endDate,
		p_reason: leaveRequestData.reason?.trim() || null
	});

	if (error) {
		console.error('❌ leaveService.createLeaveRequest: RPC error:', error);
		throw new Error(`Failed to create leave request: ${error.message}`);
	}

	if (!data || data.length === 0) {
		throw new Error('No data returned from create leave request operation');
	}

	console.log('✅ leaveService.createLeaveRequest: Created leave request:', data[0]);
	return transformDbLeaveRequest(data[0]);
}

/**
 * Update leave request status using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function updateLeaveRequestStatus(
	requestId: string,
	status: 'approved' | 'denied',
	managerNotes?: string
): Promise<Partial<LeaveRequest>> {
	console.log('🔄 leaveService.updateLeaveRequestStatus: Updating status:', { requestId, status, managerNotes });

	const { data, error } = await supabase.rpc('update_leave_request_status', {
		p_request_id: requestId,
		p_status: status,
		p_manager_notes: managerNotes?.trim() || null
	});

	if (error) {
		console.error('❌ leaveService.updateLeaveRequestStatus: RPC error:', error);
		throw new Error(`Failed to update leave request status: ${error.message}`);
	}

	if (!data || data.length === 0) {
		throw new Error('No data returned from update leave request status operation');
	}

	console.log('✅ leaveService.updateLeaveRequestStatus: Updated leave request:', data[0]);
	return {
		id: data[0].id,
		status: data[0].status,
		approvedBy: data[0].approved_by,
		approvedAt: data[0].approved_at ? new Date(data[0].approved_at) : undefined,
		managerNotes: data[0].manager_notes
	};
}

/**
 * Cancel leave request using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function cancelLeaveRequest(requestId: string): Promise<boolean> {
	console.log('🔄 leaveService.cancelLeaveRequest: Cancelling request:', requestId);

	const { data, error } = await supabase.rpc('cancel_leave_request', {
		p_request_id: requestId
	});

	if (error) {
		console.error('❌ leaveService.cancelLeaveRequest: RPC error:', error);
		throw new Error(`Failed to cancel leave request: ${error.message}`);
	}

	console.log('✅ leaveService.cancelLeaveRequest: Cancelled:', data);
	return data === true;
}

// ============================================================================
// UNAVAILABILITY FUNCTIONS
// ============================================================================

/**
 * Fetch employee unavailability patterns using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchEmployeeUnavailability(
	organizationId?: string,
	employeeId?: string,
	unavailabilityType?: UnavailabilityType
): Promise<EmployeeUnavailability[]> {
	console.log('🔄 leaveService.fetchEmployeeUnavailability: Starting fetch...', { organizationId, employeeId, unavailabilityType });

	const { data, error } = await supabase.rpc('get_employee_unavailability', {
		p_organization_id: organizationId || null,
		p_employee_id: employeeId || null,
		p_unavailability_type: unavailabilityType || null
	});

	if (error) {
		console.error('❌ leaveService.fetchEmployeeUnavailability: RPC error:', error);
		throw new Error(`Failed to fetch employee unavailability: ${error.message}`);
	}

	console.log('✅ leaveService.fetchEmployeeUnavailability: Raw data from RPC:', data);

	const unavailability = (data ?? []).map(transformDbUnavailability);
	console.log('✅ leaveService.fetchEmployeeUnavailability: Transformed unavailability:', unavailability);

	return unavailability;
}

/**
 * Create recurring unavailability using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createRecurringUnavailability(
	organizationId: string,
	unavailabilityData: RecurringUnavailabilityFormData
): Promise<string> {
	console.log('🔄 leaveService.createRecurringUnavailability: Creating recurring unavailability:', unavailabilityData);

	const { data, error } = await supabase.rpc('create_recurring_unavailability', {
		p_organization_id: organizationId,
		p_employee_id: unavailabilityData.employeeId,
		p_day_of_week: unavailabilityData.dayOfWeek,
		p_start_time: unavailabilityData.startTime,
		p_end_time: unavailabilityData.endTime,
		p_reason: unavailabilityData.reason?.trim() || null
	});

	if (error) {
		console.error('❌ leaveService.createRecurringUnavailability: RPC error:', error);
		throw new Error(`Failed to create recurring unavailability: ${error.message}`);
	}

	console.log('✅ leaveService.createRecurringUnavailability: Created unavailability ID:', data);
	return data;
}

/**
 * Create one-time unavailability using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createOneTimeUnavailability(
	organizationId: string,
	unavailabilityData: OneTimeUnavailabilityFormData
): Promise<string> {
	console.log('🔄 leaveService.createOneTimeUnavailability: Creating one-time unavailability:', unavailabilityData);

	const { data, error } = await supabase.rpc('create_onetime_unavailability', {
		p_organization_id: organizationId,
		p_employee_id: unavailabilityData.employeeId,
		p_specific_date: unavailabilityData.specificDate,
		p_specific_start_time: unavailabilityData.specificStartTime || null,
		p_specific_end_time: unavailabilityData.specificEndTime || null,
		p_reason: unavailabilityData.reason?.trim() || null
	});

	if (error) {
		console.error('❌ leaveService.createOneTimeUnavailability: RPC error:', error);
		throw new Error(`Failed to create one-time unavailability: ${error.message}`);
	}

	console.log('✅ leaveService.createOneTimeUnavailability: Created unavailability ID:', data);
	return data;
}

/**
 * Delete unavailability pattern using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function deleteUnavailability(unavailabilityId: string): Promise<boolean> {
	console.log('🔄 leaveService.deleteUnavailability: Deleting unavailability:', unavailabilityId);

	const { data, error } = await supabase.rpc('delete_unavailability', {
		p_unavailability_id: unavailabilityId
	});

	if (error) {
		console.error('❌ leaveService.deleteUnavailability: RPC error:', error);
		throw new Error(`Failed to delete unavailability: ${error.message}`);
	}

	console.log('✅ leaveService.deleteUnavailability: Deleted:', data);
	return data === true;
}

/**
 * Check if employee is available for shift using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function checkEmployeeAvailability(
	employeeId: string,
	shiftDate: Date,
	startTime: string,
	endTime: string
): Promise<boolean> {
	console.log('🔄 leaveService.checkEmployeeAvailability: Checking availability:', { employeeId, shiftDate, startTime, endTime });

	const { data, error } = await supabase.rpc('check_employee_availability', {
		p_employee_id: employeeId,
		p_shift_date: shiftDate.toISOString().split('T')[0],
		p_start_time: startTime,
		p_end_time: endTime
	});

	if (error) {
		console.error('❌ leaveService.checkEmployeeAvailability: RPC error:', error);
		throw new Error(`Failed to check employee availability: ${error.message}`);
	}

	console.log('✅ leaveService.checkEmployeeAvailability: Available:', data);
	return data === true;
}

// Export service object
export const leaveService = {
	// Leave requests
	fetchLeaveRequests,
	createLeaveRequest,
	updateLeaveRequestStatus,
	cancelLeaveRequest,
	
	// Unavailability
	fetchEmployeeUnavailability,
	createRecurringUnavailability,
	createOneTimeUnavailability,
	deleteUnavailability,
	checkEmployeeAvailability
};

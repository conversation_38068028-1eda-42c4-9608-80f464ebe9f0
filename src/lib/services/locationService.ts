/**
 * Location Service
 * Manages location data operations using Supabase RPC functions
 * Following Code Complete principles: Clear naming, error handling, single responsibility
 * Uses RPC calls exclusively as per requirements
 */

import { supabase } from '$lib/supabaseClient.js';
import type { Location } from '$lib/types.js';

/**
 * Transform database location to application location
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbLocation(dbLocation: any): Location {
	return {
		id: dbLocation.id,
		organizationId: dbLocation.organization_id,
		name: dbLocation.name,
		addressLine1: dbLocation.address_line1,
		addressLine2: dbLocation.address_line2,
		city: dbLocation.city,
		postalCode: dbLocation.postal_code,
		country: dbLocation.country,
		contactName: dbLocation.contact_name,
		contactEmail: dbLocation.contact_email,
		contactPhone: dbLocation.contact_phone,
		isActive: dbLocation.is_active,
		sortOrder: dbLocation.sort_order,
		barsy: dbLocation.barsy,
		createdAt: new Date(dbLocation.created_at),
		updatedAt: new Date(dbLocation.updated_at)
	};
}

/**
 * Fetch all active locations using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchAllLocations(): Promise<Location[]> {
	console.log('🔄 locationService.fetchAllLocations: Starting fetch...');

	try {
		const { data, error } = await supabase.rpc('fetch_all_locations');

		if (error) {
			console.error('❌ locationService.fetchAllLocations: RPC error:', error);
			throw new Error(`Failed to fetch locations: ${error.message}`);
		}

		console.log('✅ locationService.fetchAllLocations: Raw data from RPC:', data);

		const locations = (data ?? []).map(transformDbLocation);
		console.log('✅ locationService.fetchAllLocations: Transformed locations:', locations);

		return locations;
	} catch (err) {
		console.error('❌ locationService.fetchAllLocations: Unexpected error:', err);
		throw err;
	}
}

/**
 * Create new location using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createLocation(locationData: {
	name: string;
	addressLine1?: string;
	addressLine2?: string;
	city?: string;
	postalCode?: string;
	country?: string;
	contactName?: string;
	contactEmail?: string;
	contactPhone?: string;
	sortOrder?: number;
}): Promise<Location> {
	console.log('🔄 locationService.createLocation: Creating location:', locationData);

	const { data, error } = await supabase.rpc('create_location', {
		p_name: locationData.name.trim(),
		p_address_line1: locationData.addressLine1?.trim() || null,
		p_address_line2: locationData.addressLine2?.trim() || null,
		p_city: locationData.city?.trim() || null,
		p_postal_code: locationData.postalCode?.trim() || null,
		p_country: locationData.country?.trim() || null,
		p_contact_name: locationData.contactName?.trim() || null,
		p_contact_email: locationData.contactEmail?.trim() || null,
		p_contact_phone: locationData.contactPhone?.trim() || null,
		p_sort_order: locationData.sortOrder || 0
	});

	if (error) {
		console.error('❌ locationService.createLocation: RPC error:', error);
		throw new Error(`Failed to create location: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from create location operation');
	}

	console.log('✅ locationService.createLocation: Location created:', data);
	return transformDbLocation(data);
}

/**
 * Update existing location using RPC
 * Following Code Complete: Consistent interface with create
 */
async function updateLocation(locationId: string, locationData: {
	name: string;
	addressLine1?: string;
	addressLine2?: string;
	city?: string;
	postalCode?: string;
	country?: string;
	contactName?: string;
	contactEmail?: string;
	contactPhone?: string;
	sortOrder?: number;
	isActive?: boolean;
}): Promise<Location> {
	console.log('🔄 locationService.updateLocation: Updating location:', locationId, locationData);

	const { data, error } = await supabase.rpc('update_location', {
		p_location_id: locationId,
		p_name: locationData.name.trim(),
		p_address_line1: locationData.addressLine1?.trim() || null,
		p_address_line2: locationData.addressLine2?.trim() || null,
		p_city: locationData.city?.trim() || null,
		p_postal_code: locationData.postalCode?.trim() || null,
		p_country: locationData.country?.trim() || null,
		p_contact_name: locationData.contactName?.trim() || null,
		p_contact_email: locationData.contactEmail?.trim() || null,
		p_contact_phone: locationData.contactPhone?.trim() || null,
		p_sort_order: locationData.sortOrder || 0,
		p_is_active: locationData.isActive !== undefined ? locationData.isActive : true
	});

	if (error) {
		console.error('❌ locationService.updateLocation: RPC error:', error);
		throw new Error(`Failed to update location: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from update location operation');
	}

	console.log('✅ locationService.updateLocation: Location updated:', data);
	return transformDbLocation(data);
}

/**
 * Location service object
 * Following Code Complete: Consistent interface, clear organization
 */
export const locationService = {
	fetchAllLocations,
	createLocation,
	updateLocation
} as const;

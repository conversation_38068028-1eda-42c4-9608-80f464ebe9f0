/**
 * KPI & Bonus Service
 * Following Code Complete principles: Clear naming, error handling, single responsibility
 * Uses RPC calls exclusively as per requirements
 */

import { supabase } from '$lib/supabaseClient.js';
import type { 
	KpiMetric, 
	KpiMetricFormData,
	EmployeeKpiRecord,
	KpiRecordFormData,
	BonusRule,
	BonusRuleFormData,
	EmployeeBonus,
	BonusCalculationFormData,
	KpiMetricType,
	BonusCalculationType
} from '$lib/types.js';

/**
 * Database KPI metric type for RPC responses
 * Following Code Complete: Clear type definitions
 */
interface DbKpiMetric {
	id: string;
	organization_id: string;
	metric_name: string;
	metric_type: string;
	target_value?: number;
	measurement_unit?: string;
	is_active: boolean;
	created_at: string;
	updated_at: string;
}

/**
 * Database employee KPI record type for RPC responses
 * Following Code Complete: Clear type definitions
 */
interface DbEmployeeKpiRecord {
	id: string;
	organization_id: string;
	employee_id: string;
	employee_name?: string;
	kpi_metric_id: string;
	metric_name?: string;
	measurement_date: string;
	actual_value: number;
	target_value?: number;
	achievement_percentage?: number;
	notes?: string;
	recorded_by: string;
	recorded_by_name?: string;
	created_at: string;
}

/**
 * Database bonus rule type for RPC responses
 * Following Code Complete: Clear type definitions
 */
interface DbBonusRule {
	id: string;
	organization_id: string;
	rule_name: string;
	calculation_type: string;
	kpi_metric_id?: string;
	metric_name?: string;
	percentage_rate?: number;
	fixed_amount?: number;
	tier_structure?: any;
	minimum_achievement_percentage: number;
	is_active: boolean;
	effective_from: string;
	effective_until?: string;
	created_at: string;
	updated_at: string;
}

/**
 * Database employee bonus type for RPC responses
 * Following Code Complete: Clear type definitions
 */
interface DbEmployeeBonus {
	id: string;
	organization_id: string;
	employee_id: string;
	employee_name?: string;
	bonus_rule_name?: string;
	calculation_period_start: string;
	calculation_period_end: string;
	kpi_achievement_percentage?: number;
	bonus_amount: number;
	is_paid: boolean;
	paid_at?: string;
	calculated_by: string;
	calculated_by_name?: string;
	notes?: string;
	created_at: string;
}

/**
 * Transform database KPI metric to application KPI metric
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbKpiMetric(dbKpiMetric: DbKpiMetric): KpiMetric {
	return {
		id: dbKpiMetric.id,
		organizationId: dbKpiMetric.organization_id,
		metricName: dbKpiMetric.metric_name,
		metricType: dbKpiMetric.metric_type as KpiMetricType,
		targetValue: dbKpiMetric.target_value,
		measurementUnit: dbKpiMetric.measurement_unit,
		isActive: dbKpiMetric.is_active,
		createdAt: new Date(dbKpiMetric.created_at),
		updatedAt: new Date(dbKpiMetric.updated_at)
	};
}

/**
 * Transform database employee KPI record to application KPI record
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbEmployeeKpiRecord(dbKpiRecord: DbEmployeeKpiRecord): EmployeeKpiRecord {
	return {
		id: dbKpiRecord.id,
		organizationId: dbKpiRecord.organization_id,
		employeeId: dbKpiRecord.employee_id,
		employeeName: dbKpiRecord.employee_name,
		kpiMetricId: dbKpiRecord.kpi_metric_id,
		metricName: dbKpiRecord.metric_name,
		measurementDate: new Date(dbKpiRecord.measurement_date),
		actualValue: dbKpiRecord.actual_value,
		targetValue: dbKpiRecord.target_value,
		achievementPercentage: dbKpiRecord.achievement_percentage,
		notes: dbKpiRecord.notes,
		recordedBy: dbKpiRecord.recorded_by,
		recordedByName: dbKpiRecord.recorded_by_name,
		createdAt: new Date(dbKpiRecord.created_at)
	};
}

/**
 * Transform database bonus rule to application bonus rule
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbBonusRule(dbBonusRule: DbBonusRule): BonusRule {
	return {
		id: dbBonusRule.id,
		organizationId: dbBonusRule.organization_id,
		ruleName: dbBonusRule.rule_name,
		calculationType: dbBonusRule.calculation_type as BonusCalculationType,
		kpiMetricId: dbBonusRule.kpi_metric_id,
		metricName: dbBonusRule.metric_name,
		percentageRate: dbBonusRule.percentage_rate,
		fixedAmount: dbBonusRule.fixed_amount,
		tierStructure: dbBonusRule.tier_structure,
		minimumAchievementPercentage: dbBonusRule.minimum_achievement_percentage,
		isActive: dbBonusRule.is_active,
		effectiveFrom: new Date(dbBonusRule.effective_from),
		effectiveUntil: dbBonusRule.effective_until ? new Date(dbBonusRule.effective_until) : undefined,
		createdAt: new Date(dbBonusRule.created_at),
		updatedAt: new Date(dbBonusRule.updated_at)
	};
}

/**
 * Transform database employee bonus to application employee bonus
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbEmployeeBonus(dbEmployeeBonus: DbEmployeeBonus): EmployeeBonus {
	return {
		id: dbEmployeeBonus.id,
		organizationId: dbEmployeeBonus.organization_id,
		employeeId: dbEmployeeBonus.employee_id,
		employeeName: dbEmployeeBonus.employee_name,
		bonusRuleId: '', // Not returned by RPC, would need separate query
		bonusRuleName: dbEmployeeBonus.bonus_rule_name,
		calculationPeriodStart: new Date(dbEmployeeBonus.calculation_period_start),
		calculationPeriodEnd: new Date(dbEmployeeBonus.calculation_period_end),
		kpiAchievementPercentage: dbEmployeeBonus.kpi_achievement_percentage,
		bonusAmount: dbEmployeeBonus.bonus_amount,
		isPaid: dbEmployeeBonus.is_paid,
		paidAt: dbEmployeeBonus.paid_at ? new Date(dbEmployeeBonus.paid_at) : undefined,
		calculatedBy: dbEmployeeBonus.calculated_by,
		calculatedByName: dbEmployeeBonus.calculated_by_name,
		notes: dbEmployeeBonus.notes,
		createdAt: new Date(dbEmployeeBonus.created_at)
	};
}

// ============================================================================
// KPI METRIC FUNCTIONS
// ============================================================================

/**
 * Fetch KPI metrics using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchKpiMetrics(organizationId: string): Promise<KpiMetric[]> {
	console.log('🔄 kpiService.fetchKpiMetrics: Starting fetch for organization:', organizationId);

	const { data, error } = await supabase.rpc('get_kpi_metrics', {
		p_organization_id: organizationId
	});

	if (error) {
		console.error('❌ kpiService.fetchKpiMetrics: RPC error:', error);
		throw new Error(`Failed to fetch KPI metrics: ${error.message}`);
	}

	console.log('✅ kpiService.fetchKpiMetrics: Raw data from RPC:', data);

	const kpiMetrics = (data ?? []).map(transformDbKpiMetric);
	console.log('✅ kpiService.fetchKpiMetrics: Transformed KPI metrics:', kpiMetrics);

	return kpiMetrics;
}

/**
 * Create new KPI metric using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createKpiMetric(
	organizationId: string,
	kpiMetricData: KpiMetricFormData
): Promise<string> {
	console.log('🔄 kpiService.createKpiMetric: Creating KPI metric:', kpiMetricData);

	const { data, error } = await supabase.rpc('create_kpi_metric', {
		p_organization_id: organizationId,
		p_metric_name: kpiMetricData.metricName.trim(),
		p_metric_type: kpiMetricData.metricType,
		p_target_value: kpiMetricData.targetValue || null,
		p_measurement_unit: kpiMetricData.measurementUnit?.trim() || null
	});

	if (error) {
		console.error('❌ kpiService.createKpiMetric: RPC error:', error);
		throw new Error(`Failed to create KPI metric: ${error.message}`);
	}

	console.log('✅ kpiService.createKpiMetric: Created KPI metric ID:', data);
	return data;
}

// ============================================================================
// EMPLOYEE KPI RECORD FUNCTIONS
// ============================================================================

/**
 * Fetch employee KPI records using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchEmployeeKpiRecords(
	organizationId?: string,
	employeeId?: string,
	kpiMetricId?: string,
	startDate?: Date,
	endDate?: Date
): Promise<EmployeeKpiRecord[]> {
	console.log('🔄 kpiService.fetchEmployeeKpiRecords: Starting fetch...', { organizationId, employeeId, kpiMetricId });

	const { data, error } = await supabase.rpc('get_employee_kpi_records', {
		p_organization_id: organizationId || null,
		p_employee_id: employeeId || null,
		p_kpi_metric_id: kpiMetricId || null,
		p_start_date: startDate?.toISOString().split('T')[0] || null,
		p_end_date: endDate?.toISOString().split('T')[0] || null
	});

	if (error) {
		console.error('❌ kpiService.fetchEmployeeKpiRecords: RPC error:', error);
		throw new Error(`Failed to fetch employee KPI records: ${error.message}`);
	}

	console.log('✅ kpiService.fetchEmployeeKpiRecords: Raw data from RPC:', data);

	const kpiRecords = (data ?? []).map(transformDbEmployeeKpiRecord);
	console.log('✅ kpiService.fetchEmployeeKpiRecords: Transformed KPI records:', kpiRecords);

	return kpiRecords;
}

/**
 * Record employee KPI measurement using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function recordEmployeeKpi(
	organizationId: string,
	kpiRecordData: KpiRecordFormData
): Promise<string> {
	console.log('🔄 kpiService.recordEmployeeKpi: Recording KPI:', kpiRecordData);

	const { data, error } = await supabase.rpc('record_employee_kpi', {
		p_organization_id: organizationId,
		p_employee_id: kpiRecordData.employeeId,
		p_kpi_metric_id: kpiRecordData.kpiMetricId,
		p_measurement_date: kpiRecordData.measurementDate,
		p_actual_value: kpiRecordData.actualValue,
		p_target_value: kpiRecordData.targetValue || null,
		p_notes: kpiRecordData.notes?.trim() || null
	});

	if (error) {
		console.error('❌ kpiService.recordEmployeeKpi: RPC error:', error);
		throw new Error(`Failed to record employee KPI: ${error.message}`);
	}

	console.log('✅ kpiService.recordEmployeeKpi: Recorded KPI record ID:', data);
	return data;
}

// ============================================================================
// BONUS RULE FUNCTIONS
// ============================================================================

/**
 * Fetch bonus rules using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchBonusRules(organizationId: string): Promise<BonusRule[]> {
	console.log('🔄 kpiService.fetchBonusRules: Starting fetch for organization:', organizationId);

	const { data, error } = await supabase.rpc('get_bonus_rules', {
		p_organization_id: organizationId
	});

	if (error) {
		console.error('❌ kpiService.fetchBonusRules: RPC error:', error);
		throw new Error(`Failed to fetch bonus rules: ${error.message}`);
	}

	console.log('✅ kpiService.fetchBonusRules: Raw data from RPC:', data);

	const bonusRules = (data ?? []).map(transformDbBonusRule);
	console.log('✅ kpiService.fetchBonusRules: Transformed bonus rules:', bonusRules);

	return bonusRules;
}

/**
 * Create new bonus rule using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createBonusRule(
	organizationId: string,
	bonusRuleData: BonusRuleFormData
): Promise<string> {
	console.log('🔄 kpiService.createBonusRule: Creating bonus rule:', bonusRuleData);

	const { data, error } = await supabase.rpc('create_bonus_rule', {
		p_organization_id: organizationId,
		p_rule_name: bonusRuleData.ruleName.trim(),
		p_calculation_type: bonusRuleData.calculationType,
		p_kpi_metric_id: bonusRuleData.kpiMetricId || null,
		p_percentage_rate: bonusRuleData.percentageRate || null,
		p_fixed_amount: bonusRuleData.fixedAmount || null,
		p_tier_structure: bonusRuleData.tierStructure ? JSON.stringify(bonusRuleData.tierStructure) : null,
		p_minimum_achievement_percentage: bonusRuleData.minimumAchievementPercentage || 0.00,
		p_effective_from: bonusRuleData.effectiveFrom,
		p_effective_until: bonusRuleData.effectiveUntil || null
	});

	if (error) {
		console.error('❌ kpiService.createBonusRule: RPC error:', error);
		throw new Error(`Failed to create bonus rule: ${error.message}`);
	}

	console.log('✅ kpiService.createBonusRule: Created bonus rule ID:', data);
	return data;
}

// ============================================================================
// EMPLOYEE BONUS FUNCTIONS
// ============================================================================

/**
 * Calculate employee bonus using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function calculateEmployeeBonus(
	organizationId: string,
	bonusCalculationData: BonusCalculationFormData
): Promise<EmployeeBonus> {
	console.log('🔄 kpiService.calculateEmployeeBonus: Calculating bonus:', bonusCalculationData);

	const { data, error } = await supabase.rpc('calculate_employee_bonus', {
		p_organization_id: organizationId,
		p_employee_id: bonusCalculationData.employeeId,
		p_bonus_rule_id: bonusCalculationData.bonusRuleId,
		p_calculation_period_start: bonusCalculationData.calculationPeriodStart,
		p_calculation_period_end: bonusCalculationData.calculationPeriodEnd,
		p_notes: bonusCalculationData.notes?.trim() || null
	});

	if (error) {
		console.error('❌ kpiService.calculateEmployeeBonus: RPC error:', error);
		throw new Error(`Failed to calculate employee bonus: ${error.message}`);
	}

	if (!data || data.length === 0) {
		throw new Error('No data returned from calculate employee bonus operation');
	}

	console.log('✅ kpiService.calculateEmployeeBonus: Calculated bonus:', data[0]);

	// Transform the returned bonus calculation result
	return {
		id: data[0].bonus_id,
		organizationId: organizationId,
		employeeId: data[0].employee_id,
		employeeName: data[0].employee_name,
		bonusRuleId: bonusCalculationData.bonusRuleId,
		bonusRuleName: data[0].bonus_rule_name,
		calculationPeriodStart: new Date(bonusCalculationData.calculationPeriodStart),
		calculationPeriodEnd: new Date(bonusCalculationData.calculationPeriodEnd),
		kpiAchievementPercentage: data[0].kpi_achievement_percentage,
		bonusAmount: data[0].bonus_amount,
		isPaid: false,
		calculatedBy: '', // Will be set by RPC
		notes: bonusCalculationData.notes,
		createdAt: new Date()
	};
}

/**
 * Fetch employee bonuses using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchEmployeeBonuses(
	organizationId?: string,
	employeeId?: string,
	startDate?: Date,
	endDate?: Date,
	isPaid?: boolean
): Promise<EmployeeBonus[]> {
	console.log('🔄 kpiService.fetchEmployeeBonuses: Starting fetch...', { organizationId, employeeId, isPaid });

	const { data, error } = await supabase.rpc('get_employee_bonuses', {
		p_organization_id: organizationId || null,
		p_employee_id: employeeId || null,
		p_start_date: startDate?.toISOString().split('T')[0] || null,
		p_end_date: endDate?.toISOString().split('T')[0] || null,
		p_is_paid: isPaid
	});

	if (error) {
		console.error('❌ kpiService.fetchEmployeeBonuses: RPC error:', error);
		throw new Error(`Failed to fetch employee bonuses: ${error.message}`);
	}

	console.log('✅ kpiService.fetchEmployeeBonuses: Raw data from RPC:', data);

	const employeeBonuses = (data ?? []).map(transformDbEmployeeBonus);
	console.log('✅ kpiService.fetchEmployeeBonuses: Transformed employee bonuses:', employeeBonuses);

	return employeeBonuses;
}

// Export service object
export const kpiService = {
	// KPI Metrics
	fetchKpiMetrics,
	createKpiMetric,
	
	// Employee KPI Records
	fetchEmployeeKpiRecords,
	recordEmployeeKpi,
	
	// Bonus Rules
	fetchBonusRules,
	createBonusRule,
	
	// Employee Bonuses
	calculateEmployeeBonus,
	fetchEmployeeBonuses
};

/**
 * Service Layer Index
 * Following Code Complete principles: Clear module organization, single responsibility
 * All services use RPC calls to Supabase as per requirements
 */

export { employeeService } from './employeeService.js';
export { venueService } from './venueService.js';
export { shiftService } from './shiftService.js';
export { scheduleService } from './scheduleService.js';
export { leaveService } from './leaveService.js';
export { tipService } from './tipService.js';
export { kpiService } from './kpiService.js';
export { enhancedScheduleService } from './enhancedScheduleService.js';

// Re-export types for convenience
export type {
	Employee,
	Venue,
	Shift,
	WeeklySchedule,
	EmployeeFormData,
	ShiftFormData,
	// Leave/Unavailability types
	LeaveRequest,
	LeaveRequestFormData,
	EmployeeUnavailability,
	RecurringUnavailabilityFormData,
	OneTimeUnavailabilityFormData,
	// Tip pooling types
	TipPool,
	TipPoolFormData,
	TipDistribution,
	TipDistributionInput,
	// KPI & Bonus types
	KpiMetric,
	KpiMetricFormData,
	EmployeeKpiRecord,
	KpiRecordFormData,
	BonusRule,
	BonusRuleFormData,
	EmployeeBonus,
	BonusCalculationFormData
} from '../types.js';

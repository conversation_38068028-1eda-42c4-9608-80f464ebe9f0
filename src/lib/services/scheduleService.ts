/**
 * Schedule Service
 * Following Code Complete principles: Orchestration layer, clear responsibilities
 * Uses RPC calls exclusively as per requirements
 */

import { supabase } from '$lib/supabaseClient.js';
import { employeeService } from './employeeService.js';
import { venueService } from './venueService.js';
import { shiftService } from './shiftService.js';
import type { Employee, Venue, Shift, WeeklySummary } from '$lib/types.js';
import { calculateWeeklySummary } from '$lib/utils.js';

/**
 * Fetch all employees using RPC
 * Following Code Complete: Clear delegation to specialized service
 */
async function fetchEmployees(): Promise<Employee[]> {
	return employeeService.fetchActiveEmployees();
}

/**
 * Fetch all venues using RPC
 * Following Code Complete: Clear delegation to specialized service
 */
async function fetchVenues(): Promise<Venue[]> {
	return venueService.fetchAllVenues();
}

/**
 * Fetch shifts for a specific week using RPC
 * Following Code Complete: Clear delegation to specialized service
 */
async function fetchShiftsForWeek(weekStartDate: Date): Promise<Shift[]> {
	console.log('🔄 scheduleService.fetchShiftsForWeek: Delegating to shiftService for week:', weekStartDate);
	const shifts = await shiftService.fetchShiftsForWeek(weekStartDate);
	console.log('✅ scheduleService.fetchShiftsForWeek: Received shifts from shiftService:', shifts.length, 'shifts');
	return shifts;
}

/**
 * Fetch complete schedule data for a week using RPC
 * Following Code Complete: Orchestration function, clear purpose
 */
async function fetchWeeklyScheduleData(weekStartDate: Date): Promise<{
	employees: Employee[];
	venues: Venue[];
	shifts: Shift[];
}> {
	// Use Promise.all for concurrent execution - Code Complete principle: Performance optimization
	const [employees, venues, shifts] = await Promise.all([
		fetchEmployees(),
		fetchVenues(),
		fetchShiftsForWeek(weekStartDate)
	]);

	return { employees, venues, shifts };
}

/**
 * Fetch complete schedule data using optimized combined RPC call
 * Following Code Complete: Performance optimization, single database round trip
 */
async function fetchWeeklyScheduleDataOptimized(weekStartDate: Date): Promise<{
	employees: Employee[];
	venues: Venue[];
	shifts: Shift[];
	metadata?: any;
}> {
	console.log('🔄 scheduleService.fetchWeeklyScheduleDataOptimized: Starting optimized fetch for week:', weekStartDate);

	try {
		const { data, error } = await supabase.rpc('get_weekly_schedule_data_optimized', {
			p_week_start: weekStartDate.toISOString().split('T')[0]
		});

		if (error) {
			console.error('❌ scheduleService.fetchWeeklyScheduleDataOptimized: RPC error:', error);
			throw new Error(`Failed to fetch weekly schedule data: ${error.message}`);
		}

		if (!data || !data.success) {
			const errorMsg = data?.error || 'Unknown error occurred';
			console.error('❌ scheduleService.fetchWeeklyScheduleDataOptimized: Function returned error:', errorMsg);
			throw new Error(`Failed to fetch weekly schedule data: ${errorMsg}`);
		}

		// Transform the data using existing transformation functions
		const employees = (data.data.employees || []).map((emp: any) =>
			employeeService.transformDbEmployee ? employeeService.transformDbEmployee(emp) : emp
		);

		const venues = (data.data.venues || []).map((venue: any) =>
			venueService.transformDbVenue ? venueService.transformDbVenue(venue) : venue
		);

		const shifts = (data.data.shifts || []).map((shift: any) =>
			shiftService.transformDbShift ? shiftService.transformDbShift(shift) : shift
		);

		console.log('✅ scheduleService.fetchWeeklyScheduleDataOptimized: Data loaded successfully:', {
			employees: employees.length,
			venues: venues.length,
			shifts: shifts.length,
			metadata: data.metadata
		});

		return {
			employees,
			venues,
			shifts,
			metadata: data.metadata
		};

	} catch (error) {
		console.error('❌ scheduleService.fetchWeeklyScheduleDataOptimized: Unexpected error:', error);
		throw error;
	}
}

/**
 * Calculate weekly summary with all statistics
 * Following Code Complete: Pure function, clear calculation
 */
async function calculateWeeklyScheduleSummary(
	employees: Employee[],
	venues: Venue[],
	shifts: Shift[]
): Promise<WeeklySummary> {
	// Delegate to utility function for calculation logic
	return calculateWeeklySummary(employees, shifts, venues);
}

/**
 * Mark all unpaid shifts as paid for a week using RPC
 * Following Code Complete: Batch operation, clear purpose
 */
async function markAllShiftsAsPaidForWeek(weekStartDate: Date): Promise<Shift[]> {
	// First get all unpaid shifts for the week
	const shifts = await fetchShiftsForWeek(weekStartDate);
	const unpaidShiftIds = shifts
		.filter(shift => !shift.isPaid)
		.map(shift => shift.id);

	if (unpaidShiftIds.length === 0) {
		return shifts; // No unpaid shifts to update
	}

	// Mark all unpaid shifts as paid
	return shiftService.markMultipleShiftsAsPaid(unpaidShiftIds);
}

/**
 * Export schedule data for a week using RPC
 * Following Code Complete: Clear data transformation for export
 */
async function exportWeeklyScheduleData(
	weekStartDate: Date,
	venueId?: string
): Promise<{
	employees: Employee[];
	venues: Venue[];
	shifts: Shift[];
	summary: WeeklySummary;
}> {
	const { employees, venues, shifts } = await fetchWeeklyScheduleData(weekStartDate);

	// Filter shifts by venue if specified
	const filteredShifts = venueId
		? shifts.filter(shift => shift.venueId === venueId)
		: shifts;

	// Filter venues if specific venue requested
	const filteredVenues = venueId
		? venues.filter(venue => venue.id === venueId)
		: venues;

	// Calculate summary for filtered data
	const summary = await calculateWeeklyScheduleSummary(employees, filteredVenues, filteredShifts);

	return {
		employees,
		venues: filteredVenues,
		shifts: filteredShifts,
		summary
	};
}

/**
 * Get schedule statistics for dashboard using RPC
 * Following Code Complete: Specific purpose, clear data aggregation
 */
async function getScheduleStatistics(startDate: Date, endDate: Date): Promise<{
	totalEmployees: number;
	activeEmployees: number;
	totalVenues: number;
	totalShifts: number;
	paidShifts: number;
	unpaidShifts: number;
	unassignedShifts: number;
	totalHours: number;
	totalPay: number;
	paidAmount: number;
	unpaidAmount: number;
}> {
	const [employees, venues] = await Promise.all([
		employeeService.fetchAllEmployees(),
		venueService.fetchAllVenues()
	]);

	// Get shifts for the date range
	const shifts: Shift[] = []; // TODO: Implement date range shift fetching

	const paidShifts = shifts.filter(s => s.isPaid);
	const unpaidShifts = shifts.filter(s => !s.isPaid);
	const unassignedShifts = shifts.filter(s => !s.employeeId);

	const totalHours = shifts.reduce((sum, shift) => sum + (shift.totalHours || 0), 0);
	const totalPay = shifts.reduce((sum, shift) => sum + (shift.dailyRate * (shift.totalHours || 0)), 0);
	const paidAmount = paidShifts.reduce((sum, shift) => sum + (shift.dailyRate * (shift.totalHours || 0)), 0);
	const unpaidAmount = totalPay - paidAmount;

	return {
		totalEmployees: employees.length,
		activeEmployees: employees.filter(e => e.isActive).length,
		totalVenues: venues.length,
		totalShifts: shifts.length,
		paidShifts: paidShifts.length,
		unpaidShifts: unpaidShifts.length,
		unassignedShifts: unassignedShifts.length,
		totalHours,
		totalPay,
		paidAmount,
		unpaidAmount
	};
}

/**
 * Schedule service object
 * Following Code Complete: Consistent interface, clear organization
 */
export const scheduleService = {
	// Data fetching
	fetchEmployees,
	fetchVenues,
	fetchShiftsForWeek,
	fetchWeeklyScheduleData,
	fetchWeeklyScheduleDataOptimized, // New optimized single-call function

	// Calculations
	calculateWeeklyScheduleSummary,

	// Bulk operations
	markAllShiftsAsPaidForWeek,

	// Export operations
	exportWeeklyScheduleData,

	// Statistics
	getScheduleStatistics,

	// Re-export individual services for direct access
	employee: employeeService,
	venue: venueService,
	shift: shiftService
} as const;

/**
 * Payment Service
 * Manages employee payment operations using Supabase RPC functions
 * Following Code Complete principles: Clear naming, error handling, single responsibility
 * Uses RPC calls exclusively as per requirements
 */

import { supabase } from '$lib/supabaseClient.js';
import type { EmployeePayment, EmployeePaymentLog } from '$lib/types.js';

/**
 * Transform database payment to application payment
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbPayment(dbPayment: any): EmployeePayment {
	return {
		id: dbPayment.id,
		employeeId: dbPayment.employee_id,
		weekStartDate: new Date(dbPayment.week_start_date),
		totalDue: dbPayment.total_due,
		totalPaid: dbPayment.total_paid,
		paidAt: dbPayment.paid_at ? new Date(dbPayment.paid_at) : undefined,
		paymentMethod: dbPayment.payment_method,
		notes: dbPayment.notes,
		createdAt: new Date(dbPayment.created_at),
		updatedAt: new Date(dbPayment.updated_at)
	};
}

/**
 * Transform database payment log to application payment log
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbPaymentLog(dbLog: any): EmployeePaymentLog {
	return {
		id: dbLog.id,
		employeePaymentId: dbLog.employee_payment_id,
		userId: dbLog.user_id,
		action: dbLog.action,
		amount: dbLog.amount,
		notes: dbLog.notes,
		createdAt: new Date(dbLog.created_at)
	};
}

/**
 * Calculate weekly payment for employee
 * Following Code Complete: Clear function purpose, error handling
 */
async function calculateWeeklyPayment(employeeId: string, weekStartDate: Date): Promise<{
	employeeId: string;
	weekStartDate: Date;
	totalHours: number;
	totalDue: number;
	unpaidShiftsCount: number;
}> {
	console.log('🔄 paymentService.calculateWeeklyPayment: Calculating for employee:', employeeId, 'week:', weekStartDate);

	const { data, error } = await supabase.rpc('calculate_weekly_payment', {
		p_employee_id: employeeId,
		p_week_start_date: weekStartDate.toISOString().split('T')[0]
	});

	if (error) {
		console.error('❌ paymentService.calculateWeeklyPayment: RPC error:', error);
		throw new Error(`Failed to calculate weekly payment: ${error.message}`);
	}

	if (!data || data.length === 0) {
		return {
			employeeId,
			weekStartDate,
			totalHours: 0,
			totalDue: 0,
			unpaidShiftsCount: 0
		};
	}

	const result = data[0];
	console.log('✅ paymentService.calculateWeeklyPayment: Calculation result:', result);

	return {
		employeeId: result.employee_id,
		weekStartDate: new Date(result.week_start_date),
		totalHours: result.total_hours,
		totalDue: result.total_due,
		unpaidShiftsCount: result.unpaid_shifts_count
	};
}

/**
 * Create or update employee payment record
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createOrUpdateEmployeePayment(
	employeeId: string,
	weekStartDate: Date,
	paymentMethod?: 'CASH' | 'TRANSFER' | 'OTHER',
	notes?: string
): Promise<EmployeePayment> {
	console.log('🔄 paymentService.createOrUpdateEmployeePayment: Creating/updating payment for:', employeeId, weekStartDate);

	const { data, error } = await supabase.rpc('create_or_update_employee_payment', {
		p_employee_id: employeeId,
		p_week_start_date: weekStartDate.toISOString().split('T')[0],
		p_payment_method: paymentMethod || null,
		p_notes: notes?.trim() || null
	});

	if (error) {
		console.error('❌ paymentService.createOrUpdateEmployeePayment: RPC error:', error);
		throw new Error(`Failed to create/update employee payment: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from create/update employee payment operation');
	}

	console.log('✅ paymentService.createOrUpdateEmployeePayment: Payment created/updated:', data);
	return transformDbPayment(data);
}

/**
 * Mark payment as paid
 * Following Code Complete: Clear operation, proper error handling
 */
async function markPaymentAsPaid(
	paymentId: string,
	amountPaid: number,
	paymentMethod: 'CASH' | 'TRANSFER' | 'OTHER',
	notes?: string
): Promise<EmployeePayment> {
	console.log('🔄 paymentService.markPaymentAsPaid: Marking payment as paid:', paymentId, amountPaid);

	const { data, error } = await supabase.rpc('mark_payment_as_paid', {
		p_payment_id: paymentId,
		p_amount_paid: amountPaid,
		p_payment_method: paymentMethod,
		p_notes: notes?.trim() || null
	});

	if (error) {
		console.error('❌ paymentService.markPaymentAsPaid: RPC error:', error);
		throw new Error(`Failed to mark payment as paid: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from mark payment as paid operation');
	}

	console.log('✅ paymentService.markPaymentAsPaid: Payment marked as paid:', data);
	return transformDbPayment(data);
}

/**
 * Get payment history for employee
 * Following Code Complete: Clear function purpose, consistent interface
 */
async function getEmployeePaymentHistory(employeeId: string, limit: number = 10): Promise<EmployeePayment[]> {
	console.log('🔄 paymentService.getEmployeePaymentHistory: Fetching history for:', employeeId);

	const { data, error } = await supabase.rpc('get_employee_payment_history', {
		p_employee_id: employeeId,
		p_limit: limit
	});

	if (error) {
		console.error('❌ paymentService.getEmployeePaymentHistory: RPC error:', error);
		throw new Error(`Failed to get employee payment history: ${error.message}`);
	}

	console.log('✅ paymentService.getEmployeePaymentHistory: Payment history:', data);
	return (data ?? []).map(transformDbPayment);
}

/**
 * Get unpaid shifts summary for all employees
 * Following Code Complete: Clear function purpose, error handling
 */
async function getUnpaidShiftsSummary(): Promise<Array<{
	employeeId: string;
	employeeName: string;
	totalUnpaidHours: number;
	totalUnpaidAmount: number;
	oldestUnpaidDate: Date;
	unpaidShiftsCount: number;
}>> {
	console.log('🔄 paymentService.getUnpaidShiftsSummary: Fetching unpaid shifts summary...');

	const { data, error } = await supabase.rpc('get_unpaid_shifts_summary');

	if (error) {
		console.error('❌ paymentService.getUnpaidShiftsSummary: RPC error:', error);
		throw new Error(`Failed to get unpaid shifts summary: ${error.message}`);
	}

	console.log('✅ paymentService.getUnpaidShiftsSummary: Unpaid shifts summary:', data);

	return (data ?? []).map((item: any) => ({
		employeeId: item.employee_id,
		employeeName: item.employee_name,
		totalUnpaidHours: item.total_unpaid_hours,
		totalUnpaidAmount: item.total_unpaid_amount,
		oldestUnpaidDate: new Date(item.oldest_unpaid_date),
		unpaidShiftsCount: item.unpaid_shifts_count
	}));
}

/**
 * Payment service object
 * Following Code Complete: Consistent interface, clear organization
 */
export const paymentService = {
	calculateWeeklyPayment,
	createOrUpdateEmployeePayment,
	markPaymentAsPaid,
	getEmployeePaymentHistory,
	getUnpaidShiftsSummary
} as const;

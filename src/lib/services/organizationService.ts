/**
 * Organization Service
 * Manages organization and membership operations using Supabase RPC functions
 * Following Code Complete principles: Clear naming, error handling, single responsibility
 * Uses RPC calls exclusively as per requirements
 */

import { supabase } from '$lib/supabaseClient.js';
import type { Organization, OrganizationMembership } from '$lib/types.js';

/**
 * Transform database organization to application organization
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbOrganization(dbOrg: any): Organization & { userRole: string; membershipActive: boolean } {
	return {
		id: dbOrg.id,
		name: dbOrg.name,
		ownerUserId: dbOrg.owner_user_id,
		plan: dbOrg.plan,
		logoUrl: dbOrg.logo_url,
		isActive: dbOrg.is_active,
		createdAt: new Date(dbOrg.created_at),
		updatedAt: new Date(dbOrg.updated_at),
		userRole: dbOrg.user_role || 'viewer',
		membershipActive: dbOrg.membership_active ?? true
	};
}

/**
 * Transform database membership to application membership
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbMembership(dbMembership: any): OrganizationMembership & { userEmail?: string } {
	return {
		id: dbMembership.id,
		userId: dbMembership.user_id,
		organizationId: dbMembership.organization_id,
		role: dbMembership.role,
		isActive: dbMembership.is_active,
		createdAt: new Date(dbMembership.created_at),
		userEmail: dbMembership.user_email
	};
}

/**
 * Get user's organizations
 * Following Code Complete: Clear function purpose, error handling
 */
async function getUserOrganizations(): Promise<Array<Organization & { userRole: string; membershipActive: boolean }>> {
	try {
		const { data, error } = await supabase.rpc('get_user_organizations');

		if (error) {
			console.error('❌ organizationService.getUserOrganizations: RPC error:', error);
			throw new Error(`Failed to fetch user organizations: ${error.message}`);
		}

		return (data ?? []).map(transformDbOrganization);
	} catch (err) {
		console.error('❌ organizationService.getUserOrganizations: Unexpected error:', err);
		throw err;
	}
}

/**
 * Create new organization
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createOrganization(name: string, plan: string = 'free'): Promise<Organization & { userRole: string; membershipActive: boolean }> {
	console.log('🔄 organizationService.createOrganization: Creating organization:', name);

	const { data, error } = await supabase.rpc('create_organization', {
		p_name: name.trim(),
		p_plan: plan
	});

	if (error) {
		console.error('❌ organizationService.createOrganization: RPC error:', error);
		throw new Error(`Failed to create organization: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from create organization operation');
	}

	console.log('✅ organizationService.createOrganization: Organization created:', data);
	return transformDbOrganization(data);
}

/**
 * Update organization
 * Following Code Complete: Consistent interface with create
 */
async function updateOrganization(
	organizationId: string,
	name: string,
	plan?: string,
	logoUrl?: string
): Promise<Organization> {
	console.log('🔄 organizationService.updateOrganization: Updating organization:', organizationId);

	const { data, error } = await supabase.rpc('update_organization', {
		p_organization_id: organizationId,
		p_name: name.trim(),
		p_plan: plan || null,
		p_logo_url: logoUrl || null
	});

	if (error) {
		console.error('❌ organizationService.updateOrganization: RPC error:', error);
		throw new Error(`Failed to update organization: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from update organization operation');
	}

	console.log('✅ organizationService.updateOrganization: Organization updated:', data);
	return transformDbOrganization(data);
}

/**
 * Invite user to organization
 * Following Code Complete: Clear operation, proper error handling
 */
async function inviteUserToOrganization(
	organizationId: string,
	userEmail: string,
	role: 'viewer' | 'editor' | 'admin' | 'superadmin' = 'viewer'
): Promise<OrganizationMembership> {
	console.log('🔄 organizationService.inviteUserToOrganization: Inviting user:', userEmail, 'to org:', organizationId);

	const { data, error } = await supabase.rpc('invite_user_to_organization', {
		p_organization_id: organizationId,
		p_user_email: userEmail.trim(),
		p_role: role
	});

	if (error) {
		console.error('❌ organizationService.inviteUserToOrganization: RPC error:', error);
		throw new Error(`Failed to invite user to organization: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from invite user operation');
	}

	console.log('✅ organizationService.inviteUserToOrganization: User invited:', data);
	return transformDbMembership(data);
}

/**
 * Get organization members
 * Following Code Complete: Clear function purpose, consistent interface
 */
async function getOrganizationMembers(organizationId: string): Promise<Array<OrganizationMembership & { userEmail: string }>> {
	console.log('🔄 organizationService.getOrganizationMembers: Fetching members for org:', organizationId);

	const { data, error } = await supabase.rpc('get_organization_members', {
		p_organization_id: organizationId
	});

	if (error) {
		console.error('❌ organizationService.getOrganizationMembers: RPC error:', error);
		throw new Error(`Failed to get organization members: ${error.message}`);
	}

	console.log('✅ organizationService.getOrganizationMembers: Members:', data);
	return (data ?? []).map(transformDbMembership);
}

/**
 * Switch organization context
 * Following Code Complete: Clear operation, proper error handling
 */
async function switchOrganizationContext(organizationId: string): Promise<boolean> {
	console.log('🔄 organizationService.switchOrganizationContext: Switching to org:', organizationId);

	const { data, error } = await supabase.rpc('switch_organization_context', {
		p_organization_id: organizationId
	});

	if (error) {
		console.error('❌ organizationService.switchOrganizationContext: RPC error:', error);
		throw new Error(`Failed to switch organization context: ${error.message}`);
	}

	console.log('✅ organizationService.switchOrganizationContext: Context switched:', data);
	return data === true;
}

/**
 * Organization service object
 * Following Code Complete: Consistent interface, clear organization
 */
export const organizationService = {
	getUserOrganizations,
	createOrganization,
	updateOrganization,
	inviteUserToOrganization,
	getOrganizationMembers,
	switchOrganizationContext
} as const;

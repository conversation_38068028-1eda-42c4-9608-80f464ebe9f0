/**
 * Employee Service
 * Following Code Complete principles: Clear naming, error handling, single responsibility
 * Uses RPC calls exclusively as per requirements
 */

import { supabase } from '$lib/supabaseClient.js';
import type { Employee, EmployeeFormData } from '$lib/types.js';
import { getAuthState } from '$lib/stores/auth.js';

/**
 * Database employee type for RPC responses
 * Following Code Complete: Clear type definitions
 * Updated to match current database schema with enum types
 */
interface DbEmployee {
	id: string;
	full_name: string;
	name: string;
	default_daily_rate: number;
	default_daily_working_hours: number;
	default_hourly_rate: number;
	role: 'FOH' | 'BOH' | 'DELIVERY' | 'OTHER'; // employee_role enum
	user_id: string | null;
	active: boolean;
	is_active: boolean;
	organization_id: string;
	restaurant_id: string;
	created_at: string;
	updated_at: string;
}

/**
 * Transform database employee to application employee
 * Following Code Complete: Pure function, clear transformation
 * Updated to handle new schema fields and enum types
 */
function transformDbEmployee(dbEmployee: any): Employee {
	// Convert enum role to display string
	const roleDisplayMap: Record<string, string> = {
		'FOH': 'Front of House',
		'BOH': 'Back of House',
		'DELIVERY': 'Delivery',
		'OTHER': 'Other'
	};

	return {
		id: dbEmployee.id,
		organizationId: dbEmployee.organization_id,
		fullName: dbEmployee.full_name || dbEmployee.name,
		name: dbEmployee.name || dbEmployee.full_name,
		role: roleDisplayMap[dbEmployee.role] || dbEmployee.role || 'Other',
		defaultDailyRate: dbEmployee.default_daily_rate || 0,
		defaultDailyWorkingHours: dbEmployee.default_daily_working_hours || 8,
		defaultHourlyRate: dbEmployee.default_hourly_rate || 0,
		userId: dbEmployee.user_id,
		active: dbEmployee.active ?? true,
		isActive: dbEmployee.is_active ?? dbEmployee.active ?? true,
		createdAt: new Date(dbEmployee.created_at),
		updatedAt: new Date(dbEmployee.updated_at || dbEmployee.created_at)
	};
}

/**
 * Fetch all active employees using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchActiveEmployees(): Promise<Employee[]> {
	try {
		const { data, error } = await supabase.rpc('fetch_active_employees');

		if (error) {
			console.error('❌ employeeService.fetchActiveEmployees: RPC error:', error);
			console.error('❌ Error details:', {
				message: error.message,
				details: error.details,
				hint: error.hint,
				code: error.code
			});
			throw new Error(`Failed to fetch active employees: ${error.message}`);
		}

		const employees = (data ?? []).map(transformDbEmployee);
		return employees;
	} catch (err) {
		console.error('❌ employeeService.fetchActiveEmployees: Unexpected error:', err);
		throw err;
	}
}

/**
 * Fetch all employees (active and inactive) using RPC
 * Following Code Complete: Consistent naming pattern
 */
async function fetchAllEmployees(): Promise<Employee[]> {
	const { data, error } = await supabase.rpc('get_all_employees');

	if (error) {
		throw new Error(`Failed to fetch all employees: ${error.message}`);
	}

	return (data ?? []).map(transformDbEmployee);
}

/**
 * Create new employee using RPC
 * Following Code Complete: Clear parameter validation, error handling
 * Updated to handle role mapping for enum types
 */
async function createEmployee(employeeData: EmployeeFormData): Promise<Employee> {
	console.log('🔄 EmployeeService: Creating employee with data:', employeeData);

	// Get current restaurant context (for backward compatibility)
	const authState = getAuthState();
	const restaurantId = authState.currentRestaurant?.id || null;

	console.log('🔍 EmployeeService: Using restaurant ID:', restaurantId);

	// Map display role to database enum value
	const roleEnumMap: Record<string, string> = {
		'Front of House': 'FOH',
		'Back of House': 'BOH',
		'Delivery': 'DELIVERY',
		'Other': 'OTHER',
		'Server': 'FOH',
		'Cook': 'BOH',
		'Kitchen': 'BOH',
		'FOH': 'FOH',
		'BOH': 'BOH',
		'DELIVERY': 'DELIVERY',
		'OTHER': 'OTHER'
	};

	const dbRole = roleEnumMap[employeeData.role.trim()] || 'OTHER';

	const { data, error } = await supabase.rpc('create_employee', {
		p_name: employeeData.name.trim(),
		p_role: dbRole,
		p_default_daily_rate: employeeData.defaultDailyRate,
		p_restaurant_id: restaurantId, // Can be null, function will use organization context
		p_is_active: employeeData.isActive ?? true
	});

	if (error) {
		console.error('❌ EmployeeService: Create employee error:', error);
		throw new Error(`Failed to create employee: ${error.message}`);
	}

	if (!data || data.length === 0) {
		console.error('❌ EmployeeService: No data returned from create_employee RPC');
		throw new Error('No data returned from create employee operation');
	}

	console.log('✅ EmployeeService: Employee created successfully:', data);
	// The RPC function returns an array, so take the first item
	return transformDbEmployee(Array.isArray(data) ? data[0] : data);
}

/**
 * Update existing employee using RPC
 * Following Code Complete: Consistent interface with create
 * Updated to handle role mapping for enum types
 */
async function updateEmployee(employeeId: string, employeeData: EmployeeFormData): Promise<Employee> {
	console.log('🔄 EmployeeService: Updating employee with data:', employeeData);

	// Map display role to database enum value (same as create)
	const roleEnumMap: Record<string, string> = {
		'Front of House': 'FOH',
		'Back of House': 'BOH',
		'Delivery': 'DELIVERY',
		'Other': 'OTHER',
		'Server': 'FOH',
		'Cook': 'BOH',
		'Kitchen': 'BOH',
		'FOH': 'FOH',
		'BOH': 'BOH',
		'DELIVERY': 'DELIVERY',
		'OTHER': 'OTHER'
	};

	const dbRole = roleEnumMap[employeeData.role.trim()] || 'OTHER';

	const { data, error } = await supabase.rpc('update_employee', {
		p_employee_id: employeeId,
		p_name: employeeData.name.trim(),
		p_role: dbRole,
		p_default_daily_rate: employeeData.defaultDailyRate,
		p_is_active: employeeData.isActive ?? true
	});

	if (error) {
		console.error('❌ EmployeeService: Update employee error:', error);
		throw new Error(`Failed to update employee: ${error.message}`);
	}

	if (!data || data.length === 0) {
		console.error('❌ EmployeeService: No data returned from update_employee RPC');
		throw new Error('No data returned from update employee operation');
	}

	console.log('✅ EmployeeService: Employee updated successfully:', data);
	// The RPC function returns an array, so take the first item
	return transformDbEmployee(Array.isArray(data) ? data[0] : data);
}

/**
 * Delete employee using RPC
 * Following Code Complete: Clear operation, proper error handling
 */
async function deleteEmployee(employeeId: string): Promise<void> {
	const { error } = await supabase.rpc('delete_employee', {
		p_employee_id: employeeId
	});

	if (error) {
		throw new Error(`Failed to delete employee: ${error.message}`);
	}
}

/**
 * Toggle employee active status using RPC
 * Following Code Complete: Specific operation, clear purpose
 */
async function toggleEmployeeStatus(employeeId: string, isActive: boolean): Promise<Employee> {
	const { data, error } = await supabase.rpc('toggle_employee_status', {
		p_employee_id: employeeId,
		p_is_active: isActive
	});

	if (error) {
		throw new Error(`Failed to toggle employee status: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from toggle employee status operation');
	}

	return transformDbEmployee(data);
}

/**
 * Bulk delete multiple employees using RPC
 * Following Code Complete: Clear operation, proper error handling
 * Enhanced with organization context and timeout protection
 */
async function bulkDeleteEmployees(employeeIds: string[]): Promise<{ deletedCount: number; message: string }> {
	if (!employeeIds || employeeIds.length === 0) {
		throw new Error('No employee IDs provided for bulk deletion');
	}

	console.log('🔄 EmployeeService: Bulk deleting employees:', employeeIds.length, 'employees');

	// Get current organization context
	const authState = getAuthState();
	const organizationId = authState.currentRestaurant?.id || '1101f4b7-7e64-45b9-b690-de9a451426a4';

	// Create a timeout promise to prevent infinite loading
	const timeoutPromise = new Promise<never>((_, reject) => {
		setTimeout(() => {
			reject(new Error('Bulk delete operation timed out after 30 seconds'));
		}, 30000); // 30 second timeout
	});

	// Race the RPC call against the timeout
	const rpcPromise = supabase.rpc('bulk_delete_employees', {
		p_employee_ids: employeeIds,
		p_organization_id: organizationId
	});

	const { data, error } = await Promise.race([rpcPromise, timeoutPromise]);

	if (error) {
		console.error('❌ EmployeeService: Bulk delete employees error:', error);
		throw new Error(`Failed to bulk delete employees: ${error.message}`);
	}

	console.log('✅ EmployeeService: Bulk delete completed:', {
		deleted: data?.deleted_count || 0,
		requested: employeeIds.length
	});

	return {
		deletedCount: data?.deleted_count || 0,
		message: data?.message || `Successfully deleted ${data?.deleted_count || 0} employees`
	};
}

/**
 * Employee service object
 * Following Code Complete: Consistent interface, clear organization
 */
export const employeeService = {
	fetchActiveEmployees,
	fetchAllEmployees,
	createEmployee,
	updateEmployee,
	deleteEmployee,
	toggleEmployeeStatus,
	bulkDeleteEmployees,
	transformDbEmployee // Export transformation function for reuse
} as const;
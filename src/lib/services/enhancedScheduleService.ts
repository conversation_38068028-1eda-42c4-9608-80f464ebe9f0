/**
 * Enhanced Schedule Service
 * Integrates leave management, unavailability tracking, and performance metrics
 * Following Code Complete principles: Clear naming, error handling, single responsibility
 */

import { supabase } from '$lib/supabaseClient.js';
import { leaveService } from './leaveService.js';
import { kpiService } from './kpiService.js';
import { tipService } from './tipService.js';
import { shiftService } from './shiftService.js';
import { employeeService } from './employeeService.js';
import type {
	EnhancedWeeklySchedule,
	EmployeeAvailabilityStatus,
	UnavailabilityReason,
	ShiftConflict,
	EmployeeReplacement,
	EmployeePerformanceMetrics,
	Shift,
	Employee,
	LeaveRequest,
	EmployeeUnavailability
} from '$lib/types.js';

/**
 * Load enhanced weekly schedule with leave and availability data
 * Following Code Complete: Clear function purpose, comprehensive data loading
 */
async function loadEnhancedWeeklySchedule(
	organizationId: string,
	weekStartDate: Date
): Promise<EnhancedWeeklySchedule> {
	console.log('🔄 enhancedScheduleService.loadEnhancedWeeklySchedule: Loading enhanced schedule...', {
		organizationId,
		weekStartDate
	});

	try {
		// Calculate week end date
		const weekEndDate = new Date(weekStartDate);
		weekEndDate.setDate(weekStartDate.getDate() + 6);

		// Load all data in parallel
		const [
			employees,
			locationsResponse,
			shifts,
			leaveRequests,
			employeeUnavailability
		] = await Promise.all([
			employeeService.fetchActiveEmployees(),
			// venueService.fetchAllVenues(), // Assuming this exists
			supabase.from('locations').select('*').eq('organization_id', organizationId),
			shiftService.fetchShiftsForWeek(weekStartDate),
			leaveService.fetchLeaveRequests(organizationId, undefined, undefined, weekStartDate, weekEndDate),
			leaveService.fetchEmployeeUnavailability(organizationId)
		]);

		// Process venues data
		const venues = locationsResponse?.data || [];

		// Build employee availability map
		const employeeAvailability = await buildEmployeeAvailabilityMap(
			employees,
			leaveRequests,
			employeeUnavailability,
			weekStartDate
		);

		const enhancedSchedule: EnhancedWeeklySchedule = {
			weekStartDate,
			employees,
			venues,
			shifts,
			leaveRequests,
			employeeUnavailability,
			employeeAvailability
		};

		console.log('✅ enhancedScheduleService.loadEnhancedWeeklySchedule: Enhanced schedule loaded', {
			employeeCount: employees.length,
			shiftCount: shifts.length,
			leaveRequestCount: leaveRequests.length,
			unavailabilityCount: employeeUnavailability.length
		});

		return enhancedSchedule;

	} catch (error) {
		console.error('❌ enhancedScheduleService.loadEnhancedWeeklySchedule: Failed to load enhanced schedule:', error);
		const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
		throw new Error(`Failed to load enhanced weekly schedule: ${errorMessage}`);
	}
}

/**
 * Build employee availability map for the week
 * Following Code Complete: Pure function, clear data transformation
 */
async function buildEmployeeAvailabilityMap(
	employees: Employee[],
	leaveRequests: LeaveRequest[],
	employeeUnavailability: EmployeeUnavailability[],
	weekStartDate: Date
): Promise<Map<string, EmployeeAvailabilityStatus[]>> {
	const availabilityMap = new Map<string, EmployeeAvailabilityStatus[]>();

	// Generate week dates
	const weekDates: Date[] = [];
	for (let i = 0; i < 7; i++) {
		const date = new Date(weekStartDate);
		date.setDate(weekStartDate.getDate() + i);
		weekDates.push(date);
	}

	// Process each employee
	for (const employee of employees) {
		const employeeAvailability: EmployeeAvailabilityStatus[] = [];

		for (const date of weekDates) {
			const availability = await calculateEmployeeAvailabilityForDate(
				employee.id,
				date,
				leaveRequests,
				employeeUnavailability
			);
			employeeAvailability.push(availability);
		}

		availabilityMap.set(employee.id, employeeAvailability);
	}

	return availabilityMap;
}

/**
 * Calculate employee availability for a specific date
 * Following Code Complete: Clear parameter validation, comprehensive checking
 */
async function calculateEmployeeAvailabilityForDate(
	employeeId: string,
	date: Date,
	leaveRequests: LeaveRequest[],
	employeeUnavailability: EmployeeUnavailability[]
): Promise<EmployeeAvailabilityStatus> {
	const unavailabilityReasons: UnavailabilityReason[] = [];
	let isAvailable = true;

	// Check for approved leave requests
	const relevantLeaveRequests = leaveRequests.filter(lr => 
		lr.employeeId === employeeId &&
		lr.status === 'approved' &&
		date >= lr.startDate &&
		date <= lr.endDate
	);

	if (relevantLeaveRequests.length > 0) {
		isAvailable = false;
		relevantLeaveRequests.forEach(lr => {
			unavailabilityReasons.push({
				type: 'leave',
				reason: `${lr.requestType} leave: ${lr.reason || 'No reason provided'}`,
				status: lr.status
			});
		});
	}

	// Check for recurring unavailability patterns
	const dayOfWeek = getDayOfWeekString(date);
	const recurringPatterns = employeeUnavailability.filter(eu =>
		eu.employeeId === employeeId &&
		eu.unavailabilityType === 'recurring' &&
		eu.dayOfWeek === dayOfWeek &&
		eu.isActive
	);

	recurringPatterns.forEach(pattern => {
		unavailabilityReasons.push({
			type: 'recurring',
			reason: pattern.reason || 'Recurring unavailability',
			timeRange: pattern.startTime && pattern.endTime ? {
				start: pattern.startTime,
				end: pattern.endTime
			} : undefined
		});

		// If no specific time range, employee is unavailable all day
		if (!pattern.startTime || !pattern.endTime) {
			isAvailable = false;
		}
	});

	// Check for one-time unavailability
	const oneTimePatterns = employeeUnavailability.filter(eu =>
		eu.employeeId === employeeId &&
		eu.unavailabilityType === 'one_time' &&
		eu.specificDate &&
		eu.specificDate.toDateString() === date.toDateString() &&
		eu.isActive
	);

	oneTimePatterns.forEach(pattern => {
		unavailabilityReasons.push({
			type: 'one_time',
			reason: pattern.reason || 'One-time unavailability',
			timeRange: pattern.specificStartTime && pattern.specificEndTime ? {
				start: pattern.specificStartTime,
				end: pattern.specificEndTime
			} : undefined
		});

		// If no specific time range, employee is unavailable all day
		if (!pattern.specificStartTime || !pattern.specificEndTime) {
			isAvailable = false;
		}
	});

	return {
		employeeId,
		date,
		isAvailable,
		unavailabilityReasons,
		leaveRequests: relevantLeaveRequests,
		recurringPatterns: recurringPatterns
	};
}

/**
 * Check if shift assignment would create conflicts
 * Following Code Complete: Clear validation logic, comprehensive conflict detection
 */
async function validateShiftAssignment(
	employeeId: string,
	shift: Shift,
	organizationId: string
): Promise<ShiftConflict[]> {
	console.log('🔄 enhancedScheduleService.validateShiftAssignment: Validating shift assignment...', {
		employeeId,
		shiftId: shift.id,
		date: shift.date,
		startTime: shift.startTime,
		endTime: shift.endTime
	});

	const conflicts: ShiftConflict[] = [];

	try {
		// Check availability using RPC function
		const isAvailable = await leaveService.checkEmployeeAvailability(
			employeeId,
			shift.date,
			shift.startTime,
			shift.endTime
		);

		if (!isAvailable) {
			// Get detailed unavailability information
			const [leaveRequests, unavailability] = await Promise.all([
				leaveService.fetchLeaveRequests(organizationId, employeeId, undefined, shift.date, shift.date),
				leaveService.fetchEmployeeUnavailability(organizationId, employeeId)
			]);

			// Identify specific conflicts
			const approvedLeave = leaveRequests.find(lr => 
				lr.status === 'approved' &&
				shift.date >= lr.startDate &&
				shift.date <= lr.endDate
			);

			if (approvedLeave) {
				conflicts.push({
					shiftId: shift.id,
					employeeId,
					conflictType: 'leave_request',
					conflictDetails: `Employee has approved ${approvedLeave.requestType} leave from ${approvedLeave.startDate.toLocaleDateString()} to ${approvedLeave.endDate.toLocaleDateString()}`,
					severity: 'error',
					suggestedActions: ['Assign to different employee', 'Request leave cancellation']
				});
			}

			// Check unavailability patterns
			const dayOfWeek = getDayOfWeekString(shift.date);
			const conflictingPattern = unavailability.find(ua =>
				ua.isActive &&
				((ua.unavailabilityType === 'recurring' && ua.dayOfWeek === dayOfWeek) ||
				 (ua.unavailabilityType === 'one_time' && ua.specificDate?.toDateString() === shift.date.toDateString()))
			);

			if (conflictingPattern) {
				conflicts.push({
					shiftId: shift.id,
					employeeId,
					conflictType: 'unavailability',
					conflictDetails: `Employee has ${conflictingPattern.unavailabilityType} unavailability: ${conflictingPattern.reason || 'No reason provided'}`,
					severity: 'warning',
					suggestedActions: ['Assign to different employee', 'Update unavailability pattern']
				});
			}
		}

		console.log('✅ enhancedScheduleService.validateShiftAssignment: Validation complete', {
			conflictCount: conflicts.length,
			conflicts
		});

		return conflicts;

	} catch (error) {
		console.error('❌ enhancedScheduleService.validateShiftAssignment: Validation failed:', error);
		const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
		throw new Error(`Failed to validate shift assignment: ${errorMessage}`);
	}
}

/**
 * Get suggested employee replacements for a shift
 * Following Code Complete: Clear algorithm, performance-based suggestions
 */
async function getSuggestedReplacements(
	shift: Shift,
	organizationId: string,
	excludeEmployeeId?: string
): Promise<EmployeeReplacement[]> {
	console.log('🔄 enhancedScheduleService.getSuggestedReplacements: Getting replacement suggestions...', {
		shiftId: shift.id,
		excludeEmployeeId
	});

	try {
		// Get all active employees
		const employees = await employeeService.fetchActiveEmployees();
		const availableEmployees = employees.filter(emp => emp.id !== excludeEmployeeId);

		const replacements: EmployeeReplacement[] = [];

		for (const employee of availableEmployees) {
			// Check availability
			const isAvailable = await leaveService.checkEmployeeAvailability(
				employee.id,
				shift.date,
				shift.startTime,
				shift.endTime
			);

			let availabilityScore = isAvailable ? 100 : 0;
			let conflictReason: string | undefined;

			if (!isAvailable) {
				// Get conflict details for partial availability scoring
				const conflicts = await validateShiftAssignment(employee.id, shift, organizationId);
				if (conflicts.length > 0) {
					conflictReason = conflicts[0].conflictDetails;
					// Reduce score based on conflict severity
					availabilityScore = conflicts[0].severity === 'warning' ? 50 : 0;
				}
			}

			// Get performance metrics (simplified for now)
			const performanceScore = await getEmployeePerformanceScore(employee.id, organizationId);

			replacements.push({
				employee,
				availabilityScore,
				performanceScore,
				conflictReason,
				isRecommended: availabilityScore >= 50 && (performanceScore || 0) >= 70
			});
		}

		// Sort by availability score, then performance score
		replacements.sort((a, b) => {
			if (a.availabilityScore !== b.availabilityScore) {
				return b.availabilityScore - a.availabilityScore;
			}
			return (b.performanceScore || 0) - (a.performanceScore || 0);
		});

		console.log('✅ enhancedScheduleService.getSuggestedReplacements: Suggestions generated', {
			totalSuggestions: replacements.length,
			recommendedCount: replacements.filter(r => r.isRecommended).length
		});

		return replacements;

	} catch (error) {
		console.error('❌ enhancedScheduleService.getSuggestedReplacements: Failed to get suggestions:', error);
		const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
		throw new Error(`Failed to get replacement suggestions: ${errorMessage}`);
	}
}

/**
 * Helper function to get day of week string
 * Following Code Complete: Pure function, clear conversion
 */
function getDayOfWeekString(date: Date): string {
	const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
	return days[date.getDay()];
}

/**
 * Get employee performance score (simplified implementation)
 * Following Code Complete: Clear scoring algorithm
 */
async function getEmployeePerformanceScore(employeeId: string, organizationId: string): Promise<number> {
	try {
		// Get recent KPI records (last 30 days)
		const endDate = new Date();
		const startDate = new Date();
		startDate.setDate(endDate.getDate() - 30);

		const kpiRecords = await kpiService.fetchEmployeeKpiRecords(
			organizationId,
			employeeId,
			undefined,
			startDate,
			endDate
		);

		if (kpiRecords.length === 0) {
			return 75; // Default score for employees without KPI data
		}

		// Calculate average achievement percentage
		const avgAchievement = kpiRecords.reduce((sum, record) => 
			sum + (record.achievementPercentage || 0), 0
		) / kpiRecords.length;

		return Math.min(100, Math.max(0, avgAchievement));

	} catch (error) {
		console.error('❌ enhancedScheduleService.getEmployeePerformanceScore: Failed to get performance score:', error);
		return 75; // Default score on error
	}
}

// Export service object
export const enhancedScheduleService = {
	loadEnhancedWeeklySchedule,
	validateShiftAssignment,
	getSuggestedReplacements,
	calculateEmployeeAvailabilityForDate
};

/**
 * Venue Service
 * Following Code Complete principles: Clear naming, error handling, single responsibility
 * Uses RPC calls exclusively as per requirements
 */

import { supabase } from '$lib/supabaseClient.js';
import type { Venue } from '$lib/types.js';
import { getAuthState } from '$lib/stores/auth.js';

/**
 * Database venue type for RPC responses
 * Following Code Complete: Clear type definitions
 */
interface DbVenue {
	id: string;
	name: string;
	color: string;
	address?: string;
	restaurant_id?: string; // Multi-tenant field
	created_at: string;
	updated_at: string;
}

/**
 * Transform database venue to application venue
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbVenue(dbVenue: DbVenue): Venue {
	return {
		id: dbVenue.id,
		name: dbVenue.name,
		color: dbVenue.color,
		address: dbVenue.address
	};
}

/**
 * Fetch all venues using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchAllVenues(): Promise<Venue[]> {
	const { data, error } = await supabase.rpc('get_all_venues');

	if (error) {
		console.error('❌ venueService.fetchAllVenues: RPC error:', error);
		throw new Error(`Failed to fetch venues: ${error.message}`);
	}

	const venues = (data ?? []).map(transformDbVenue);
	return venues;
}

/**
 * Create new venue using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createVenue(venueData: {
	name: string;
	color: string;
	address?: string;
}): Promise<Venue> {
	// Get current restaurant context
	const authState = getAuthState();
	if (!authState.currentRestaurant) {
		throw new Error('No restaurant context available');
	}

	const { data, error } = await supabase.rpc('create_venue', {
		p_name: venueData.name.trim(),
		p_color: venueData.color,
		p_restaurant_id: authState.currentRestaurant.id,
		p_address: venueData.address?.trim() || null
	});

	if (error) {
		throw new Error(`Failed to create venue: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from create venue operation');
	}

	return transformDbVenue(data);
}

/**
 * Update existing venue using RPC
 * Following Code Complete: Consistent interface with create
 */
async function updateVenue(venueId: string, venueData: {
	name: string;
	color: string;
	address?: string;
}): Promise<Venue> {
	const { data, error } = await supabase.rpc('update_venue', {
		p_venue_id: venueId,
		p_name: venueData.name.trim(),
		p_color: venueData.color,
		p_address: venueData.address?.trim() || null
	});

	if (error) {
		throw new Error(`Failed to update venue: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from update venue operation');
	}

	return transformDbVenue(data);
}

/**
 * Delete venue using RPC
 * Following Code Complete: Clear operation, proper error handling
 */
async function deleteVenue(venueId: string): Promise<void> {
	const { error } = await supabase.rpc('delete_venue', {
		p_venue_id: venueId
	});

	if (error) {
		throw new Error(`Failed to delete venue: ${error.message}`);
	}
}

/**
 * Venue service object
 * Following Code Complete: Consistent interface, clear organization
 */
export const venueService = {
	fetchAllVenues,
	createVenue,
	updateVenue,
	deleteVenue,
	transformDbVenue // Export transformation function for reuse
} as const;

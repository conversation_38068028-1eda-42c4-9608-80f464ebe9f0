/**
 * Server-Side Calculation Service
 * ===============================
 * Replaces ALL frontend calculation logic with server-side RPC calls
 * Following Code Complete principles: Single source of truth, defensive programming
 * Eliminates calculation inconsistencies between frontend and backend
 */

import { supabase } from '$lib/supabaseClient.js';
import type { 
    Shift, 
    Employee, 
    EmployeeWeeklySummary, 
    VenueSummary, 
    WeeklySummary 
} from '$lib/types';

// =====================================================
// Server-Side Calculation Response Types
// =====================================================

export interface ShiftCompensationResult {
    shiftId: string;
    totalCompensation: number;
    hourlyRate: number;
    calculationMethod: 'manual_override' | 'employee_daily_rate' | 'employee_hourly_rate' | 'default_minimum_wage';
    hoursWorked: number;
    baseRate: number;
    isFullDay: boolean;
    employeeId: string | null;
    employeeName: string;
}

export interface PayValidationResult {
    isValid: boolean;
    validationMessage: string;
    suggestedRate: number;
    hourlyRate: number;
    minWageCompliant: boolean;
}

export interface ServerEmployeeWeeklySummary {
    employeeId: string;
    employeeName: string;
    weekStartDate: string;
    totalShifts: number;
    totalHours: number;
    totalCompensation: number;
    paidAmount: number;
    unpaidAmount: number;
    advanceDeductions: number;
    netAmount: number;
    averageHourlyRate: number;
    shiftsBreakdown: any[];
}

export interface ServerVenueSummary {
    venueId: string;
    venueName: string;
    weekStartDate: string;
    totalShifts: number;
    totalHours: number;
    totalCompensation: number;
    paidAmount: number;
    unpaidAmount: number;
    advanceDeductions: number;
    netAmount: number;
    uniqueEmployees: number;
    averageHourlyRate: number;
    shiftsBreakdown: any[];
}

export interface ServerWeeklySummary {
    organizationId: string;
    weekStartDate: string;
    totalShifts: number;
    totalHours: number;
    totalCompensation: number;
    paidAmount: number;
    unpaidAmount: number;
    advanceDeductions: number;
    netAmount: number;
    unassignedShifts: number;
    uniqueEmployees: number;
    activeVenues: number;
    averageHourlyRate: number;
    employeeSummaries: ServerEmployeeWeeklySummary[];
    venueSummaries: ServerVenueSummary[];
}

// =====================================================
// Core Calculation Functions
// =====================================================

/**
 * Calculate shift compensation using server-side RPC
 * Replaces frontend getShiftPay() function entirely
 * Following Code Complete: Clear function purpose, error handling
 */
export async function calculateShiftCompensation(shiftId: string): Promise<ShiftCompensationResult> {
    console.log('🔄 serverSideCalculationService.calculateShiftCompensation: Calculating for shift:', shiftId);

    const { data, error } = await supabase.rpc('calculate_shift_compensation', {
        p_shift_id: shiftId
    });

    if (error) {
        console.error('❌ serverSideCalculationService.calculateShiftCompensation: RPC error:', error);
        throw new Error(`Failed to calculate shift compensation: ${error.message}`);
    }

    if (!data || data.length === 0) {
        throw new Error('Shift not found or calculation failed');
    }

    const result = data[0];
    console.log('✅ serverSideCalculationService.calculateShiftCompensation: Result:', result);

    return {
        shiftId: result.shift_id,
        totalCompensation: result.total_compensation,
        hourlyRate: result.hourly_rate,
        calculationMethod: result.calculation_method,
        hoursWorked: result.hours_worked,
        baseRate: result.base_rate,
        isFullDay: result.is_full_day,
        employeeId: result.employee_id,
        employeeName: result.employee_name
    };
}

/**
 * Validate shift pay rules using server-side RPC
 * Used by frontend forms before saving shift data
 * Following Code Complete: Input validation, defensive programming
 */
export async function validateShiftPayRules(
    employeeId: string | null,
    hours: number,
    proposedRate: number
): Promise<PayValidationResult> {
    console.log('🔄 serverSideCalculationService.validateShiftPayRules: Validating pay rules');

    const { data, error } = await supabase.rpc('validate_shift_pay_rules', {
        p_employee_id: employeeId,
        p_hours: hours,
        p_proposed_rate: proposedRate
    });

    if (error) {
        console.error('❌ serverSideCalculationService.validateShiftPayRules: RPC error:', error);
        throw new Error(`Failed to validate pay rules: ${error.message}`);
    }

    if (!data || data.length === 0) {
        throw new Error('Pay validation failed');
    }

    const result = data[0];
    console.log('✅ serverSideCalculationService.validateShiftPayRules: Result:', result);

    return {
        isValid: result.is_valid,
        validationMessage: result.validation_message,
        suggestedRate: result.suggested_rate,
        hourlyRate: result.hourly_rate,
        minWageCompliant: result.min_wage_compliant
    };
}

/**
 * Calculate employee weekly totals using server-side RPC
 * Replaces frontend calculateEmployeeWeeklySummary() function
 * Following Code Complete: Consistent interface, clear organization
 */
export async function calculateEmployeeWeeklyTotals(
    employeeId: string,
    weekStartDate: Date
): Promise<ServerEmployeeWeeklySummary> {
    console.log('🔄 serverSideCalculationService.calculateEmployeeWeeklyTotals: Calculating for employee:', employeeId);

    const { data, error } = await supabase.rpc('calculate_employee_weekly_totals', {
        p_employee_id: employeeId,
        p_week_start: weekStartDate.toISOString().split('T')[0]
    });

    if (error) {
        console.error('❌ serverSideCalculationService.calculateEmployeeWeeklyTotals: RPC error:', error);
        throw new Error(`Failed to calculate employee weekly totals: ${error.message}`);
    }

    if (!data || data.length === 0) {
        throw new Error('Employee weekly calculation failed');
    }

    const result = data[0];
    console.log('✅ serverSideCalculationService.calculateEmployeeWeeklyTotals: Result:', result);

    return {
        employeeId: result.employee_id,
        employeeName: result.employee_name,
        weekStartDate: result.week_start_date,
        totalShifts: result.total_shifts,
        totalHours: result.total_hours,
        totalCompensation: result.total_compensation,
        paidAmount: result.paid_amount,
        unpaidAmount: result.unpaid_amount,
        advanceDeductions: result.advance_deductions,
        netAmount: result.net_amount,
        averageHourlyRate: result.average_hourly_rate,
        shiftsBreakdown: result.shifts_breakdown || []
    };
}

/**
 * Calculate venue weekly totals using server-side RPC
 * Replaces frontend calculateVenueSummary() function
 * Following Code Complete: Consistent interface, clear organization
 */
export async function calculateVenueWeeklyTotals(
    venueId: string,
    weekStartDate: Date
): Promise<ServerVenueSummary> {
    console.log('🔄 serverSideCalculationService.calculateVenueWeeklyTotals: Calculating for venue:', venueId);

    const { data, error } = await supabase.rpc('calculate_venue_weekly_totals', {
        p_venue_id: venueId,
        p_week_start: weekStartDate.toISOString().split('T')[0]
    });

    if (error) {
        console.error('❌ serverSideCalculationService.calculateVenueWeeklyTotals: RPC error:', error);
        throw new Error(`Failed to calculate venue weekly totals: ${error.message}`);
    }

    if (!data || data.length === 0) {
        throw new Error('Venue weekly calculation failed');
    }

    const result = data[0];
    console.log('✅ serverSideCalculationService.calculateVenueWeeklyTotals: Result:', result);

    return {
        venueId: result.venue_id,
        venueName: result.venue_name,
        weekStartDate: result.week_start_date,
        totalShifts: result.total_shifts,
        totalHours: result.total_hours,
        totalCompensation: result.total_compensation,
        paidAmount: result.paid_amount,
        unpaidAmount: result.unpaid_amount,
        advanceDeductions: result.advance_deductions,
        netAmount: result.net_amount,
        uniqueEmployees: result.unique_employees,
        averageHourlyRate: result.average_hourly_rate,
        shiftsBreakdown: result.shifts_breakdown || []
    };
}

/**
 * Calculate overall weekly summary using server-side RPC
 * Replaces frontend calculateWeeklySummary() function
 * Following Code Complete: Comprehensive interface, error handling
 */
export async function calculateOverallWeeklySummary(
    weekStartDate: Date
): Promise<ServerWeeklySummary> {
    console.log('🔄 serverSideCalculationService.calculateOverallWeeklySummary: Calculating for week:', weekStartDate);

    const { data, error } = await supabase.rpc('calculate_overall_weekly_summary', {
        p_week_start: weekStartDate.toISOString().split('T')[0]
    });

    if (error) {
        console.error('❌ serverSideCalculationService.calculateOverallWeeklySummary: RPC error:', error);
        throw new Error(`Failed to calculate overall weekly summary: ${error.message}`);
    }

    if (!data || data.length === 0) {
        throw new Error('Overall weekly calculation failed');
    }

    const result = data[0];
    console.log('✅ serverSideCalculationService.calculateOverallWeeklySummary: Result:', result);

    return {
        organizationId: result.organization_id,
        weekStartDate: result.week_start_date,
        totalShifts: result.total_shifts,
        totalHours: result.total_hours,
        totalCompensation: result.total_compensation,
        paidAmount: result.paid_amount,
        unpaidAmount: result.unpaid_amount,
        advanceDeductions: result.advance_deductions,
        netAmount: result.net_amount,
        unassignedShifts: result.unassigned_shifts,
        uniqueEmployees: result.unique_employees,
        activeVenues: result.active_venues,
        averageHourlyRate: result.average_hourly_rate,
        employeeSummaries: result.employee_summaries || [],
        venueSummaries: result.venue_summaries || []
    };
}

/**
 * Server-side calculation service object
 * Following Code Complete: Consistent interface, clear organization
 */
export const serverSideCalculationService = {
    calculateShiftCompensation,
    validateShiftPayRules,
    calculateEmployeeWeeklyTotals,
    calculateVenueWeeklyTotals,
    calculateOverallWeeklySummary
} as const;

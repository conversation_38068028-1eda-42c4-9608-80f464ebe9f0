/**
 * Shift Service
 * Following Code Complete principles: Clear naming, error handling, single responsibility
 * Uses RPC calls exclusively as per requirements
 */

import { supabase } from '$lib/supabaseClient.js';
import type { Shift, ShiftFormData } from '$lib/types.js';
import { getAuthState } from '$lib/stores/auth.js';

/**
 * Interface for shift overlap check result
 */
export interface ShiftOverlapResult {
	hasOverlap: boolean;
	conflictingShifts: Array<{
		id: string;
		startTime: string;
		endTime: string;
		venueName: string;
	}>;
}

/**
 * Database shift type for RPC responses
 * Following Code Complete: Clear type definitions
 */
interface DbShift {
	id: string;
	employee_id: string | null;
	location_id: string;
	venue_id: string;
	shift_date: string;  // RPC returns shift_date
	start_time: string;
	end_time: string;
	hours_worked: number;
	total_hours: number;
	daily_rate: number;
	daily_rate_override?: number;
	is_paid: boolean;
	advance_deduction: number;
	notes?: string;
	created_at: string;
	updated_at: string;
}

/**
 * Transform database shift to application shift
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbShift(dbShift: DbShift): Shift {
	const transformed = {
		id: dbShift.id,
		employeeId: dbShift.employee_id,
		locationId: dbShift.location_id,
		venueId: dbShift.venue_id || dbShift.location_id, // Backward compatibility
		date: new Date(dbShift.shift_date + 'T00:00:00.000Z'),  // Fix date parsing from shift_date column
		startTime: dbShift.start_time,
		endTime: dbShift.end_time,
		hoursWorked: dbShift.hours_worked,
		totalHours: dbShift.total_hours || dbShift.hours_worked, // Backward compatibility
		dailyRate: dbShift.daily_rate,
		dailyRateOverride: dbShift.daily_rate_override,
		isPaid: dbShift.is_paid,
		advanceDeduction: dbShift.advance_deduction,
		notes: dbShift.notes,
		createdAt: new Date(dbShift.created_at),
		updatedAt: new Date(dbShift.updated_at)
	};

	return transformed;
}

/**
 * Fetch shifts for a specific week using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchShiftsForWeek(weekStartDate: Date): Promise<Shift[]> {
	const { data, error } = await supabase.rpc('fetch_shifts_for_week', {
		p_week_start: weekStartDate.toISOString().split('T')[0]
	});

	if (error) {
		console.error('❌ shiftService.fetchShiftsForWeek: RPC error:', error);
		throw new Error(`Failed to fetch shifts for week: ${error.message}`);
	}

	const transformedShifts = (data ?? []).map(transformDbShift);
	return transformedShifts;
}

/**
 * Fetch shifts for a specific employee using RPC
 * Following Code Complete: Consistent naming pattern
 */
async function fetchShiftsForEmployee(employeeId: string, startDate?: Date, endDate?: Date): Promise<Shift[]> {
	const { data, error } = await supabase.rpc('get_shifts_for_employee', {
		p_employee_id: employeeId,
		p_start_date: startDate?.toISOString().split('T')[0] || null,
		p_end_date: endDate?.toISOString().split('T')[0] || null
	});

	if (error) {
		throw new Error(`Failed to fetch shifts for employee: ${error.message}`);
	}

	return (data ?? []).map(transformDbShift);
}

/**
 * Fetch shifts for a specific venue using RPC
 * Following Code Complete: Consistent naming pattern
 */
async function fetchShiftsForVenue(venueId: string, startDate?: Date, endDate?: Date): Promise<Shift[]> {
	const { data, error } = await supabase.rpc('get_shifts_for_venue', {
		p_venue_id: venueId,
		p_start_date: startDate?.toISOString().split('T')[0] || null,
		p_end_date: endDate?.toISOString().split('T')[0] || null
	});

	if (error) {
		throw new Error(`Failed to fetch shifts for venue: ${error.message}`);
	}

	return (data ?? []).map(transformDbShift);
}

/**
 * Create new shift using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createShift(shiftData: ShiftFormData): Promise<Shift> {
	// Get current restaurant context
	const authState = getAuthState();
	if (!authState.currentRestaurant) {
		throw new Error('No restaurant context available');
	}

	// Get the current auth state
	const currentAuthState = getAuthState();

	const { data, error } = await supabase.rpc('create_shift', {
		p_employee_id: shiftData.employeeId,
		p_location_id: shiftData.venueId, // venueId is now locationId
		p_shift_date: shiftData.date,
		p_start_time: shiftData.startTime,
		p_end_time: shiftData.endTime,
		p_daily_rate: shiftData.dailyRate,
		p_hours_worked: shiftData.totalHours || null,
		p_is_paid: shiftData.isPaid,
		p_advance_deduction: shiftData.advanceDeduction,
		p_notes: shiftData.notes?.trim() || null
	});

	if (error) {
		console.error('❌ Create shift error:', error);
		throw new Error(`Failed to create shift: ${error.message}`);
	}

	if (!data || data.length === 0) {
		console.error('❌ No data returned from create_shift RPC');
		throw new Error('No data returned from create shift operation');
	}

	// The RPC function returns an array, so take the first item
	return transformDbShift(Array.isArray(data) ? data[0] : data);
}

/**
 * Update shift with partial data (for reassignments)
 * Following Code Complete: Specific method for specific use case
 * Updated to work with enhanced update_shift function
 */
async function updateShiftPartial(shiftId: string, updates: Partial<{employeeId: string | null}>): Promise<Shift> {
	// Use the enhanced update_shift function with only the fields we want to update
	const { data, error } = await supabase.rpc('update_shift', {
		p_shift_id: shiftId,
		p_employee_id: updates.employeeId !== undefined ? updates.employeeId : null,
		// All other parameters are optional and will maintain existing values
		p_location_id: null,
		p_start_time: null,
		p_end_time: null,
		p_date: null,
		p_daily_rate: null,
		p_hourly_rate: null,
		p_is_paid: null,
		p_notes: null
	});

	if (error) {
		console.error('❌ ShiftService: Failed to update shift partially:', error);
		throw new Error(`Failed to update shift: ${error.message}`);
	}

	if (!data || data.length === 0) {
		console.error('❌ ShiftService: No data returned from update_shift RPC');
		throw new Error('No data returned from update shift operation');
	}

	// The enhanced RPC function returns an array, so take the first item
	const result = Array.isArray(data) ? data[0] : data;
	return transformDbShift(result);
}

/**
 * Update existing shift using RPC
 * Following Code Complete: Consistent interface with create
 * Updated to work with enhanced update_shift function
 */
async function updateShift(shiftId: string, shiftData: ShiftFormData): Promise<Shift> {
	// Calculate hourly rate from daily rate and total hours if available
	const hourlyRate = shiftData.totalHours && shiftData.totalHours > 0
		? shiftData.dailyRate / shiftData.totalHours
		: null;

	const { data, error } = await supabase.rpc('update_shift', {
		p_shift_id: shiftId,
		p_employee_id: shiftData.employeeId,
		p_location_id: shiftData.venueId, // Map venueId to location_id
		p_date: shiftData.date, // Updated parameter name
		p_start_time: shiftData.startTime,
		p_end_time: shiftData.endTime,
		p_daily_rate: shiftData.dailyRate,
		p_hourly_rate: hourlyRate,
		p_is_paid: shiftData.isPaid,
		p_advance_deduction: shiftData.advanceDeduction,
		p_notes: shiftData.notes?.trim() || null
		// Hours are calculated automatically from start/end times
	});

	if (error) {
		console.error('❌ ShiftService: Failed to update shift:', error);
		throw new Error(`Failed to update shift: ${error.message}`);
	}

	if (!data || data.length === 0) {
		console.error('❌ ShiftService: No data returned from update_shift RPC');
		throw new Error('No data returned from update shift operation');
	}

	// The enhanced RPC function returns an array, so take the first item
	const result = Array.isArray(data) ? data[0] : data;
	return transformDbShift(result);
}

/**
 * Delete shift using RPC
 * Following Code Complete: Clear operation, comprehensive error handling
 * Enhanced with detailed logging and validation
 */
async function deleteShift(shiftId: string): Promise<void> {
	const { data, error } = await supabase.rpc('delete_shift', {
		p_shift_id: shiftId
	});

	if (error) {
		console.error('❌ ShiftService: Delete shift RPC error:', error);
		throw new Error(`Failed to delete shift: ${error.message}`);
	}

	if (!data || data.length === 0) {
		console.error('❌ ShiftService: No response data from delete_shift RPC');
		throw new Error('No response from delete shift operation');
	}

	const result = Array.isArray(data) ? data[0] : data;

	if (!result.success) {
		console.error('❌ ShiftService: Delete shift failed:', result.message);
		throw new Error(`Failed to delete shift: ${result.message}`);
	}
}

/**
 * Mark shift as paid using RPC
 * Following Code Complete: Specific operation, clear purpose
 */
async function markShiftAsPaid(shiftId: string): Promise<Shift> {
	const { data, error } = await supabase.rpc('mark_shift_as_paid', {
		p_shift_id: shiftId
	});

	if (error) {
		throw new Error(`Failed to mark shift as paid: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from mark shift as paid operation');
	}

	return transformDbShift(data);
}

/**
 * Mark multiple shifts as paid using RPC
 * Following Code Complete: Batch operation, clear purpose
 */
async function markMultipleShiftsAsPaid(shiftIds: string[]): Promise<Shift[]> {
	const { data, error } = await supabase.rpc('mark_multiple_shifts_as_paid', {
		p_shift_ids: shiftIds
	});

	if (error) {
		throw new Error(`Failed to mark shifts as paid: ${error.message}`);
	}

	return (data ?? []).map(transformDbShift);
}

/**
 * Assign shift to employee using RPC
 * Following Code Complete: Specific operation, clear purpose
 */
async function assignShiftToEmployee(shiftId: string, employeeId: string): Promise<Shift> {
	const { data, error } = await supabase.rpc('assign_shift_to_employee', {
		p_shift_id: shiftId,
		p_employee_id: employeeId
	});

	if (error) {
		throw new Error(`Failed to assign shift to employee: ${error.message}`);
	}

	if (!data) {
		throw new Error('No data returned from assign shift operation');
	}

	return transformDbShift(data);
}

/**
 * Convert time string (HH:MM) to minutes since midnight
 */
function timeToMinutes(timeStr: string): number {
	const [hours, minutes] = timeStr.split(':').map(Number);
	return hours * 60 + minutes;
}

/**
 * Check for overlapping shifts for the same employee on the same date
 */
export async function checkShiftOverlap(
	employeeId: string,
	date: string,
	startTime: string,
	endTime: string,
	excludeShiftId?: string
): Promise<ShiftOverlapResult> {
	try {

		// Convert times to minutes for easier comparison
		const startMinutes = timeToMinutes(startTime);
		const endMinutes = timeToMinutes(endTime);

		// Get all shifts for this employee on this date
		const shifts = await fetchShiftsForEmployee(employeeId, new Date(date), new Date(date));

		// Filter out the shift being edited (if any)
		const relevantShifts = shifts.filter(shift => shift.id !== excludeShiftId);

		// Check for overlaps
		const conflictingShifts = relevantShifts.filter(shift => {
			const shiftStartMinutes = timeToMinutes(shift.startTime);
			const shiftEndMinutes = timeToMinutes(shift.endTime);

			// Check if time ranges overlap
			return (startMinutes < shiftEndMinutes && endMinutes > shiftStartMinutes);
		});

		const hasOverlap = conflictingShifts.length > 0;

		return {
			hasOverlap,
			conflictingShifts: conflictingShifts.map(shift => ({
				id: shift.id,
				startTime: shift.startTime,
				endTime: shift.endTime,
				venueName: 'Venue' // We'll get venue name from the schedule context
			}))
		};

	} catch (error) {
		console.error('❌ ShiftService: Error in checkShiftOverlap:', error);
		throw error;
	}
}

/**
 * Bulk delete shifts for an employee on all days in a date range
 */
export async function bulkDeleteEmployeeShifts(
	employeeId: string,
	startDate: string,
	endDate: string
): Promise<{ deletedCount: number; message: string }> {
	try {
		const { data, error } = await supabase.rpc('bulk_delete_employee_shifts', {
			p_employee_id: employeeId,
			p_start_date: startDate,
			p_end_date: endDate
		});

		if (error) {
			console.error('❌ ShiftService: Bulk delete employee shifts error:', error);
			throw new Error(`Failed to bulk delete employee shifts: ${error.message}`);
		}

		// Parse the JSON response from the RPC function
		const result = typeof data === 'string' ? JSON.parse(data) : data;

		if (!result.success) {
			throw new Error(result.message || 'Bulk delete operation failed');
		}

		return {
			deletedCount: result.deleted_count || 0,
			message: result.message || `Deleted ${result.deleted_count || 0} shifts`
		};
	} catch (error) {
		console.error('❌ ShiftService: Error in bulkDeleteEmployeeShifts:', error);
		throw error;
	}
}

/**
 * Bulk delete shifts for all employees on a specific date
 */
export async function bulkDeleteDateShifts(date: string): Promise<{ deletedCount: number; message: string }> {
	try {
		const { data, error } = await supabase.rpc('bulk_delete_date_shifts', {
			p_date: date
		});

		if (error) {
			console.error('❌ ShiftService: Bulk delete date shifts error:', error);
			throw new Error(`Failed to bulk delete date shifts: ${error.message}`);
		}

		// Parse the JSON response from the RPC function
		const result = typeof data === 'string' ? JSON.parse(data) : data;

		if (!result.success) {
			throw new Error(result.message || 'Bulk delete operation failed');
		}

		return {
			deletedCount: result.deleted_count || 0,
			message: result.message || `Deleted ${result.deleted_count || 0} shifts`
		};
	} catch (error) {
		console.error('❌ ShiftService: Error in bulkDeleteDateShifts:', error);
		throw error;
	}
}

/**
 * Bulk delete all shifts for a week (all employees, all dates in range)
 */
export async function bulkDeleteWeekShifts(
	startDate: string,
	endDate: string
): Promise<{ deletedCount: number; message: string }> {
	try {
		const { data, error } = await supabase.rpc('bulk_delete_week_shifts', {
			p_start_date: startDate,
			p_end_date: endDate
		});

		if (error) {
			console.error('❌ ShiftService: Bulk delete week shifts error:', error);
			throw new Error(`Failed to bulk delete week shifts: ${error.message}`);
		}

		// Parse the JSON response from the RPC function
		const result = typeof data === 'string' ? JSON.parse(data) : data;

		if (!result.success) {
			throw new Error(result.message || 'Bulk delete operation failed');
		}

		return {
			deletedCount: result.deleted_count || 0,
			message: result.message || `Deleted ${result.deleted_count || 0} shifts`
		};
	} catch (error) {
		console.error('❌ ShiftService: Error in bulkDeleteWeekShifts:', error);
		throw error;
	}
}

/**
 * Duplicate all shifts from a source week to one or more target weeks
 * Following Code Complete: Clear function purpose, comprehensive error handling
 */
export async function duplicateWeekShifts(
	sourceWeekStart: Date,
	targetWeekStarts: Date[],
	conflictResolution: 'merge' | 'replace' = 'merge',
	organizationId?: string
): Promise<import('$lib/types.js').DuplicateWeekResult> {
	try {

		// Convert dates to ISO strings for RPC function
		const sourceWeekStartStr = sourceWeekStart.toISOString().split('T')[0];
		const targetWeekStartsStr = targetWeekStarts.map(date => date.toISOString().split('T')[0]);

		// Create a timeout promise to prevent infinite loading
		const timeoutPromise = new Promise<never>((_, reject) => {
			setTimeout(() => {
				reject(new Error('Duplicate week operation timed out after 30 seconds'));
			}, 30000); // 30 second timeout
		});

		// Race the RPC call against the timeout
		const rpcPromise = supabase.rpc('duplicate_week_shifts', {
			p_conflict_resolution: conflictResolution,
			p_organization_id: (organizationId && organizationId !== 'auto') ? organizationId : null,
			p_source_week_start: sourceWeekStartStr,
			p_target_week_starts: targetWeekStartsStr
		});

		const { data, error } = await Promise.race([rpcPromise, timeoutPromise]);

		if (error) {
			console.error('❌ ShiftService: Duplicate week shifts error:', error);
			throw new Error(`Failed to duplicate week shifts: ${error.message}`);
		}

		// Parse the JSON response from the RPC function
		const result = typeof data === 'string' ? JSON.parse(data) : data;

		if (!result.success) {
			throw new Error(result.message || 'Duplicate week operation failed');
		}



		// Transform the result to match our TypeScript interface
		const duplicateResult: import('$lib/types.js').DuplicateWeekResult = {
			success: result.success,
			totalShiftsDuplicated: result.totalShiftsDuplicated || 0,
			conflictsDetected: result.conflictsDetected || 0,
			conflictsResolved: result.conflictsResolved || 0,
			targetWeeksProcessed: result.targetWeeksProcessed || 0,
			message: result.message || 'Week duplication completed successfully',
			details: result.details || []
		};

		return duplicateResult;
	} catch (error) {
		console.error('❌ Week duplication failed:', error instanceof Error ? error.message : error);
		throw error;
	}
}

/**
 * Shift service object
 * Following Code Complete: Consistent interface, clear organization
 */
export const shiftService = {
	fetchShiftsForWeek,
	fetchShiftsForEmployee,
	fetchShiftsForVenue,
	createShift,
	updateShift,
	updateShiftPartial,
	deleteShift,
	markShiftAsPaid,
	markMultipleShiftsAsPaid,
	assignShiftToEmployee,
	checkShiftOverlap,
	bulkDeleteEmployeeShifts,
	bulkDeleteDateShifts,
	bulkDeleteWeekShifts,
	duplicateWeekShifts,
	transformDbShift // Export transformation function for reuse
} as const;

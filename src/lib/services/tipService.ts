/**
 * Tip Pooling Service
 * Following Code Complete principles: Clear naming, error handling, single responsibility
 * Uses RPC calls exclusively as per requirements
 */

import { supabase } from '$lib/supabaseClient.js';
import type { 
	TipPool, 
	TipPoolFormData, 
	TipDistribution,
	TipDistributionInput,
	TipDistributionMethod
} from '$lib/types.js';

/**
 * Database tip pool type for RPC responses
 * Following Code Complete: Clear type definitions
 */
interface DbTipPool {
	id: string;
	organization_id: string;
	location_id: string;
	location_name?: string;
	pool_date: string;
	total_tips: number;
	distribution_method: string;
	is_distributed: boolean;
	distributed_at?: string;
	distributed_by?: string;
	distributed_by_name?: string;
	notes?: string;
	created_at: string;
	updated_at: string;
}

/**
 * Database tip distribution type for RPC responses
 * Following Code Complete: Clear type definitions
 */
interface DbTipDistribution {
	id: string;
	employee_id: string;
	employee_name?: string;
	hours_worked: number;
	performance_multiplier: number;
	tip_amount: number;
	created_at: string;
}

/**
 * Transform database tip pool to application tip pool
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbTipPool(dbTipPool: DbTipPool): TipPool {
	return {
		id: dbTipPool.id,
		organizationId: dbTipPool.organization_id,
		locationId: dbTipPool.location_id,
		locationName: dbTipPool.location_name,
		poolDate: new Date(dbTipPool.pool_date),
		totalTips: dbTipPool.total_tips,
		distributionMethod: dbTipPool.distribution_method as TipDistributionMethod,
		isDistributed: dbTipPool.is_distributed,
		distributedAt: dbTipPool.distributed_at ? new Date(dbTipPool.distributed_at) : undefined,
		distributedBy: dbTipPool.distributed_by,
		distributedByName: dbTipPool.distributed_by_name,
		notes: dbTipPool.notes,
		createdAt: new Date(dbTipPool.created_at),
		updatedAt: new Date(dbTipPool.updated_at)
	};
}

/**
 * Transform database tip distribution to application tip distribution
 * Following Code Complete: Pure function, clear transformation
 */
function transformDbTipDistribution(dbTipDistribution: DbTipDistribution): TipDistribution {
	return {
		id: dbTipDistribution.id,
		tipPoolId: '', // Will be set by calling function
		employeeId: dbTipDistribution.employee_id,
		employeeName: dbTipDistribution.employee_name,
		hoursWorked: dbTipDistribution.hours_worked,
		performanceMultiplier: dbTipDistribution.performance_multiplier,
		tipAmount: dbTipDistribution.tip_amount,
		createdAt: new Date(dbTipDistribution.created_at)
	};
}

/**
 * Fetch tip pools using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchTipPools(
	organizationId?: string,
	locationId?: string,
	startDate?: Date,
	endDate?: Date
): Promise<TipPool[]> {
	console.log('🔄 tipService.fetchTipPools: Starting fetch...', { organizationId, locationId, startDate, endDate });

	const { data, error } = await supabase.rpc('get_tip_pools', {
		p_organization_id: organizationId || null,
		p_location_id: locationId || null,
		p_start_date: startDate?.toISOString().split('T')[0] || null,
		p_end_date: endDate?.toISOString().split('T')[0] || null
	});

	if (error) {
		console.error('❌ tipService.fetchTipPools: RPC error:', error);
		throw new Error(`Failed to fetch tip pools: ${error.message}`);
	}

	console.log('✅ tipService.fetchTipPools: Raw data from RPC:', data);

	const tipPools = (data ?? []).map(transformDbTipPool);
	console.log('✅ tipService.fetchTipPools: Transformed tip pools:', tipPools);

	return tipPools;
}

/**
 * Create new tip pool using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function createTipPool(
	organizationId: string,
	tipPoolData: TipPoolFormData
): Promise<string> {
	console.log('🔄 tipService.createTipPool: Creating tip pool:', tipPoolData);

	// Validate tip amount
	if (tipPoolData.totalTips < 0) {
		throw new Error('Tip amount cannot be negative');
	}

	const { data, error } = await supabase.rpc('create_tip_pool', {
		p_organization_id: organizationId,
		p_location_id: tipPoolData.locationId,
		p_pool_date: tipPoolData.poolDate,
		p_total_tips: tipPoolData.totalTips,
		p_distribution_method: tipPoolData.distributionMethod,
		p_notes: tipPoolData.notes?.trim() || null
	});

	if (error) {
		console.error('❌ tipService.createTipPool: RPC error:', error);
		throw new Error(`Failed to create tip pool: ${error.message}`);
	}

	console.log('✅ tipService.createTipPool: Created tip pool ID:', data);
	return data;
}

/**
 * Distribute tips to employees using RPC
 * Following Code Complete: Clear parameter validation, error handling
 */
async function distributeTips(
	tipPoolId: string,
	distributions: TipDistributionInput[]
): Promise<TipDistribution[]> {
	console.log('🔄 tipService.distributeTips: Distributing tips:', { tipPoolId, distributions });

	// Validate distributions
	if (!distributions || distributions.length === 0) {
		throw new Error('At least one employee distribution is required');
	}

	// Validate each distribution
	for (const dist of distributions) {
		if (!dist.employeeId) {
			throw new Error('Employee ID is required for each distribution');
		}
		if (dist.hoursWorked < 0) {
			throw new Error('Hours worked cannot be negative');
		}
		if (dist.performanceMultiplier && dist.performanceMultiplier < 0) {
			throw new Error('Performance multiplier cannot be negative');
		}
	}

	// Convert distributions to JSONB format expected by RPC
	const distributionsJson = distributions.map(dist => ({
		employee_id: dist.employeeId,
		hours_worked: dist.hoursWorked,
		performance_multiplier: dist.performanceMultiplier || 1.0
	}));

	const { data, error } = await supabase.rpc('distribute_tips', {
		p_tip_pool_id: tipPoolId,
		p_distributions: JSON.stringify(distributionsJson)
	});

	if (error) {
		console.error('❌ tipService.distributeTips: RPC error:', error);
		throw new Error(`Failed to distribute tips: ${error.message}`);
	}

	console.log('✅ tipService.distributeTips: Distributed tips:', data);

	// Transform the returned data
	const tipDistributions = (data ?? []).map((dist: any) => ({
		id: '', // RPC doesn't return ID for new distributions
		tipPoolId: tipPoolId,
		employeeId: dist.employee_id,
		employeeName: dist.employee_name,
		hoursWorked: dist.hours_worked,
		performanceMultiplier: dist.performance_multiplier,
		tipAmount: dist.tip_amount,
		createdAt: new Date()
	}));

	return tipDistributions;
}

/**
 * Get tip distributions for a tip pool using RPC
 * Following Code Complete: Clear function purpose, error handling
 */
async function fetchTipDistributions(tipPoolId: string): Promise<TipDistribution[]> {
	console.log('🔄 tipService.fetchTipDistributions: Starting fetch for pool:', tipPoolId);

	const { data, error } = await supabase.rpc('get_tip_distributions', {
		p_tip_pool_id: tipPoolId
	});

	if (error) {
		console.error('❌ tipService.fetchTipDistributions: RPC error:', error);
		throw new Error(`Failed to fetch tip distributions: ${error.message}`);
	}

	console.log('✅ tipService.fetchTipDistributions: Raw data from RPC:', data);

	const tipDistributions = (data ?? []).map((dist: any) => ({
		...transformDbTipDistribution(dist),
		tipPoolId: tipPoolId
	}));
	
	console.log('✅ tipService.fetchTipDistributions: Transformed tip distributions:', tipDistributions);

	return tipDistributions;
}

/**
 * Update tip pool using RPC (for editing before distribution)
 * Following Code Complete: Clear parameter validation, error handling
 */
async function updateTipPool(
	tipPoolId: string,
	updates: Partial<TipPoolFormData>
): Promise<boolean> {
	console.log('🔄 tipService.updateTipPool: Updating tip pool:', { tipPoolId, updates });

	// For now, we'll implement this as a simple update
	// In a full implementation, you'd create an update_tip_pool RPC function
	const { error } = await supabase
		.from('tip_pools')
		.update({
			total_tips: updates.totalTips,
			distribution_method: updates.distributionMethod,
			notes: updates.notes?.trim() || null,
			updated_at: new Date().toISOString()
		})
		.eq('id', tipPoolId)
		.eq('is_distributed', false); // Only allow updates if not yet distributed

	if (error) {
		console.error('❌ tipService.updateTipPool: Update error:', error);
		throw new Error(`Failed to update tip pool: ${error.message}`);
	}

	console.log('✅ tipService.updateTipPool: Updated tip pool');
	return true;
}

/**
 * Delete tip pool using RPC (only if not distributed)
 * Following Code Complete: Clear parameter validation, error handling
 */
async function deleteTipPool(tipPoolId: string): Promise<boolean> {
	console.log('🔄 tipService.deleteTipPool: Deleting tip pool:', tipPoolId);

	// For now, we'll implement this as a simple delete
	// In a full implementation, you'd create a delete_tip_pool RPC function
	const { error } = await supabase
		.from('tip_pools')
		.delete()
		.eq('id', tipPoolId)
		.eq('is_distributed', false); // Only allow deletion if not yet distributed

	if (error) {
		console.error('❌ tipService.deleteTipPool: Delete error:', error);
		throw new Error(`Failed to delete tip pool: ${error.message}`);
	}

	console.log('✅ tipService.deleteTipPool: Deleted tip pool');
	return true;
}

/**
 * Get tip pool summary for a date range
 * Following Code Complete: Clear function purpose, error handling
 */
async function getTipPoolSummary(
	organizationId: string,
	startDate: Date,
	endDate: Date
): Promise<{
	totalTips: number;
	totalDistributed: number;
	poolCount: number;
	distributedPoolCount: number;
}> {
	console.log('🔄 tipService.getTipPoolSummary: Getting summary:', { organizationId, startDate, endDate });

	const tipPools = await fetchTipPools(
		organizationId,
		undefined,
		startDate,
		endDate
	);

	const summary = tipPools.reduce((acc, pool) => {
		acc.totalTips += pool.totalTips;
		acc.poolCount += 1;
		
		if (pool.isDistributed) {
			acc.totalDistributed += pool.totalTips;
			acc.distributedPoolCount += 1;
		}
		
		return acc;
	}, {
		totalTips: 0,
		totalDistributed: 0,
		poolCount: 0,
		distributedPoolCount: 0
	});

	console.log('✅ tipService.getTipPoolSummary: Summary:', summary);
	return summary;
}

// Export service object
export const tipService = {
	fetchTipPools,
	createTipPool,
	distributeTips,
	fetchTipDistributions,
	updateTipPool,
	deleteTipPool,
	getTipPoolSummary
};

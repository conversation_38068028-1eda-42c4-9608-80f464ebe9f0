/**
 * Environment Configuration Module
 * Handles environment variables for both SSR and client-side
 * Following Code Complete principles: Centralized configuration, error handling
 */

import { browser } from '$app/environment';

// Environment variable configuration
interface EnvConfig {
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
}

/**
 * Get environment variables with fallback handling
 * Works in both SSR and client-side contexts
 */
function getEnvConfig(): EnvConfig {
  // In browser context, use import.meta.env
  if (browser) {
    const url = import.meta.env.PUBLIC_SUPABASE_URL;
    const key = import.meta.env.PUBLIC_SUPABASE_ANON_KEY;
    
    if (!url || !key) {
      throw new Error(
        'Missing Supabase environment variables in browser context. Please check your .env file and ensure PUBLIC_SUPABASE_URL and PUBLIC_SUPABASE_ANON_KEY are set.'
      );
    }
    
    return {
      SUPABASE_URL: url,
      SUPABASE_ANON_KEY: key
    };
  }
  
  // In SSR context, try multiple approaches
  let url: string | undefined;
  let key: string | undefined;
  
  // Try import.meta.env first
  try {
    url = import.meta.env.PUBLIC_SUPABASE_URL;
    key = import.meta.env.PUBLIC_SUPABASE_ANON_KEY;
  } catch (e) {
    // Fallback for SSR
  }
  
  // If not available, try process.env as fallback
  if (!url || !key) {
    url = process.env.PUBLIC_SUPABASE_URL;
    key = process.env.PUBLIC_SUPABASE_ANON_KEY;
  }
  
  // Final validation
  if (!url || !key) {
    throw new Error(
      'Missing Supabase environment variables in SSR context. Please check your .env file and ensure PUBLIC_SUPABASE_URL and PUBLIC_SUPABASE_ANON_KEY are set.'
    );
  }
  
  return {
    SUPABASE_URL: url,
    SUPABASE_ANON_KEY: key
  };
}

// Export the configuration
export const env = getEnvConfig();

// Validate URL format
if (!env.SUPABASE_URL.startsWith('https://') || !env.SUPABASE_URL.includes('.supabase.co')) {
  throw new Error(
    'Invalid Supabase URL format. Expected format: https://your-project-id.supabase.co'
  );
}

// Export individual values for convenience
export const SUPABASE_URL = env.SUPABASE_URL;
export const SUPABASE_ANON_KEY = env.SUPABASE_ANON_KEY;

/**
 * Security Module Index
 * Centralized exports for all security services and components
 * Following Code Complete principles: Clear module organization, single entry point
 */

// ============================================================================
// CORE SECURITY SERVICES
// ============================================================================

// Token validation and management
export {
	validateToken,
	refreshTokenIfNeeded,
	getOrganizationContext,
	validateSecurity,
	hasOrganizationRole,
	formatTimeUntilExpiry,
	getSecurityStatus,
	type TokenValidationResult,
	type OrganizationContext,
	type SecurityValidationResult
} from './TokenValidationService.js';

// Secure RPC service
export {
	secureRPC,
	secureOrgRPC,
	secureAdminRPC,
	type SecureRPCOptions,
	type SecureRPCResult,
	type RPCCallLog
} from './SecureRPCService.js';

// Session management
export {
	SessionManager,
	sessionManager,
	sessionState,
	isSessionActive,
	currentUser,
	sessionTimeRemaining,
	type SessionState,
	type SessionConfig,
	type SessionEvent
} from './SessionManager.js';

// Security audit and monitoring
export {
	SecurityAuditService,
	securityAudit,
	securityEvents,
	securityAlerts,
	securityMetrics,
	type SecurityEvent,
	type SecurityEventType,
	type SecurityMetrics,
	type SecurityAlert
} from './SecurityAuditService.js';

// Enhanced authentication store
export {
	EnhancedAuthService,
	enhancedAuth,
	enhancedAuthState,
	isAuthenticated,
	isLoading,
	currentUser as enhancedCurrentUser,
	organizationContext,
	securityStatus,
	type EnhancedAuthState,
	type AuthenticationOptions,
	type AuthenticationResult
} from './EnhancedAuthStore.js';

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Initialize all security services
 * Following Code Complete: Centralized initialization, error handling
 */
export async function initializeSecurity(): Promise<void> {
	console.log('🔒 Security: Initializing all security services...');
	
	try {
		// Initialize enhanced authentication (includes session manager)
		const { enhancedAuth } = await import('./EnhancedAuthStore.js');
		await enhancedAuth.initialize();
		
		console.log('✅ Security: All services initialized successfully');
	} catch (error) {
		console.error('🔒 Security: Initialization failed:', error);
		throw error;
	}
}

/**
 * Get comprehensive security status
 * Following Code Complete: Status reporting, centralized monitoring
 */
export async function getComprehensiveSecurityStatus(): Promise<{
	authentication: any;
	session: any;
	metrics: any;
	alerts: any;
}> {
	const { get } = await import('svelte/store');
	const { enhancedAuthState } = await import('./EnhancedAuthStore.js');
	const { sessionManager } = await import('./SessionManager.js');
	const { securityAudit } = await import('./SecurityAuditService.js');

	return {
		authentication: get(enhancedAuthState),
		session: sessionManager.getStatus(),
		metrics: securityAudit.getMetrics(),
		alerts: securityAudit.generateReport(24 * 60 * 60 * 1000) // Last 24 hours
	};
}

/**
 * Emergency security lockdown
 * Following Code Complete: Emergency procedures, security response
 */
export async function emergencyLockdown(reason: string): Promise<void> {
	console.warn('🚨 Security: Emergency lockdown initiated:', reason);
	
	try {
		const { enhancedAuth } = await import('./EnhancedAuthStore.js');
		const { securityAudit } = await import('./SecurityAuditService.js');
		
		// Log critical security event
		securityAudit.logEvent('security_violation', {
			reason,
			action: 'emergency_lockdown',
			timestamp: Date.now()
		}, 'critical');
		
		// Force sign out
		await enhancedAuth.signOut();
		
		// Clear all local storage (browser only)
		if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
			localStorage.clear();
		}
		
		console.log('✅ Security: Emergency lockdown completed');
	} catch (error) {
		console.error('🔒 Security: Emergency lockdown failed:', error);
		throw error;
	}
}

// ============================================================================
// SECURITY CONSTANTS
// ============================================================================

/**
 * Security configuration constants
 * Following Code Complete: Centralized configuration, clear constants
 */
export const SECURITY_CONSTANTS = {
	// Token management
	TOKEN_REFRESH_THRESHOLD: 5 * 60 * 1000, // 5 minutes
	TOKEN_VALIDATION_TIMEOUT: 10 * 1000, // 10 seconds
	
	// Session management
	SESSION_IDLE_TIMEOUT: 30 * 60 * 1000, // 30 minutes
	SESSION_ABSOLUTE_TIMEOUT: 8 * 60 * 60 * 1000, // 8 hours
	SESSION_WARNING_THRESHOLD: 5 * 60 * 1000, // 5 minutes
	
	// Rate limiting
	MAX_REQUESTS_PER_MINUTE: 100,
	MAX_FAILED_LOGINS: 5,
	LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
	
	// Security monitoring
	SECURITY_EVENT_RETENTION: 7 * 24 * 60 * 60 * 1000, // 7 days
	ALERT_THRESHOLD_HIGH_SEVERITY: 3,
	METRICS_UPDATE_INTERVAL: 5 * 60 * 1000, // 5 minutes
	
	// Role hierarchy
	ROLE_HIERARCHY: ['viewer', 'editor', 'admin', 'superadmin'] as const,
	
	// Security headers
	SECURITY_HEADERS: {
		'X-Frame-Options': 'DENY',
		'X-Content-Type-Options': 'nosniff',
		'Referrer-Policy': 'strict-origin-when-cross-origin',
		'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
	}
} as const;

/**
 * Security error codes
 * Following Code Complete: Standardized error handling, clear categorization
 */
export const SECURITY_ERRORS = {
	// Authentication errors
	AUTH_REQUIRED: 'SECURITY_AUTH_REQUIRED',
	AUTH_INVALID: 'SECURITY_AUTH_INVALID',
	AUTH_EXPIRED: 'SECURITY_AUTH_EXPIRED',
	AUTH_REFRESH_FAILED: 'SECURITY_AUTH_REFRESH_FAILED',
	
	// Authorization errors
	INSUFFICIENT_PERMISSIONS: 'SECURITY_INSUFFICIENT_PERMISSIONS',
	ORGANIZATION_REQUIRED: 'SECURITY_ORGANIZATION_REQUIRED',
	ROLE_REQUIRED: 'SECURITY_ROLE_REQUIRED',
	
	// Session errors
	SESSION_EXPIRED: 'SECURITY_SESSION_EXPIRED',
	SESSION_INVALID: 'SECURITY_SESSION_INVALID',
	SESSION_TIMEOUT: 'SECURITY_SESSION_TIMEOUT',
	
	// Rate limiting errors
	RATE_LIMIT_EXCEEDED: 'SECURITY_RATE_LIMIT_EXCEEDED',
	TOO_MANY_REQUESTS: 'SECURITY_TOO_MANY_REQUESTS',
	
	// Security violations
	CSRF_DETECTED: 'SECURITY_CSRF_DETECTED',
	SUSPICIOUS_ACTIVITY: 'SECURITY_SUSPICIOUS_ACTIVITY',
	SECURITY_VIOLATION: 'SECURITY_VIOLATION'
} as const;

// ============================================================================
// TYPE EXPORTS
// ============================================================================

// Re-export commonly used types for convenience
export type SecurityRole = 'viewer' | 'editor' | 'admin' | 'superadmin';
export type SecuritySeverity = 'low' | 'medium' | 'high' | 'critical';
export type SecurityStatus = 'secure' | 'warning' | 'error';

/**
 * Security context interface for components
 * Following Code Complete: Clear interface definition, comprehensive context
 */
export interface SecurityContext {
	isAuthenticated: boolean;
	user: any | null;
	organizationId: string | null;
	userRole: SecurityRole | null;
	hasRole: (role: SecurityRole) => boolean;
	securityStatus: SecurityStatus;
	sessionTimeRemaining: number;
	needsRefresh: boolean;
}

/**
 * Security guard configuration
 * Following Code Complete: Configuration interface, clear options
 */
export interface SecurityGuardConfig {
	requireAuth?: boolean;
	requireOrganization?: boolean;
	requiredRole?: SecurityRole;
	redirectTo?: string;
	allowedRoles?: SecurityRole[];
	customValidator?: (context: SecurityContext) => boolean;
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Check if user has any of the specified roles
 * Following Code Complete: Utility function, clear logic
 */
export function hasAnyRole(userRole: string | null, allowedRoles: SecurityRole[]): boolean {
	if (!userRole) return false;
	
	const roleHierarchy = SECURITY_CONSTANTS.ROLE_HIERARCHY;
	const userRoleIndex = roleHierarchy.indexOf(userRole as SecurityRole);
	
	if (userRoleIndex === -1) return false;
	
	return allowedRoles.some(role => {
		const requiredRoleIndex = roleHierarchy.indexOf(role);
		return userRoleIndex >= requiredRoleIndex;
	});
}

/**
 * Format security error message
 * Following Code Complete: User-friendly error formatting
 */
export function formatSecurityError(errorCode: string, details?: any): string {
	const errorMessages: Record<string, string> = {
		[SECURITY_ERRORS.AUTH_REQUIRED]: 'Authentication required to access this resource.',
		[SECURITY_ERRORS.AUTH_INVALID]: 'Invalid authentication credentials.',
		[SECURITY_ERRORS.AUTH_EXPIRED]: 'Your session has expired. Please sign in again.',
		[SECURITY_ERRORS.INSUFFICIENT_PERMISSIONS]: 'You do not have permission to perform this action.',
		[SECURITY_ERRORS.ORGANIZATION_REQUIRED]: 'Organization membership required.',
		[SECURITY_ERRORS.RATE_LIMIT_EXCEEDED]: 'Too many requests. Please try again later.',
		[SECURITY_ERRORS.SESSION_EXPIRED]: 'Your session has expired due to inactivity.',
		[SECURITY_ERRORS.CSRF_DETECTED]: 'Security violation detected. Request blocked.',
		[SECURITY_ERRORS.SUSPICIOUS_ACTIVITY]: 'Suspicious activity detected. Access restricted.'
	};
	
	const baseMessage = errorMessages[errorCode] || 'A security error occurred.';
	
	if (details?.requiredRole) {
		return `${baseMessage} Required role: ${details.requiredRole}.`;
	}
	
	return baseMessage;
}

/**
 * Validate security configuration
 * Following Code Complete: Configuration validation, error prevention
 */
export function validateSecurityConfig(config: SecurityGuardConfig): string[] {
	const errors: string[] = [];
	
	if (config.requiredRole && !config.requireOrganization) {
		errors.push('requiredRole requires requireOrganization to be true');
	}
	
	if (config.allowedRoles && config.requiredRole) {
		errors.push('Cannot specify both requiredRole and allowedRoles');
	}
	
	if (config.allowedRoles) {
		const invalidRoles = config.allowedRoles.filter(
			role => !SECURITY_CONSTANTS.ROLE_HIERARCHY.includes(role)
		);
		if (invalidRoles.length > 0) {
			errors.push(`Invalid roles: ${invalidRoles.join(', ')}`);
		}
	}
	
	return errors;
}

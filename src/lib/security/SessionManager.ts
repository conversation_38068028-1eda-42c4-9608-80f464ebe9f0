/**
 * Session Manager
 * Advanced session lifecycle management with security monitoring
 * Following Code Complete principles: Comprehensive session handling, security-first
 */

import { writable, derived, get } from 'svelte/store';
import { supabase } from '$lib/supabaseClient.js';
import { validateSecurity, type SecurityValidationResult } from './TokenValidationService.js';
import type { Session, User } from '@supabase/supabase-js';

// ============================================================================
// TYPES
// ============================================================================

export interface SessionState {
	isActive: boolean;
	user: User | null;
	session: Session | null;
	organizationId: string | null;
	userRole: string | null;
	lastActivity: number;
	sessionStart: number;
	expiresAt: number;
	needsRefresh: boolean;
	isRefreshing: boolean;
}

export interface SessionConfig {
	idleTimeout: number; // Time before session expires due to inactivity
	absoluteTimeout: number; // Maximum session duration
	refreshThreshold: number; // Time before expiry to refresh
	activityCheckInterval: number; // How often to check activity
	warningThreshold: number; // When to warn user about expiry
}

export interface SessionEvent {
	type: 'session_start' | 'session_end' | 'session_refresh' | 'activity_update' | 'timeout_warning' | 'session_expired';
	timestamp: number;
	userId?: string;
	sessionId?: string;
	details?: any;
}

// ============================================================================
// CONFIGURATION
// ============================================================================

/**
 * Default session configuration
 * Following Code Complete: Centralized configuration, security-focused defaults
 */
const DEFAULT_SESSION_CONFIG: SessionConfig = {
	idleTimeout: 30 * 60 * 1000, // 30 minutes
	absoluteTimeout: 8 * 60 * 60 * 1000, // 8 hours
	refreshThreshold: 5 * 60 * 1000, // 5 minutes
	activityCheckInterval: 60 * 1000, // 1 minute
	warningThreshold: 5 * 60 * 1000 // 5 minutes
};

// ============================================================================
// SESSION STORE
// ============================================================================

/**
 * Session state store
 * Following Code Complete: Centralized state management
 */
const initialSessionState: SessionState = {
	isActive: false,
	user: null,
	session: null,
	organizationId: null,
	userRole: null,
	lastActivity: 0,
	sessionStart: 0,
	expiresAt: 0,
	needsRefresh: false,
	isRefreshing: false
};

export const sessionState = writable<SessionState>(initialSessionState);

// Derived stores for convenience
export const isSessionActive = derived(sessionState, $state => $state.isActive);
export const currentUser = derived(sessionState, $state => $state.user);
export const sessionTimeRemaining = derived(sessionState, $state => {
	if (!$state.isActive) return 0;
	return Math.max(0, $state.expiresAt - Date.now());
});

// ============================================================================
// SESSION MANAGER CLASS
// ============================================================================

export class SessionManager {
	private config: SessionConfig;
	private activityTimer: NodeJS.Timeout | null = null;
	private refreshTimer: NodeJS.Timeout | null = null;
	private eventListeners: ((event: SessionEvent) => void)[] = [];

	constructor(config: Partial<SessionConfig> = {}) {
		this.config = { ...DEFAULT_SESSION_CONFIG, ...config };
		this.setupActivityTracking();
		this.setupAuthStateListener();
	}

	/**
	 * Initialize session manager
	 * Following Code Complete: Clear initialization, comprehensive setup
	 */
	async initialize(): Promise<void> {
		console.log('🔒 SessionManager: Initializing...');
		
		try {
			const validation = await validateSecurity();
			
			if (validation.isValid && validation.session) {
				await this.startSession(validation);
			} else {
				this.clearSession();
			}
		} catch (error) {
			console.error('🔒 SessionManager: Initialization failed:', error);
			this.clearSession();
		}
	}

	/**
	 * Start new session
	 * Following Code Complete: Clear session lifecycle, comprehensive tracking
	 */
	private async startSession(validation: SecurityValidationResult): Promise<void> {
		const now = Date.now();
		const expiresAt = validation.session?.expires_at ? validation.session.expires_at * 1000 : now + this.config.absoluteTimeout;

		const newSessionState: SessionState = {
			isActive: true,
			user: validation.user,
			session: validation.session,
			organizationId: validation.organizationContext.organizationId,
			userRole: validation.organizationContext.userRole,
			lastActivity: now,
			sessionStart: now,
			expiresAt,
			needsRefresh: validation.needsRefresh,
			isRefreshing: false
		};

		sessionState.set(newSessionState);

		this.emitEvent({
			type: 'session_start',
			timestamp: now,
			userId: validation.user?.id,
			sessionId: validation.session?.access_token.substring(0, 8),
			details: {
				organizationId: validation.organizationContext.organizationId,
				userRole: validation.organizationContext.userRole
			}
		});

		this.scheduleRefresh();
		this.startActivityMonitoring();

		console.log('✅ SessionManager: Session started');
	}

	/**
	 * End current session
	 * Following Code Complete: Clean session termination, state clearing
	 */
	async endSession(reason: string = 'user_logout'): Promise<void> {
		const currentState = get(sessionState);
		
		if (currentState.isActive) {
			this.emitEvent({
				type: 'session_end',
				timestamp: Date.now(),
				userId: currentState.user?.id,
				details: { reason }
			});
		}

		// Sign out from Supabase
		try {
			await supabase.auth.signOut();
		} catch (error) {
			console.warn('🔒 SessionManager: Supabase signout error:', error);
		}

		this.clearSession();
		console.log('✅ SessionManager: Session ended');
	}

	/**
	 * Update activity timestamp
	 * Following Code Complete: Activity tracking, session extension
	 */
	updateActivity(): void {
		const currentState = get(sessionState);
		
		if (!currentState.isActive) return;

		const now = Date.now();
		const newExpiresAt = Math.min(
			now + this.config.idleTimeout,
			currentState.sessionStart + this.config.absoluteTimeout
		);

		sessionState.update(state => ({
			...state,
			lastActivity: now,
			expiresAt: newExpiresAt
		}));

		this.emitEvent({
			type: 'activity_update',
			timestamp: now,
			userId: currentState.user?.id
		});
	}

	/**
	 * Refresh session token
	 * Following Code Complete: Token refresh, error handling
	 */
	async refreshSession(): Promise<boolean> {
		const currentState = get(sessionState);
		
		if (!currentState.isActive || currentState.isRefreshing) {
			return false;
		}

		sessionState.update(state => ({ ...state, isRefreshing: true }));

		try {
			const { data: { session }, error } = await supabase.auth.refreshSession();

			if (error || !session) {
				console.error('🔒 SessionManager: Refresh failed:', error?.message);
				await this.endSession('refresh_failed');
				return false;
			}

			const now = Date.now();
			const expiresAt = session.expires_at ? session.expires_at * 1000 : now + this.config.absoluteTimeout;

			sessionState.update(state => ({
				...state,
				session,
				expiresAt,
				needsRefresh: false,
				isRefreshing: false
			}));

			this.emitEvent({
				type: 'session_refresh',
				timestamp: now,
				userId: session.user.id,
				sessionId: session.access_token.substring(0, 8)
			});

			this.scheduleRefresh();
			console.log('✅ SessionManager: Session refreshed');
			return true;

		} catch (error) {
			console.error('🔒 SessionManager: Refresh error:', error);
			sessionState.update(state => ({ ...state, isRefreshing: false }));
			await this.endSession('refresh_error');
			return false;
		}
	}

	/**
	 * Check session validity and handle expiration
	 * Following Code Complete: Comprehensive validation, automatic handling
	 */
	private async checkSessionValidity(): Promise<void> {
		const currentState = get(sessionState);
		
		if (!currentState.isActive) return;

		const now = Date.now();
		const timeRemaining = currentState.expiresAt - now;

		// Check if session expired
		if (timeRemaining <= 0) {
			this.emitEvent({
				type: 'session_expired',
				timestamp: now,
				userId: currentState.user?.id,
				details: { reason: 'timeout' }
			});
			
			await this.endSession('session_expired');
			return;
		}

		// Check if warning needed
		if (timeRemaining <= this.config.warningThreshold) {
			this.emitEvent({
				type: 'timeout_warning',
				timestamp: now,
				userId: currentState.user?.id,
				details: { timeRemaining }
			});
		}

		// Check if refresh needed
		if (timeRemaining <= this.config.refreshThreshold && !currentState.needsRefresh) {
			sessionState.update(state => ({ ...state, needsRefresh: true }));
			await this.refreshSession();
		}
	}

	/**
	 * Setup activity tracking
	 * Following Code Complete: Event-driven architecture, comprehensive tracking
	 */
	private setupActivityTracking(): void {
		// Track user interactions
		const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
		
		const activityHandler = () => {
			this.updateActivity();
		};

		events.forEach(event => {
			document.addEventListener(event, activityHandler, { passive: true });
		});

		// Track page visibility changes
		document.addEventListener('visibilitychange', () => {
			if (!document.hidden) {
				this.updateActivity();
			}
		});
	}

	/**
	 * Setup Supabase auth state listener
	 * Following Code Complete: Event-driven architecture, state synchronization
	 */
	private setupAuthStateListener(): void {
		supabase.auth.onAuthStateChange(async (event, session) => {
			console.log('🔒 SessionManager: Auth state change:', event);

			switch (event) {
				case 'SIGNED_IN':
					if (session) {
						const validation = await validateSecurity();
						if (validation.isValid) {
							await this.startSession(validation);
						}
					}
					break;

				case 'SIGNED_OUT':
					this.clearSession();
					break;

				case 'TOKEN_REFRESHED':
					if (session) {
						const currentState = get(sessionState);
						if (currentState.isActive) {
							sessionState.update(state => ({
								...state,
								session,
								expiresAt: session.expires_at ? session.expires_at * 1000 : state.expiresAt,
								needsRefresh: false
							}));
						}
					}
					break;
			}
		});
	}

	/**
	 * Schedule automatic session refresh
	 * Following Code Complete: Proactive session management
	 */
	private scheduleRefresh(): void {
		if (this.refreshTimer) {
			clearTimeout(this.refreshTimer);
		}

		const currentState = get(sessionState);
		const timeUntilRefresh = Math.max(0, currentState.expiresAt - Date.now() - this.config.refreshThreshold);

		this.refreshTimer = setTimeout(() => {
			this.refreshSession();
		}, timeUntilRefresh);
	}

	/**
	 * Start activity monitoring
	 * Following Code Complete: Periodic validation, automatic cleanup
	 */
	private startActivityMonitoring(): void {
		if (this.activityTimer) {
			clearInterval(this.activityTimer);
		}

		this.activityTimer = setInterval(() => {
			this.checkSessionValidity();
		}, this.config.activityCheckInterval);
	}

	/**
	 * Clear session state and timers
	 * Following Code Complete: Complete cleanup, resource management
	 */
	private clearSession(): void {
		sessionState.set(initialSessionState);

		if (this.activityTimer) {
			clearInterval(this.activityTimer);
			this.activityTimer = null;
		}

		if (this.refreshTimer) {
			clearTimeout(this.refreshTimer);
			this.refreshTimer = null;
		}
	}

	/**
	 * Add event listener
	 * Following Code Complete: Observer pattern, event-driven architecture
	 */
	addEventListener(listener: (event: SessionEvent) => void): void {
		this.eventListeners.push(listener);
	}

	/**
	 * Remove event listener
	 * Following Code Complete: Resource cleanup, memory management
	 */
	removeEventListener(listener: (event: SessionEvent) => void): void {
		const index = this.eventListeners.indexOf(listener);
		if (index > -1) {
			this.eventListeners.splice(index, 1);
		}
	}

	/**
	 * Emit session event
	 * Following Code Complete: Event notification, observer pattern
	 */
	private emitEvent(event: SessionEvent): void {
		this.eventListeners.forEach(listener => {
			try {
				listener(event);
			} catch (error) {
				console.error('🔒 SessionManager: Event listener error:', error);
			}
		});
	}

	/**
	 * Get current session status
	 * Following Code Complete: Status reporting, clear interface
	 */
	getStatus(): {
		isActive: boolean;
		timeRemaining: number;
		needsRefresh: boolean;
		organizationId: string | null;
		userRole: string | null;
	} {
		const state = get(sessionState);
		return {
			isActive: state.isActive,
			timeRemaining: Math.max(0, state.expiresAt - Date.now()),
			needsRefresh: state.needsRefresh,
			organizationId: state.organizationId,
			userRole: state.userRole
		};
	}
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

/**
 * Global session manager instance
 * Following Code Complete: Singleton pattern, centralized management
 */
export const sessionManager = new SessionManager();

/**
 * Security Audit Service
 * Comprehensive security monitoring and logging system
 * Following Code Complete principles: Security monitoring, comprehensive logging
 */

import { writable, get } from 'svelte/store';
import { browser } from '$app/environment';

// ============================================================================
// TYPES
// ============================================================================

export interface SecurityEvent {
	id: string;
	type: SecurityEventType;
	severity: 'low' | 'medium' | 'high' | 'critical';
	timestamp: number;
	userId?: string;
	sessionId?: string;
	organizationId?: string;
	route?: string;
	userAgent?: string;
	ip?: string;
	details: any;
	resolved: boolean;
}

export type SecurityEventType =
	| 'authentication_success'
	| 'authentication_failure'
	| 'session_start'
	| 'session_end'
	| 'session_expired'
	| 'token_refresh'
	| 'unauthorized_access'
	| 'permission_denied'
	| 'suspicious_activity'
	| 'rate_limit_exceeded'
	| 'invalid_token'
	| 'csrf_attempt'
	| 'data_access'
	| 'admin_action'
	| 'security_violation';

export interface SecurityMetrics {
	totalEvents: number;
	eventsByType: Record<SecurityEventType, number>;
	eventsBySeverity: Record<string, number>;
	failedLogins: number;
	suspiciousActivities: number;
	lastActivity: number;
	activeUsers: number;
	securityScore: number;
}

export interface SecurityAlert {
	id: string;
	type: 'anomaly' | 'threshold' | 'pattern' | 'critical';
	message: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
	timestamp: number;
	events: SecurityEvent[];
	acknowledged: boolean;
}

// ============================================================================
// CONFIGURATION
// ============================================================================

/**
 * Security monitoring configuration
 * Following Code Complete: Centralized configuration, security-focused
 */
const SECURITY_CONFIG = {
	maxEventsInMemory: 1000,
	alertThresholds: {
		failedLogins: 5,
		suspiciousActivities: 3,
		rateLimitViolations: 10
	},
	retentionPeriod: 7 * 24 * 60 * 60 * 1000, // 7 days
	anomalyDetectionWindow: 60 * 60 * 1000, // 1 hour
	reportingInterval: 5 * 60 * 1000 // 5 minutes
};

// ============================================================================
// STORES
// ============================================================================

export const securityEvents = writable<SecurityEvent[]>([]);
export const securityAlerts = writable<SecurityAlert[]>([]);
export const securityMetrics = writable<SecurityMetrics>({
	totalEvents: 0,
	eventsByType: {} as Record<SecurityEventType, number>,
	eventsBySeverity: { low: 0, medium: 0, high: 0, critical: 0 },
	failedLogins: 0,
	suspiciousActivities: 0,
	lastActivity: 0,
	activeUsers: 0,
	securityScore: 100
});

// ============================================================================
// SECURITY AUDIT SERVICE
// ============================================================================

export class SecurityAuditService {
	private events: SecurityEvent[] = [];
	private alerts: SecurityAlert[] = [];
	private eventListeners: ((event: SecurityEvent) => void)[] = [];
	private alertListeners: ((alert: SecurityAlert) => void)[] = [];
	private metricsTimer: NodeJS.Timeout | null = null;

	constructor() {
		this.loadPersistedData();
		this.startMetricsCollection();
	}

	/**
	 * Log security event
	 * Following Code Complete: Comprehensive logging, structured data
	 */
	logEvent(
		type: SecurityEventType,
		details: any = {},
		severity: 'low' | 'medium' | 'high' | 'critical' = 'low',
		userId?: string,
		organizationId?: string
	): SecurityEvent {
		const event: SecurityEvent = {
			id: this.generateEventId(),
			type,
			severity,
			timestamp: Date.now(),
			userId,
			organizationId,
			route: browser && typeof window !== 'undefined' ? window.location.pathname : undefined,
			userAgent: browser && typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
			ip: 'client-side', // In production, get from server
			details,
			resolved: false
		};

		this.addEvent(event);
		this.checkForAnomalies(event);
		this.updateMetrics();

		// Emit to listeners
		this.eventListeners.forEach(listener => {
			try {
				listener(event);
			} catch (error) {
				console.error('🔒 SecurityAuditService: Event listener error:', error);
			}
		});

		return event;
	}

	/**
	 * Log authentication success
	 * Following Code Complete: Specific event logging, clear intent
	 */
	logAuthSuccess(userId: string, organizationId?: string): void {
		this.logEvent('authentication_success', {
			method: 'password',
			timestamp: Date.now()
		}, 'low', userId, organizationId);
	}

	/**
	 * Log authentication failure
	 * Following Code Complete: Security monitoring, failure tracking
	 */
	logAuthFailure(email: string, reason: string): void {
		this.logEvent('authentication_failure', {
			email,
			reason,
			timestamp: Date.now()
		}, 'medium');

		// Check for brute force attempts
		this.checkBruteForceAttempts(email);
	}

	/**
	 * Log unauthorized access attempt
	 * Following Code Complete: Security violation tracking
	 */
	logUnauthorizedAccess(route: string, userId?: string, requiredRole?: string): void {
		this.logEvent('unauthorized_access', {
			route,
			requiredRole,
			timestamp: Date.now()
		}, 'high', userId);
	}

	/**
	 * Log suspicious activity
	 * Following Code Complete: Anomaly detection, security monitoring
	 */
	logSuspiciousActivity(activity: string, details: any, userId?: string): void {
		this.logEvent('suspicious_activity', {
			activity,
			...details,
			timestamp: Date.now()
		}, 'high', userId);
	}

	/**
	 * Log admin action
	 * Following Code Complete: Administrative action tracking
	 */
	logAdminAction(action: string, target: string, userId: string, organizationId?: string): void {
		this.logEvent('admin_action', {
			action,
			target,
			timestamp: Date.now()
		}, 'medium', userId, organizationId);
	}

	/**
	 * Create security alert
	 * Following Code Complete: Alert management, escalation
	 */
	createAlert(
		type: SecurityAlert['type'],
		message: string,
		severity: SecurityAlert['severity'],
		relatedEvents: SecurityEvent[] = []
	): SecurityAlert {
		const alert: SecurityAlert = {
			id: this.generateAlertId(),
			type,
			message,
			severity,
			timestamp: Date.now(),
			events: relatedEvents,
			acknowledged: false
		};

		this.alerts.push(alert);
		securityAlerts.set([...this.alerts]);

		// Emit to listeners
		this.alertListeners.forEach(listener => {
			try {
				listener(alert);
			} catch (error) {
				console.error('🔒 SecurityAuditService: Alert listener error:', error);
			}
		});

		console.warn('🚨 Security Alert:', alert);
		return alert;
	}

	/**
	 * Acknowledge security alert
	 * Following Code Complete: Alert lifecycle management
	 */
	acknowledgeAlert(alertId: string): boolean {
		const alert = this.alerts.find(a => a.id === alertId);
		if (alert) {
			alert.acknowledged = true;
			securityAlerts.set([...this.alerts]);
			return true;
		}
		return false;
	}

	/**
	 * Get security metrics
	 * Following Code Complete: Metrics calculation, performance monitoring
	 */
	getMetrics(): SecurityMetrics {
		const now = Date.now();
		const recentEvents = this.events.filter(e => now - e.timestamp < SECURITY_CONFIG.anomalyDetectionWindow);

		const eventsByType = this.events.reduce((acc, event) => {
			acc[event.type] = (acc[event.type] || 0) + 1;
			return acc;
		}, {} as Record<SecurityEventType, number>);

		const eventsBySeverity = this.events.reduce((acc, event) => {
			acc[event.severity] = (acc[event.severity] || 0) + 1;
			return acc;
		}, { low: 0, medium: 0, high: 0, critical: 0 });

		const failedLogins = eventsByType.authentication_failure || 0;
		const suspiciousActivities = eventsByType.suspicious_activity || 0;

		// Calculate security score (100 = perfect, 0 = critical)
		let securityScore = 100;
		securityScore -= Math.min(failedLogins * 2, 20);
		securityScore -= Math.min(suspiciousActivities * 5, 30);
		securityScore -= Math.min(eventsBySeverity.critical * 10, 40);
		securityScore -= Math.min(eventsBySeverity.high * 3, 20);

		const activeUsers = new Set(
			recentEvents
				.filter(e => e.userId)
				.map(e => e.userId)
		).size;

		return {
			totalEvents: this.events.length,
			eventsByType,
			eventsBySeverity,
			failedLogins,
			suspiciousActivities,
			lastActivity: this.events.length > 0 ? Math.max(...this.events.map(e => e.timestamp)) : 0,
			activeUsers,
			securityScore: Math.max(0, securityScore)
		};
	}

	/**
	 * Generate security report
	 * Following Code Complete: Comprehensive reporting, data analysis
	 */
	generateReport(timeRange: number = 24 * 60 * 60 * 1000): {
		summary: SecurityMetrics;
		events: SecurityEvent[];
		alerts: SecurityAlert[];
		recommendations: string[];
	} {
		const now = Date.now();
		const cutoff = now - timeRange;

		const filteredEvents = this.events.filter(e => e.timestamp >= cutoff);
		const filteredAlerts = this.alerts.filter(a => a.timestamp >= cutoff);

		const recommendations: string[] = [];
		const metrics = this.getMetrics();

		// Generate recommendations based on metrics
		if (metrics.failedLogins > SECURITY_CONFIG.alertThresholds.failedLogins) {
			recommendations.push('Consider implementing account lockout policies');
		}

		if (metrics.suspiciousActivities > SECURITY_CONFIG.alertThresholds.suspiciousActivities) {
			recommendations.push('Review suspicious activity patterns and consider additional monitoring');
		}

		if (metrics.securityScore < 70) {
			recommendations.push('Security score is below recommended threshold - review recent events');
		}

		if (filteredAlerts.filter(a => !a.acknowledged).length > 0) {
			recommendations.push('Acknowledge and investigate pending security alerts');
		}

		return {
			summary: metrics,
			events: filteredEvents,
			alerts: filteredAlerts,
			recommendations
		};
	}

	/**
	 * Add event listener
	 * Following Code Complete: Observer pattern, event-driven architecture
	 */
	addEventListener(listener: (event: SecurityEvent) => void): void {
		this.eventListeners.push(listener);
	}

	/**
	 * Add alert listener
	 * Following Code Complete: Observer pattern, alert notification
	 */
	addAlertListener(listener: (alert: SecurityAlert) => void): void {
		this.alertListeners.push(listener);
	}

	/**
	 * Remove event listener
	 * Following Code Complete: Resource cleanup, memory management
	 */
	removeEventListener(listener: (event: SecurityEvent) => void): void {
		const index = this.eventListeners.indexOf(listener);
		if (index > -1) {
			this.eventListeners.splice(index, 1);
		}
	}

	/**
	 * Remove alert listener
	 * Following Code Complete: Resource cleanup, memory management
	 */
	removeAlertListener(listener: (alert: SecurityAlert) => void): void {
		const index = this.alertListeners.indexOf(listener);
		if (index > -1) {
			this.alertListeners.splice(index, 1);
		}
	}

	// ============================================================================
	// PRIVATE METHODS
	// ============================================================================

	/**
	 * Add event to collection
	 * Following Code Complete: Data management, memory optimization
	 */
	private addEvent(event: SecurityEvent): void {
		this.events.push(event);

		// Limit memory usage
		if (this.events.length > SECURITY_CONFIG.maxEventsInMemory) {
			this.events = this.events.slice(-SECURITY_CONFIG.maxEventsInMemory);
		}

		securityEvents.set([...this.events]);
		this.persistData();
	}

	/**
	 * Check for security anomalies
	 * Following Code Complete: Anomaly detection, pattern recognition
	 */
	private checkForAnomalies(event: SecurityEvent): void {
		// Check for brute force attempts
		if (event.type === 'authentication_failure') {
			this.checkBruteForceAttempts(event.details.email);
		}

		// Check for rapid successive failures
		if (event.severity === 'high' || event.severity === 'critical') {
			const recentHighSeverityEvents = this.events.filter(e => 
				e.severity === 'high' || e.severity === 'critical'
			).filter(e => 
				Date.now() - e.timestamp < 5 * 60 * 1000 // Last 5 minutes
			);

			if (recentHighSeverityEvents.length >= 3) {
				this.createAlert(
					'pattern',
					'Multiple high-severity security events detected',
					'critical',
					recentHighSeverityEvents
				);
			}
		}
	}

	/**
	 * Check for brute force attempts
	 * Following Code Complete: Security pattern detection
	 */
	private checkBruteForceAttempts(email: string): void {
		const recentFailures = this.events.filter(e => 
			e.type === 'authentication_failure' &&
			e.details.email === email &&
			Date.now() - e.timestamp < 15 * 60 * 1000 // Last 15 minutes
		);

		if (recentFailures.length >= SECURITY_CONFIG.alertThresholds.failedLogins) {
			this.createAlert(
				'threshold',
				`Potential brute force attack detected for ${email}`,
				'high',
				recentFailures
			);
		}
	}

	/**
	 * Update security metrics
	 * Following Code Complete: Metrics calculation, performance monitoring
	 */
	private updateMetrics(): void {
		const metrics = this.getMetrics();
		securityMetrics.set(metrics);
	}

	/**
	 * Start metrics collection timer
	 * Following Code Complete: Periodic monitoring, automated reporting
	 */
	private startMetricsCollection(): void {
		if (this.metricsTimer) {
			clearInterval(this.metricsTimer);
		}

		this.metricsTimer = setInterval(() => {
			this.updateMetrics();
			this.cleanupOldEvents();
		}, SECURITY_CONFIG.reportingInterval);
	}

	/**
	 * Cleanup old events
	 * Following Code Complete: Data retention, memory management
	 */
	private cleanupOldEvents(): void {
		const cutoff = Date.now() - SECURITY_CONFIG.retentionPeriod;
		this.events = this.events.filter(e => e.timestamp >= cutoff);
		this.alerts = this.alerts.filter(a => a.timestamp >= cutoff);

		securityEvents.set([...this.events]);
		securityAlerts.set([...this.alerts]);
	}

	/**
	 * Generate unique event ID
	 * Following Code Complete: Unique identification, traceability
	 */
	private generateEventId(): string {
		return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * Generate unique alert ID
	 * Following Code Complete: Unique identification, traceability
	 */
	private generateAlertId(): string {
		return `alt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * Persist data to localStorage
	 * Following Code Complete: Data persistence, error handling
	 */
	private persistData(): void {
		if (!browser || typeof localStorage === 'undefined') return;

		try {
			localStorage.setItem('security_events', JSON.stringify(this.events.slice(-100))); // Keep last 100
			localStorage.setItem('security_alerts', JSON.stringify(this.alerts));
		} catch (error) {
			console.warn('🔒 SecurityAuditService: Failed to persist data:', error);
		}
	}

	/**
	 * Load persisted data from localStorage
	 * Following Code Complete: Data recovery, error handling
	 */
	private loadPersistedData(): void {
		if (!browser || typeof localStorage === 'undefined') return;

		try {
			const eventsData = localStorage.getItem('security_events');
			const alertsData = localStorage.getItem('security_alerts');

			if (eventsData) {
				this.events = JSON.parse(eventsData);
				securityEvents.set([...this.events]);
			}

			if (alertsData) {
				this.alerts = JSON.parse(alertsData);
				securityAlerts.set([...this.alerts]);
			}
		} catch (error) {
			console.warn('🔒 SecurityAuditService: Failed to load persisted data:', error);
		}
	}
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

/**
 * Global security audit service instance
 * Following Code Complete: Singleton pattern, centralized monitoring
 */
export const securityAudit = new SecurityAuditService();

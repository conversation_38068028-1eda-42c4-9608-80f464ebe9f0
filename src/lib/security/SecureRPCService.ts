/**
 * Secure RPC Service
 * Wrapper for all Supabase RPC calls with automatic token validation
 * Following Code Complete principles: Security-first, comprehensive error handling
 */

import { supabase } from '$lib/supabaseClient.js';
import { validateSecurity, type SecurityValidationResult } from './TokenValidationService.js';
import type { PostgrestError } from '@supabase/supabase-js';

// ============================================================================
// TYPES
// ============================================================================

export interface SecureRPCOptions {
	requireOrganization?: boolean;
	requiredRole?: 'viewer' | 'editor' | 'admin' | 'superadmin';
	timeout?: number;
	retries?: number;
	logCalls?: boolean;
}

export interface SecureRPCResult<T = any> {
	data: T | null;
	error: PostgrestError | Error | null;
	securityContext: SecurityValidationResult;
	executionTime: number;
}

export interface RPCCallLog {
	functionName: string;
	parameters: any;
	userId: string | null;
	organizationId: string | null;
	timestamp: number;
	executionTime: number;
	success: boolean;
	error?: string;
}

// ============================================================================
// CONFIGURATION
// ============================================================================

/**
 * RPC security configuration
 * Following Code Complete: Centralized configuration
 */
const RPC_CONFIG = {
	defaultTimeout: 30000, // 30 seconds
	defaultRetries: 2,
	logAllCalls: true,
	maxParameterSize: 1024 * 1024, // 1MB
	rateLimitWindow: 60000, // 1 minute
	maxCallsPerWindow: 100
};

// Rate limiting storage (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// ============================================================================
// CORE SECURE RPC FUNCTIONS
// ============================================================================

/**
 * Execute RPC function with comprehensive security validation
 * Following Code Complete: Single responsibility, comprehensive validation
 */
export async function secureRPC<T = any>(
	functionName: string,
	parameters: any = {},
	options: SecureRPCOptions = {}
): Promise<SecureRPCResult<T>> {
	const startTime = Date.now();
	
	// Set default options
	const opts: Required<SecureRPCOptions> = {
		requireOrganization: false,
		requiredRole: 'viewer',
		timeout: RPC_CONFIG.defaultTimeout,
		retries: RPC_CONFIG.defaultRetries,
		logCalls: RPC_CONFIG.logAllCalls,
		...options
	};

	// Validate security context
	const securityValidation = await validateSecurity();
	
	if (!securityValidation.isValid) {
		const error = new Error(`Authentication required for RPC call: ${functionName}`);
		return {
			data: null,
			error,
			securityContext: securityValidation,
			executionTime: Date.now() - startTime
		};
	}

	// Check organization requirement
	if (opts.requireOrganization && !securityValidation.organizationContext.organizationId) {
		const error = new Error(`Organization context required for RPC call: ${functionName}`);
		return {
			data: null,
			error,
			securityContext: securityValidation,
			executionTime: Date.now() - startTime
		};
	}

	// Check role requirement
	if (opts.requireOrganization) {
		const hasRole = hasOrganizationRole(securityValidation.organizationContext, opts.requiredRole);
		if (!hasRole) {
			const error = new Error(`Insufficient permissions for RPC call: ${functionName} (required: ${opts.requiredRole})`);
			return {
				data: null,
				error,
				securityContext: securityValidation,
				executionTime: Date.now() - startTime
			};
		}
	}

	// Rate limiting check
	const rateLimitError = checkRateLimit(securityValidation.user?.id || 'anonymous');
	if (rateLimitError) {
		return {
			data: null,
			error: rateLimitError,
			securityContext: securityValidation,
			executionTime: Date.now() - startTime
		};
	}

	// Validate parameters
	const paramValidationError = validateParameters(parameters);
	if (paramValidationError) {
		return {
			data: null,
			error: paramValidationError,
			securityContext: securityValidation,
			executionTime: Date.now() - startTime
		};
	}

	// Execute RPC call with retry logic
	let lastError: Error | null = null;
	
	for (let attempt = 0; attempt <= opts.retries; attempt++) {
		try {
			const result = await executeRPCWithTimeout(functionName, parameters, opts.timeout);
			
			// Log successful call
			if (opts.logCalls) {
				logRPCCall({
					functionName,
					parameters: sanitizeParameters(parameters),
					userId: securityValidation.user?.id || null,
					organizationId: securityValidation.organizationContext.organizationId,
					timestamp: startTime,
					executionTime: Date.now() - startTime,
					success: true
				});
			}

			return {
				data: result.data,
				error: result.error,
				securityContext: securityValidation,
				executionTime: Date.now() - startTime
			};

		} catch (error) {
			lastError = error instanceof Error ? error : new Error('Unknown RPC error');
			
			if (attempt < opts.retries) {
				console.warn(`🔒 SecureRPCService: Retry ${attempt + 1}/${opts.retries} for ${functionName}:`, lastError.message);
				await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1))); // Exponential backoff
			}
		}
	}

	// Log failed call
	if (opts.logCalls) {
		logRPCCall({
			functionName,
			parameters: sanitizeParameters(parameters),
			userId: securityValidation.user?.id || null,
			organizationId: securityValidation.organizationContext.organizationId,
			timestamp: startTime,
			executionTime: Date.now() - startTime,
			success: false,
			error: lastError?.message
		});
	}

	return {
		data: null,
		error: lastError,
		securityContext: securityValidation,
		executionTime: Date.now() - startTime
	};
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Execute RPC call with timeout
 * Following Code Complete: Defensive programming, timeout handling
 */
async function executeRPCWithTimeout(
	functionName: string,
	parameters: any,
	timeout: number
): Promise<{ data: any; error: PostgrestError | null }> {
	const timeoutPromise = new Promise<never>((_, reject) => {
		setTimeout(() => {
			reject(new Error(`RPC call ${functionName} timed out after ${timeout}ms`));
		}, timeout);
	});

	const rpcPromise = supabase.rpc(functionName, parameters);
	
	return Promise.race([rpcPromise, timeoutPromise]);
}

/**
 * Check rate limiting for user
 * Following Code Complete: Security validation, rate limiting
 */
function checkRateLimit(userId: string): Error | null {
	const now = Date.now();
	const userKey = `rpc_${userId}`;
	
	let userLimit = rateLimitStore.get(userKey);
	
	if (!userLimit || now > userLimit.resetTime) {
		// Reset or initialize rate limit
		userLimit = {
			count: 1,
			resetTime: now + RPC_CONFIG.rateLimitWindow
		};
		rateLimitStore.set(userKey, userLimit);
		return null;
	}

	if (userLimit.count >= RPC_CONFIG.maxCallsPerWindow) {
		return new Error(`Rate limit exceeded: ${RPC_CONFIG.maxCallsPerWindow} calls per minute`);
	}

	userLimit.count++;
	rateLimitStore.set(userKey, userLimit);
	return null;
}

/**
 * Validate RPC parameters
 * Following Code Complete: Input validation, security
 */
function validateParameters(parameters: any): Error | null {
	if (!parameters) {
		return null;
	}

	try {
		const serialized = JSON.stringify(parameters);
		if (serialized.length > RPC_CONFIG.maxParameterSize) {
			return new Error(`Parameter size exceeds limit: ${serialized.length} bytes`);
		}
	} catch (error) {
		return new Error('Invalid parameters: cannot serialize');
	}

	return null;
}

/**
 * Sanitize parameters for logging
 * Following Code Complete: Security, data privacy
 */
function sanitizeParameters(parameters: any): any {
	if (!parameters || typeof parameters !== 'object') {
		return parameters;
	}

	const sanitized = { ...parameters };
	
	// Remove sensitive fields
	const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
	
	for (const field of sensitiveFields) {
		if (field in sanitized) {
			sanitized[field] = '[REDACTED]';
		}
	}

	return sanitized;
}

/**
 * Log RPC call for monitoring
 * Following Code Complete: Comprehensive logging, structured data
 */
function logRPCCall(log: RPCCallLog): void {
	const logEntry = {
		...log,
		timestamp: new Date(log.timestamp).toISOString()
	};

	if (log.success) {
		console.log('✅ RPC Call:', JSON.stringify(logEntry));
	} else {
		console.error('❌ RPC Call Failed:', JSON.stringify(logEntry));
	}
}

/**
 * Check if user has required organization role
 * Following Code Complete: Business rule validation
 */
function hasOrganizationRole(
	context: { organizationId: string | null; userRole: string | null; membershipActive: boolean },
	requiredRole: 'viewer' | 'editor' | 'admin' | 'superadmin'
): boolean {
	if (!context.organizationId || !context.membershipActive) {
		return false;
	}

	const roleHierarchy = ['viewer', 'editor', 'admin', 'superadmin'];
	const userRoleIndex = roleHierarchy.indexOf(context.userRole || '');
	const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

	return userRoleIndex >= requiredRoleIndex;
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Execute RPC with organization context requirement
 * Following Code Complete: Convenience wrapper, clear intent
 */
export async function secureOrgRPC<T = any>(
	functionName: string,
	parameters: any = {},
	requiredRole: 'viewer' | 'editor' | 'admin' | 'superadmin' = 'viewer'
): Promise<SecureRPCResult<T>> {
	return secureRPC<T>(functionName, parameters, {
		requireOrganization: true,
		requiredRole
	});
}

/**
 * Execute RPC with admin role requirement
 * Following Code Complete: Convenience wrapper, security-first
 */
export async function secureAdminRPC<T = any>(
	functionName: string,
	parameters: any = {}
): Promise<SecureRPCResult<T>> {
	return secureOrgRPC<T>(functionName, parameters, 'admin');
}

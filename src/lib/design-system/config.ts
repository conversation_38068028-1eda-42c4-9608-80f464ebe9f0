/**
 * Design System Configuration
 * Following Code Complete principles: Centralized configuration, type safety
 */

import type { ThemeConfig, DesignTokens, ResponsiveConfig } from './types.js';

// Design Tokens Configuration
export const designTokens: DesignTokens = {
  typography: {
    fontFamily: {
      primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif'
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      md: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.75rem'
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.5
    }
  },
  colors: {
    primary: {
      'primary': '#3b82f6',
      'primary-dark': '#0068F8',
      'primary-light': '#dbeafe'
    },
    semantic: {
      success: '#10b981',
      warning: '#f59e0b',
      danger: '#ef4444',
      purple: '#8b5cf6',
      teal: '#58CEE1',
      sky: '#7EB3FC'
    },
    text: {
      primary: '#1e293b',
      secondary: '#475569',
      tertiary: '#64748b',
      muted: '#94a3b8'
    },
    background: {
      primary: '#ffffff',
      secondary: '#fffeff',
      tertiary: '#f1f5f9'
    },
    border: {
      standard: '#e2e8f0'
    }
  },
  spacing: {
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem'
  },
  borderRadius: {
    none: '0px',
    small: '4px',
    medium: '8px',
    large: '12px',
    full: '9999px'
  },
  layout: {
    headerHeight: '60px',
    sidebarWidth: '250px'
  },
  transitions: {
    standard: '0.2s ease-in-out'
  }
};

// Responsive Configuration
export const responsiveConfig: ResponsiveConfig = {
  mobile: {
    maxWidth: '768px',
    breakpoint: 768
  },
  tablet: {
    minWidth: '769px',
    maxWidth: '1199px',
    breakpoint: 1200
  },
  desktop: {
    minWidth: '1200px',
    breakpoint: 1200
  }
};

// Complete Theme Configuration
export const themeConfig: ThemeConfig = {
  tokens: designTokens,
  responsive: responsiveConfig,
  accessibility: {
    focusRingColor: designTokens.colors.primary.primary,
    focusRingWidth: '2px',
    focusRingOffset: '2px'
  }
};

// Utility Functions for Design System
export const getColor = (category: keyof typeof designTokens.colors, variant: string): string => {
  const colorGroup = designTokens.colors[category] as Record<string, string>;
  return colorGroup[variant] || colorGroup.primary || '#000000';
};

export const getSpacing = (size: keyof typeof designTokens.spacing): string => {
  return designTokens.spacing[size];
};

export const getFontSize = (size: keyof typeof designTokens.typography.fontSize): string => {
  return designTokens.typography.fontSize[size];
};

export const getBorderRadius = (size: keyof typeof designTokens.borderRadius): string => {
  return designTokens.borderRadius[size];
};

// CSS Custom Property Generators
export const generateCSSCustomProperties = (): string => {
  const properties: string[] = [];
  
  // Typography
  properties.push(`--font-family-primary: ${designTokens.typography.fontFamily.primary};`);
  
  Object.entries(designTokens.typography.fontSize).forEach(([key, value]) => {
    properties.push(`--font-size-${key}: ${value};`);
  });
  
  Object.entries(designTokens.typography.fontWeight).forEach(([key, value]) => {
    properties.push(`--font-weight-${key}: ${value};`);
  });
  
  Object.entries(designTokens.typography.lineHeight).forEach(([key, value]) => {
    properties.push(`--line-height-${key}: ${value};`);
  });
  
  // Colors
  Object.entries(designTokens.colors.primary).forEach(([key, value]) => {
    properties.push(`--color-${key}: ${value};`);
  });
  
  Object.entries(designTokens.colors.semantic).forEach(([key, value]) => {
    properties.push(`--color-${key}: ${value};`);
  });
  
  Object.entries(designTokens.colors.text).forEach(([key, value]) => {
    properties.push(`--color-text-${key}: ${value};`);
  });
  
  Object.entries(designTokens.colors.background).forEach(([key, value]) => {
    properties.push(`--color-bg-${key}: ${value};`);
  });
  
  Object.entries(designTokens.colors.border).forEach(([key, value]) => {
    properties.push(`--color-border-${key}: ${value};`);
  });
  
  // Spacing
  Object.entries(designTokens.spacing).forEach(([key, value]) => {
    properties.push(`--spacing-${key}: ${value};`);
  });
  
  // Border Radius
  Object.entries(designTokens.borderRadius).forEach(([key, value]) => {
    properties.push(`--radius-${key}: ${value};`);
  });
  
  // Layout
  properties.push(`--header-height: ${designTokens.layout.headerHeight};`);
  properties.push(`--sidebar-width: ${designTokens.layout.sidebarWidth};`);
  
  // Transitions
  properties.push(`--transition-standard: ${designTokens.transitions.standard};`);
  
  return properties.join('\n  ');
};

// Export default theme
export default themeConfig;

/**
 * Design System Type Definitions
 * Following Code Complete principles: Type safety, clear interfaces
 */

// Typography Types
export type FontSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
export type FontWeight = 'normal' | 'medium' | 'semibold' | 'bold';
export type LineHeight = 'tight' | 'normal';

// Color Types
export type PrimaryColor = 'primary' | 'primary-dark' | 'primary-light';
export type SemanticColor = 'success' | 'warning' | 'danger' | 'purple' | 'teal' | 'sky';
export type TextColor = 'primary' | 'secondary' | 'tertiary' | 'muted';
export type BackgroundColor = 'primary' | 'secondary' | 'tertiary';
export type BorderColor = 'standard';

// Spacing Types
export type Spacing = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// Border Radius Types
export type BorderRadius = 'none' | 'small' | 'medium' | 'large' | 'full';

// Component Variant Types
export type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'ghost' | 'success' | 'warning' | 'outline';
export type ButtonSize = 'sm' | 'md' | 'lg';

export type InputVariant = 'default' | 'error' | 'success';
export type InputSize = 'sm' | 'md' | 'lg';

export type ModalSize = 'sm' | 'md' | 'lg' | 'xl';

export type BadgeVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'purple';

// Layout Types
export type BreakPoint = 'mobile' | 'tablet' | 'desktop';

// Design Token Interface
export interface DesignTokens {
  typography: {
    fontFamily: {
      primary: string;
    };
    fontSize: Record<FontSize, string>;
    fontWeight: Record<FontWeight, number>;
    lineHeight: Record<LineHeight, number>;
  };
  colors: {
    primary: Record<PrimaryColor, string>;
    semantic: Record<SemanticColor, string>;
    text: Record<TextColor, string>;
    background: Record<BackgroundColor, string>;
    border: Record<BorderColor, string>;
  };
  spacing: Record<Spacing, string>;
  borderRadius: Record<BorderRadius, string>;
  layout: {
    headerHeight: string;
    sidebarWidth: string;
  };
  transitions: {
    standard: string;
  };
}

// Component Props Interfaces
export interface BaseComponentProps {
  className?: string;
  id?: string;
  'data-testid'?: string;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  type?: 'button' | 'submit' | 'reset';
  onclick?: (event: MouseEvent) => void;
  children?: any;
}

export interface InputProps extends BaseComponentProps {
  variant?: InputVariant;
  size?: InputSize;
  type?: string;
  placeholder?: string;
  value?: string;
  disabled?: boolean;
  required?: boolean;
  label?: string;
  error?: string;
  helpText?: string;
  onchange?: (event: Event) => void;
  oninput?: (event: Event) => void;
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  title?: string;
  size?: ModalSize;
  closeOnBackdrop?: boolean;
  onclose?: () => void;
  onClose?: () => void;
  children?: any;
}

export interface BadgeProps extends BaseComponentProps {
  variant?: BadgeVariant;
  children?: any;
}

// Responsive Design Types
export interface ResponsiveConfig {
  mobile: {
    maxWidth: string;
    breakpoint: number;
  };
  tablet: {
    minWidth: string;
    maxWidth: string;
    breakpoint: number;
  };
  desktop: {
    minWidth: string;
    breakpoint: number;
  };
}

// Theme Configuration
export interface ThemeConfig {
  tokens: DesignTokens;
  responsive: ResponsiveConfig;
  accessibility: {
    focusRingColor: string;
    focusRingWidth: string;
    focusRingOffset: string;
  };
}

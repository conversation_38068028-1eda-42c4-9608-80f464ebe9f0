/**
 * Design System Utility Functions
 * Following Code Complete principles: Pure functions, clear purpose, reusability
 */

import type { 
  ButtonVariant, 
  ButtonSize, 
  InputVariant, 
  InputSize, 
  BadgeVariant,
  ModalSize 
} from './types.js';

/**
 * Generate button classes based on variant and size
 * Following Code Complete: Pure function, clear logic separation
 */
export function getButtonClasses(variant: ButtonVariant, size: ButtonSize, fullWidth = false): string {
  const baseClasses = 'button-base focus-ring';

  const variantClasses: Record<ButtonVariant, string> = {
    primary: 'btn-primary',
    secondary: 'btn-secondary',
    danger: 'btn-danger',
    ghost: 'btn-ghost',
    success: 'btn-success',
    warning: 'btn-warning',
    outline: 'btn-outline'
  };

  const sizeClasses: Record<ButtonSize, string> = {
    sm: 'btn-sm',
    md: 'btn-md',
    lg: 'btn-lg'
  };

  const widthClass = fullWidth ? 'w-full' : '';

  return `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass}`.trim();
}

/**
 * Generate input classes based on variant and size
 * Following Code Complete: Pure function, clear logic separation
 */
export function getInputClasses(variant: InputVariant, size: InputSize, hasError = false): string {
  const baseClasses = 'input-base';
  
  const variantClasses: Record<InputVariant, string> = {
    default: 'border-border-standard',
    error: 'border-danger focus:border-danger focus:ring-danger-light',
    success: 'border-success focus:border-success focus:ring-success-light'
  };
  
  const sizeClasses: Record<InputSize, string> = {
    sm: 'px-2 py-1 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };
  
  const errorClass = hasError ? 'border-danger focus:border-danger focus:ring-danger-light' : '';
  
  return `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${errorClass}`.trim();
}

/**
 * Generate badge classes based on variant
 * Following Code Complete: Pure function, clear logic separation
 */
export function getBadgeClasses(variant: BadgeVariant): string {
  const baseClasses = 'badge-base';
  
  const variantClasses: Record<BadgeVariant, string> = {
    primary: 'bg-primary-light text-primary',
    secondary: 'bg-gray-100 text-gray-800',
    success: 'bg-success-light text-success',
    warning: 'bg-warning-light text-warning',
    danger: 'bg-danger-light text-danger',
    purple: 'bg-purple-light text-purple'
  };
  
  return `${baseClasses} ${variantClasses[variant]}`;
}

/**
 * Generate modal size classes
 * Following Code Complete: Pure function, clear logic separation
 */
export function getModalSizeClasses(size: ModalSize): string {
  const sizeClasses: Record<ModalSize, string> = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl'
  };
  
  return sizeClasses[size];
}

/**
 * Generate card classes with optional variants
 * Following Code Complete: Pure function, clear logic separation
 */
export function getCardClasses(padding = true, border = true, shadow = false): string {
  const baseClasses = 'card-base';
  const paddingClass = padding ? '' : '!p-0';
  const borderClass = border ? '' : '!border-0';
  const shadowClass = shadow ? 'shadow-sm' : '';
  
  return `${baseClasses} ${paddingClass} ${borderClass} ${shadowClass}`.trim();
}

/**
 * Generate responsive classes for different breakpoints
 * Following Code Complete: Pure function, clear logic separation
 */
export function getResponsiveClasses(
  mobile: string = '',
  tablet: string = '',
  desktop: string = ''
): string {
  const classes: string[] = [];
  
  if (mobile) classes.push(mobile);
  if (tablet) classes.push(`md:${tablet}`);
  if (desktop) classes.push(`lg:${desktop}`);
  
  return classes.join(' ');
}

/**
 * Generate text classes based on design system typography
 * Following Code Complete: Pure function, clear logic separation
 */
export function getTextClasses(
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' = 'md',
  weight: 'normal' | 'medium' | 'semibold' | 'bold' = 'normal',
  color: 'primary' | 'secondary' | 'tertiary' | 'muted' = 'primary'
): string {
  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-md',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl'
  };
  
  const weightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold'
  };
  
  const colorClasses = {
    primary: 'text-text-primary',
    secondary: 'text-text-secondary',
    tertiary: 'text-text-tertiary',
    muted: 'text-text-muted'
  };
  
  return `${sizeClasses[size]} ${weightClasses[weight]} ${colorClasses[color]}`;
}

/**
 * Generate spacing classes
 * Following Code Complete: Pure function, clear logic separation
 */
export function getSpacingClasses(
  margin?: 'xs' | 'sm' | 'md' | 'lg' | 'xl',
  padding?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
): string {
  const classes: string[] = [];
  
  if (margin) {
    const marginMap = {
      xs: 'm-xs',
      sm: 'm-sm',
      md: 'm-md',
      lg: 'm-lg',
      xl: 'm-xl'
    };
    classes.push(marginMap[margin]);
  }
  
  if (padding) {
    const paddingMap = {
      xs: 'p-xs',
      sm: 'p-sm',
      md: 'p-md',
      lg: 'p-lg',
      xl: 'p-xl'
    };
    classes.push(paddingMap[padding]);
  }
  
  return classes.join(' ');
}

/**
 * Combine multiple class strings safely
 * Following Code Complete: Utility function, handles edge cases
 */
export function combineClasses(...classes: (string | undefined | null | false)[]): string {
  return classes
    .filter(Boolean)
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Check if current viewport matches breakpoint
 * Following Code Complete: Pure function, clear purpose
 */
export function matchesBreakpoint(breakpoint: 'mobile' | 'tablet' | 'desktop'): boolean {
  if (typeof window === 'undefined') return false;
  
  const width = window.innerWidth;
  
  switch (breakpoint) {
    case 'mobile':
      return width <= 768;
    case 'tablet':
      return width > 768 && width < 1200;
    case 'desktop':
      return width >= 1200;
    default:
      return false;
  }
}

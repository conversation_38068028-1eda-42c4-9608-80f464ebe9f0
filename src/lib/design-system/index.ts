/**
 * Design System Entry Point
 * Following Code Complete principles: Clear module organization, single entry point
 */

// Export types
export type {
  FontSize,
  FontWeight,
  LineHeight,
  PrimaryColor,
  SemanticColor,
  TextColor,
  BackgroundColor,
  BorderColor,
  Spacing,
  BorderRadius,
  ButtonVariant,
  ButtonSize,
  InputVariant,
  InputSize,
  ModalSize,
  BadgeVariant,
  BreakPoint,
  DesignTokens,
  BaseComponentProps,
  ButtonProps,
  InputProps,
  ModalProps,
  BadgeProps,
  ResponsiveConfig,
  ThemeConfig
} from './types.js';

// Export configuration
export {
  designTokens,
  responsiveConfig,
  themeConfig,
  getColor,
  getSpacing,
  getFontSize,
  getBorderRadius,
  generateCSSCustomProperties
} from './config.js';

// Export utilities
export {
  getButtonClasses,
  getInputClasses,
  getBadgeClasses,
  getModalSizeClasses,
  getCardClasses,
  getResponsiveClasses,
  getTextClasses,
  getSpacingClasses,
  combineClasses,
  matchesBreakpoint
} from './utils.js';

// Design System Constants
export const DESIGN_SYSTEM_VERSION = '1.0.0';

export const BREAKPOINTS = {
  mobile: 768,
  tablet: 1200,
  desktop: 1200
} as const;

export const COMPONENT_DEFAULTS = {
  button: {
    variant: 'primary' as const,
    size: 'md' as const
  },
  input: {
    variant: 'default' as const,
    size: 'md' as const
  },
  modal: {
    size: 'md' as const,
    closeOnBackdrop: true
  },
  badge: {
    variant: 'primary' as const
  }
} as const;

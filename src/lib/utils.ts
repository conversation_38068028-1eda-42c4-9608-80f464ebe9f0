// Utility functions for the restaurant scheduling system

import type {
	Shift,
	Employee,
	WeeklySummary,
	EmployeeWeeklySummary,
	VenueSummary,
	ShiftStatus,
	TimeSlot,
	ValidationResult,
	ValidationError
} from './types';

// Date utilities
export function getWeekStart(date: Date): Date {
	const d = new Date(date);
	const day = d.getDay();
	const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
	return new Date(d.setDate(diff));
}

export function getWeekDates(weekStart: Date): Date[] {
	const dates: Date[] = [];
	for (let i = 0; i < 7; i++) {
		const date = new Date(weekStart);
		date.setDate(weekStart.getDate() + i);
		dates.push(date);
	}
	return dates;
}

export function formatDate(date: Date): string {
	return date.toISOString().split('T')[0];
}

export function formatDateDisplay(date: Date): string {
	return date.toLocaleDateString('en-US', {
		weekday: 'short',
		month: 'short',
		day: 'numeric'
	});
}

/**
 * Format week range in the format "26 May - 1 Jun"
 * Following Code Complete: Clear, user-friendly date formatting
 */
export function formatWeekRange(weekStart: Date): string {
	const weekEnd = new Date(weekStart);
	weekEnd.setDate(weekStart.getDate() + 6);

	const startDay = weekStart.getDate();
	const endDay = weekEnd.getDate();

	const startMonth = weekStart.toLocaleDateString('en-US', { month: 'short' });
	const endMonth = weekEnd.toLocaleDateString('en-US', { month: 'short' });

	// If same month, show "26 - 31 May"
	if (startMonth === endMonth) {
		return `${startDay} - ${endDay} ${startMonth}`;
	}

	// If different months, show "26 May - 1 Jun"
	return `${startDay} ${startMonth} - ${endDay} ${endMonth}`;
}

export function isSameDate(date1: Date, date2: Date): boolean {
	return formatDate(date1) === formatDate(date2);
}

// Time utilities
export function parseTime(timeString: string): { hours: number; minutes: number } {
	// Handle undefined/null timeString
	if (!timeString || typeof timeString !== 'string') {
		return { hours: 0, minutes: 0 };
	}

	const [hours, minutes] = timeString.split(':').map(Number);
	return {
		hours: isNaN(hours) ? 0 : hours,
		minutes: isNaN(minutes) ? 0 : minutes
	};
}

export function formatTime(hours: number, minutes: number): string {
	return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

export function calculateHours(startTime: string, endTime: string): number {
	// Handle undefined/null times
	if (!startTime || !endTime) {
		return 0;
	}

	const start = parseTime(startTime);
	const end = parseTime(endTime);

	let startMinutes = start.hours * 60 + start.minutes;
	let endMinutes = end.hours * 60 + end.minutes;

	// Handle overnight shifts
	if (endMinutes < startMinutes) {
		endMinutes += 24 * 60;
	}

	return (endMinutes - startMinutes) / 60;
}

export function timeToMinutes(timeString: string): number {
	const { hours, minutes } = parseTime(timeString);
	return hours * 60 + minutes;
}

export function minutesToTime(minutes: number): string {
	const hours = Math.floor(minutes / 60);
	const mins = minutes % 60;
	return formatTime(hours, mins);
}

// Shift utilities
export function getShiftStatus(shift: Shift): ShiftStatus {
	if (!shift.employeeId) return 'unassigned' as ShiftStatus;
	if (shift.isPaid) return 'paid' as ShiftStatus;
	return 'scheduled' as ShiftStatus;
}

export function getShiftDuration(shift: Shift): number {
	// Handle missing shift data
	if (!shift) {
		return 0;
	}

	// Use total_hours if available, otherwise calculate from times
	if (shift.totalHours && shift.totalHours > 0) {
		return shift.totalHours;
	}

	// Calculate from start/end times with safety checks
	if (shift.startTime && shift.endTime) {
		return calculateHours(shift.startTime, shift.endTime);
	}

	return 0;
}

/**
 * DEPRECATED: Calculate shift pay based on employee context and business rules
 * ⚠️ WARNING: This function is deprecated and should not be used for new code
 * Use serverSideCalculationService.calculateShiftCompensation() instead
 *
 * This function remains only for backward compatibility during migration
 * Following Code Complete: Graceful deprecation with clear migration path
 */
export function getShiftPay(shift: Shift, employee?: Employee): number {
	console.warn('⚠️ DEPRECATED: getShiftPay() called. Use serverSideCalculationService.calculateShiftCompensation() instead');

	const hours = getShiftDuration(shift);

	// FIXED: Use shift's daily rate as total compensation (not multiplied by hours)
	// This was the critical bug causing inflated pay calculations
	if (!employee) {
		return shift.dailyRate; // ✅ CORRECT: dailyRate is total shift compensation
	}

	// Determine if this is a full-day shift
	const fullDayThreshold = employee.defaultDailyWorkingHours || 8;
	const isFullDay = hours >= fullDayThreshold;

	if (isFullDay) {
		// Use daily rate for full-day shifts
		return employee.defaultDailyRate;
	} else {
		// Use hourly rate for partial shifts
		const hourlyRate = employee.defaultHourlyRate || (employee.defaultDailyRate / fullDayThreshold);
		return hours * hourlyRate;
	}
}

export function getShiftNetPay(shift: Shift, employee?: Employee): number {
	return getShiftPay(shift, employee) - shift.advanceDeduction;
}

/**
 * DEPRECATED: Calculate employee weekly summary
 * ⚠️ WARNING: This function is deprecated and should not be used for new code
 * Use serverSideCalculationService.calculateEmployeeWeeklyTotals() instead
 *
 * This function remains only for backward compatibility during migration
 * Following Code Complete: Graceful deprecation with clear migration path
 */
export function calculateEmployeeWeeklySummary(
	employee: Employee,
	shifts: Shift[]
): EmployeeWeeklySummary {
	console.warn('⚠️ DEPRECATED: calculateEmployeeWeeklySummary() called. Use serverSideCalculationService.calculateEmployeeWeeklyTotals() instead');

	const employeeShifts = shifts.filter(s => s.employeeId === employee.id);

	const totalHours = employeeShifts.reduce((sum, shift) => sum + getShiftDuration(shift), 0);
	const totalPay = employeeShifts.reduce((sum, shift) => sum + getShiftPay(shift, employee), 0);
	const paidAmount = employeeShifts
		.filter(shift => shift.isPaid)
		.reduce((sum, shift) => sum + getShiftPay(shift, employee), 0);
	const unpaidAmount = totalPay - paidAmount;
	const advanceDeductions = employeeShifts.reduce((sum, shift) => sum + shift.advanceDeduction, 0);

	return {
		employeeId: employee.id,
		employeeName: employee.name || employee.fullName || 'Unknown Employee',
		shiftsCount: employeeShifts.length,
		totalHours,
		totalPay,
		paidAmount,
		unpaidAmount,
		advanceDeductions
	};
}

/**
 * DEPRECATED: Calculate venue summary
 * ⚠️ WARNING: This function is deprecated and should not be used for new code
 * Use serverSideCalculationService.calculateVenueWeeklyTotals() instead
 *
 * This function remains only for backward compatibility during migration
 * Following Code Complete: Graceful deprecation with clear migration path
 */
export function calculateVenueSummary(
	venueId: string,
	venueName: string,
	shifts: Shift[],
	employees: Employee[] = []
): VenueSummary {
	console.warn('⚠️ DEPRECATED: calculateVenueSummary() called. Use serverSideCalculationService.calculateVenueWeeklyTotals() instead');

	const venueShifts = shifts.filter(s => s.venueId === venueId);

	const totalHours = venueShifts.reduce((sum, shift) => sum + getShiftDuration(shift), 0);

	// Use employee-aware pay calculation for consistency
	const totalPay = venueShifts.reduce((sum, shift) => {
		const employee = employees.find(emp => emp.id === shift.employeeId);
		return sum + getShiftPay(shift, employee);
	}, 0);

	const paidAmount = venueShifts
		.filter(shift => shift.isPaid)
		.reduce((sum, shift) => {
			const employee = employees.find(emp => emp.id === shift.employeeId);
			return sum + getShiftPay(shift, employee);
		}, 0);

	const unpaidAmount = totalPay - paidAmount;
	const advanceDeductions = venueShifts.reduce((sum, shift) => sum + shift.advanceDeduction, 0);

	return {
		venueId,
		venueName,
		shiftsCount: venueShifts.length,
		totalHours,
		totalPay,
		paidAmount,
		unpaidAmount,
		advanceDeductions
	};
}

/**
 * DEPRECATED: Calculate weekly summary
 * ⚠️ WARNING: This function is deprecated and should not be used for new code
 * Use serverSideCalculationService.calculateOverallWeeklySummary() instead
 *
 * This function remains only for backward compatibility during migration
 * Following Code Complete: Graceful deprecation with clear migration path
 */
export function calculateWeeklySummary(
	employees: Employee[],
	shifts: Shift[],
	venues: { id: string; name: string }[]
): WeeklySummary {
	console.warn('⚠️ DEPRECATED: calculateWeeklySummary() called. Use serverSideCalculationService.calculateOverallWeeklySummary() instead');

	const employeeSummaries = employees.map(emp => calculateEmployeeWeeklySummary(emp, shifts));
	const venueSummaries = venues.map(venue => calculateVenueSummary(venue.id, venue.name, shifts, employees));

	const totalHours = shifts.reduce((sum, shift) => sum + getShiftDuration(shift), 0);

	// Use employee-aware pay calculation for consistency
	const totalPay = shifts.reduce((sum, shift) => {
		const employee = employees.find(emp => emp.id === shift.employeeId);
		return sum + getShiftPay(shift, employee);
	}, 0);

	const paidAmount = shifts
		.filter(shift => shift.isPaid)
		.reduce((sum, shift) => {
			const employee = employees.find(emp => emp.id === shift.employeeId);
			return sum + getShiftPay(shift, employee);
		}, 0);

	const unpaidAmount = totalPay - paidAmount;
	const advanceDeductions = shifts.reduce((sum, shift) => sum + shift.advanceDeduction, 0);
	const unassignedShifts = shifts.filter(shift => !shift.employeeId).length;

	return {
		totalHours,
		totalPay,
		paidAmount,
		unpaidAmount,
		advanceDeductions,
		employeeSummaries,
		venueSummaries,
		unassignedShifts
	};
}

// Validation utilities
export function validateShift(shift: Partial<Shift>): ValidationResult {
	const errors: ValidationError[] = [];

	// Note: employeeId can be null for unassigned shifts - this is valid

	if (!shift.venueId) {
		errors.push({ field: 'venueId', message: 'Venue is required' });
	}

	if (!shift.date) {
		errors.push({ field: 'date', message: 'Date is required' });
	}

	if (!shift.startTime) {
		errors.push({ field: 'startTime', message: 'Start time is required' });
	}

	if (!shift.endTime) {
		errors.push({ field: 'endTime', message: 'End time is required' });
	}

	if (shift.startTime && shift.endTime) {
		const duration = calculateHours(shift.startTime, shift.endTime);
		if (duration <= 0) {
			errors.push({ field: 'endTime', message: 'End time must be after start time' });
		}
		if (duration > 24) {
			errors.push({ field: 'endTime', message: 'Shift cannot exceed 24 hours' });
		}
	}

	if (shift.dailyRate !== undefined && shift.dailyRate < 0) {
		errors.push({ field: 'dailyRate', message: 'Daily rate must be positive' });
	}

	if (shift.advanceDeduction !== undefined && shift.advanceDeduction < 0) {
		errors.push({ field: 'advanceDeduction', message: 'Advance deduction cannot be negative' });
	}

	return {
		isValid: errors.length === 0,
		errors
	};
}

export function validateEmployee(employee: Partial<Employee>): ValidationResult {
	const errors: ValidationError[] = [];

	if (!employee.name || employee.name.trim().length === 0) {
		errors.push({ field: 'name', message: 'Name is required' });
	}

	if (employee.defaultDailyRate !== undefined && employee.defaultDailyRate < 0) {
		errors.push({ field: 'defaultDailyRate', message: 'Daily rate must be positive' });
	}

	if (!employee.role || employee.role.trim().length === 0) {
		errors.push({ field: 'role', message: 'Role is required' });
	}

	return {
		isValid: errors.length === 0,
		errors
	};
}

// Color utilities for UI
export function getShiftStatusColor(status: ShiftStatus): string {
	switch (status) {
		case 'unassigned':
			return 'bg-gray-200 border-gray-300 text-gray-700';
		case 'scheduled':
			return 'bg-blue-100 border-blue-300 text-blue-800';
		case 'paid':
			return 'bg-green-100 border-green-300 text-green-800';
		default:
			return 'bg-gray-200 border-gray-300 text-gray-700';
	}
}

// Currency formatting - Bulgarian Lev (BGN)
export function formatCurrency(amount: number): string {
	// Format: "133 650,47 лв" (space as thousands separator, comma as decimal separator, "лв" suffix)
	return new Intl.NumberFormat('bg-BG', {
		style: 'currency',
		currency: 'BGN',
		currencyDisplay: 'code'
	}).format(amount).replace('BGN', 'лв');
}

// ID generation
export function generateId(): string {
	return Math.random().toString(36).substr(2, 9);
}

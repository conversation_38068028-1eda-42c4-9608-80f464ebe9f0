/**
 * Authentication Store
 * Manages user authentication state, session management, and restaurant context
 * Following Code Complete principles: Single responsibility, clear interfaces
 */

import { writable, derived, get } from 'svelte/store';
import type { AuthState, User, AuthSession, Restaurant, UserRestaurant } from '$lib/types.js';
import { supabase } from '$lib/supabaseClient.js';
import type { Session } from '@supabase/supabase-js';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialAuthState: AuthState = {
	user: null,
	session: null,
	currentRestaurant: null,
	userRestaurants: [],
	isLoading: true,
	isAuthenticated: false,
	needsRestaurantSetup: false
};

// ============================================================================
// STORES
// ============================================================================

export const authState = writable<AuthState>(initialAuthState);

// Derived stores for convenient access
export const user = derived(authState, ($authState) => $authState.user);
export const isAuthenticated = derived(authState, ($authState) => $authState.isAuthenticated);
export const isLoading = derived(authState, ($authState) => $authState.isLoading);
export const currentRestaurant = derived(authState, ($authState) => $authState.currentRestaurant);
export const userRestaurants = derived(authState, ($authState) => $authState.userRestaurants);

// ============================================================================
// AUTH ACTIONS
// ============================================================================

// Track if auth has been initialized to prevent double initialization
let authInitialized = false;

/**
 * Initialize authentication state from Supabase session
 * ULTRA SIMPLIFIED VERSION - just check if user exists
 */
export async function initializeAuth(): Promise<void> {
	if (authInitialized) {
		return;
	}
	authInitialized = true;

	try {
		// Add timeout to prevent infinite loading
		const timeoutPromise = new Promise<never>((_, reject) => {
			setTimeout(() => {
				reject(new Error('Authentication initialization timed out after 10 seconds'));
			}, 10000);
		});

		// Check for existing session first with timeout
		const sessionPromise = supabase.auth.getSession();
		const { data: { session }, error } = await Promise.race([sessionPromise, timeoutPromise]);

		if (error) {
			console.error('❌ initializeAuth: Error getting session:', error);
			authState.update(state => ({ ...state, isLoading: false, isAuthenticated: false }));
			return;
		}

		if (session?.user) {
			await setAuthenticatedUser(session);
		} else {
			authState.update(state => ({
				...state,
				isLoading: false,
				isAuthenticated: false,
				needsRestaurantSetup: false,
				user: null,
				session: null,
				currentRestaurant: null,
				userRestaurants: []
			}));
		}

		// Set up auth state change listener for future changes
		supabase.auth.onAuthStateChange(async (event, session) => {

			if (event === 'SIGNED_IN' && session?.user) {
				await setAuthenticatedUser(session);
			} else if (event === 'SIGNED_OUT') {
				clearAuthState();
				authInitialized = false; // Allow re-initialization after sign out
			} else if (event === 'TOKEN_REFRESHED' && session?.user) {
				// Update session but don't reload everything
				const currentState = get(authState);
				if (currentState.isAuthenticated) {
					authState.update(state => ({
						...state,
						session: {
							user: {
								id: session.user.id,
								email: session.user.email!,
								created_at: session.user.created_at,
								updated_at: session.user.updated_at
							},
							access_token: session.access_token,
							refresh_token: session.refresh_token!,
							expires_at: session.expires_at!
						}
					}));
				}
			}
		});



	} catch (error) {
		console.error('❌ Failed to initialize auth:', error);

		authState.update(state => ({
			...state,
			isLoading: false,
			isAuthenticated: false,
			needsRestaurantSetup: false,
			user: null,
			session: null,
			currentRestaurant: null,
			userRestaurants: []
		}));
		authInitialized = false; // Allow retry on error
	}
}

/**
 * Set authenticated user and load their restaurant data
 */
async function setAuthenticatedUser(session: Session): Promise<void> {
	try {

		const user: User = {
			id: session.user.id,
			email: session.user.email!,
			created_at: session.user.created_at,
			updated_at: session.user.updated_at
		};

		const authSession: AuthSession = {
			user,
			access_token: session.access_token,
			refresh_token: session.refresh_token!,
			expires_at: session.expires_at!
		};



		// Load user's organizations using the organization service
		try {


			// Add timeout to prevent hanging on RPC calls
			const timeoutPromise = new Promise<never>((_, reject) => {
				setTimeout(() => {
					reject(new Error('get_user_organizations RPC timed out after 5 seconds'));
				}, 5000);
			});

			const rpcPromise = supabase.rpc('get_user_organizations');
			const { data: organizations, error } = await Promise.race([rpcPromise, timeoutPromise]);

			if (error) {
				console.error('❌ setAuthenticatedUser: Failed to load organizations:', error);
				// Fall back to mock organization for development

				await setMockOrganizationContext(user, authSession);
				return;
			}

			if (!organizations || organizations.length === 0) {
				authState.update(state => ({
					...state,
					user,
					session: authSession,
					currentRestaurant: null,
					userRestaurants: [],
					isLoading: false,
					isAuthenticated: true,
					needsRestaurantSetup: true
				}));

				return;
			}

			// Use the first active organization
			const currentOrg = organizations[0];
			const currentRestaurant: Restaurant = {
				id: currentOrg.id,
				name: currentOrg.name,
				owner_id: currentOrg.owner_user_id,
				created_at: currentOrg.created_at,
				updated_at: currentOrg.updated_at
			};



			authState.update(state => ({
				...state,
				user,
				session: authSession,
				currentRestaurant,
				userRestaurants: [], // Will be populated later if needed
				isLoading: false,
				isAuthenticated: true,
				needsRestaurantSetup: false
			}));

		} catch (orgError) {
			console.error('❌ setAuthenticatedUser: Organization loading failed:', orgError);
			// Fall back to mock organization for development
			await setMockOrganizationContext(user, authSession);
		}


	} catch (error) {
		console.error('❌ Failed to set authenticated user:', error);
		authState.update(state => ({
			...state,
			isLoading: false,
			isAuthenticated: false
		}));
	}
}

/**
 * Set mock organization context for development
 */
async function setMockOrganizationContext(user: User, authSession: AuthSession): Promise<void> {

	// Use the real organization ID from your database
	const mockRestaurant: Restaurant = {
		id: '1101f4b7-7e64-45b9-b690-de9a451426a4', // Real organization ID from database
		name: 'Default Restaurant',
		owner_id: user.id,
		created_at: new Date().toISOString(),
		updated_at: new Date().toISOString()
	};

	authState.update(state => ({
		...state,
		user,
		session: authSession,
		currentRestaurant: mockRestaurant,
		userRestaurants: [],
		isLoading: false,
		isAuthenticated: true,
		needsRestaurantSetup: false
	}));


}

/**
 * SIMPLIFIED: Set authenticated user without restaurant management
 */
async function setAuthenticatedUserSimplified(session: Session): Promise<void> {
	try {

		const user: User = {
			id: session.user.id,
			email: session.user.email!,
			created_at: session.user.created_at,
			updated_at: session.user.updated_at
		};

		const authSession: AuthSession = {
			user,
			access_token: session.access_token,
			refresh_token: session.refresh_token!,
			expires_at: session.expires_at!
		};

		// Use the real restaurant ID from your database
		const mockRestaurant: Restaurant = {
			id: '1101f4b7-7e64-45b9-b690-de9a451426a4', // Real restaurant ID from employees
			name: 'Test Restaurant',
			owner_id: user.id,
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString()
		};

		// Force update the auth state
		authState.set({
			user,
			session: authSession,
			currentRestaurant: mockRestaurant,
			userRestaurants: [],
			isLoading: false,
			isAuthenticated: true,
			needsRestaurantSetup: false
		});

	} catch (error) {
		console.error('❌ Failed to set authenticated user (simplified):', error);
		authState.update(state => ({
			...state,
			isLoading: false,
			isAuthenticated: false
		}));
	}
}

/**
 * Clear authentication state
 */
function clearAuthState(): void {
	authState.set({
		...initialAuthState,
		isLoading: false
	});
}

/**
 * Sign up new user
 */
export async function signUp(email: string, password: string): Promise<{ success: boolean; error?: string }> {
	try {
		const { data, error } = await supabase.auth.signUp({
			email,
			password
		});

		if (error) {
			return { success: false, error: error.message };
		}

		return { success: true };
	} catch (error) {
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Failed to sign up'
		};
	}
}

/**
 * Sign in existing user
 */
export async function signIn(email: string, password: string): Promise<{ success: boolean; error?: string }> {
	try {
		const { data, error } = await supabase.auth.signInWithPassword({
			email,
			password
		});

		if (error) {
			console.error('❌ signIn: Supabase error:', error);
			return { success: false, error: error.message };
		}

		return { success: true };
	} catch (error) {
		console.error('❌ signIn: Unexpected error:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Failed to sign in'
		};
	}
}

/**
 * Sign out current user
 */
export async function signOut(): Promise<void> {
	try {
		const { error } = await supabase.auth.signOut();
		if (error) {
			console.error('❌ Error signing out:', error);
		}
	} catch (error) {
		console.error('❌ Failed to sign out:', error);
	}
}

/**
 * Switch to different restaurant context
 */
export async function switchRestaurant(restaurantId: string): Promise<void> {
	try {
		const restaurant = await loadRestaurant(restaurantId);
		authState.update(state => ({
			...state,
			currentRestaurant: restaurant
		}));
	} catch (error) {
		console.error('❌ Failed to switch restaurant:', error);
	}
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Load user's restaurant associations
 */
async function loadUserRestaurants(userId: string): Promise<UserRestaurant[]> {
	try {
		const { data, error } = await supabase.rpc('get_user_restaurants');

		if (error) {
			// If function doesn't exist yet, return empty array
			if (error.message.includes('function get_user_restaurants() does not exist')) {
				console.log('📝 Restaurant functions not deployed yet - user needs to deploy auth schema');
				return [];
			}
			throw error;
		}

		return (data || []).map((item: any) => ({
			id: item.id,
			user_id: item.user_id,
			restaurant_id: item.restaurant_id,
			role: item.role,
			created_at: item.created_at
		}));
	} catch (error) {
		console.error('❌ Failed to load user restaurants:', error);
		return [];
	}
}

/**
 * Load restaurant details
 */
async function loadRestaurant(restaurantId: string): Promise<Restaurant | null> {
	try {
		const { data, error } = await supabase.rpc('get_restaurant', {
			p_restaurant_id: restaurantId
		});

		if (error) {
			// If function doesn't exist yet, return null
			if (error.message.includes('function get_restaurant') && error.message.includes('does not exist')) {
				console.log('📝 Restaurant functions not deployed yet');
				return null;
			}
			throw error;
		}

		if (!data || data.length === 0) {
			return null;
		}

		return {
			id: data[0].id,
			name: data[0].name,
			owner_id: data[0].owner_id,
			created_at: data[0].created_at,
			updated_at: data[0].updated_at
		};
	} catch (error) {
		console.error('❌ Failed to load restaurant:', error);
		return null;
	}
}

/**
 * Get current authentication state
 */
export function getAuthState(): AuthState {
	return get(authState);
}

/**
 * Check if user has specific role in current restaurant
 */
export function hasRole(role: 'owner' | 'manager' | 'staff'): boolean {
	const state = get(authState);
	if (!state.isAuthenticated || !state.currentRestaurant) return false;

	const userRestaurant = state.userRestaurants.find(
		ur => ur.restaurant_id === state.currentRestaurant!.id
	);

	return userRestaurant?.role === role;
}

/**
 * Check if user is owner or manager
 */
export function canManage(): boolean {
	return hasRole('owner') || hasRole('manager');
}

<script lang="ts">
	/**
	 * Week Calendar Picker Component
	 * Interactive calendar widget for selecting weeks in restaurant scheduling
	 * Following Code Complete principles and Design Language System
	 */
	
	import { formatWeekRange, getWeekStart, getWeekDates } from '$lib/utils.js';
	
	interface Props {
		selectedWeek: Date;
		isOpen: boolean;
		onWeekSelect: (weekStart: Date) => void;
		onClose: () => void;
		position?: 'bottom' | 'top';
		className?: string;
	}
	
	let {
		selectedWeek,
		isOpen,
		onWeekSelect,
		onClose,
		position = 'bottom',
		className = ''
	}: Props = $props();
	
	// Calendar state
	let currentMonth = $state(new Date(selectedWeek.getFullYear(), selectedWeek.getMonth(), 1));
	let hoveredWeek = $state<Date | null>(null);
	
	// Calendar calculations
	let monthName = $derived(currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' }));
	let calendarWeeks = $derived(generateCalendarWeeks(currentMonth));
	
	function generateCalendarWeeks(month: Date): Date[][] {
		const weeks: Date[][] = [];
		const firstDay = new Date(month.getFullYear(), month.getMonth(), 1);
		const lastDay = new Date(month.getFullYear(), month.getMonth() + 1, 0);
		
		// Start from the Monday of the week containing the first day
		let currentWeekStart = getWeekStart(firstDay);
		
		// Generate weeks until we've covered the entire month
		while (currentWeekStart <= lastDay || currentWeekStart.getMonth() === month.getMonth()) {
			const weekDates = getWeekDates(currentWeekStart);
			weeks.push(weekDates);
			
			// Move to next week
			currentWeekStart = new Date(currentWeekStart);
			currentWeekStart.setDate(currentWeekStart.getDate() + 7);
			
			// Stop if we've gone too far past the month
			if (currentWeekStart.getMonth() > month.getMonth() + 1 || 
				(currentWeekStart.getMonth() === month.getMonth() + 1 && currentWeekStart.getDate() > 7)) {
				break;
			}
		}
		
		return weeks;
	}
	
	function navigateMonth(direction: 'prev' | 'next') {
		const newMonth = new Date(currentMonth);
		newMonth.setMonth(currentMonth.getMonth() + (direction === 'next' ? 1 : -1));
		currentMonth = newMonth;
	}
	
	function navigateYear(direction: 'prev' | 'next') {
		const newMonth = new Date(currentMonth);
		newMonth.setFullYear(currentMonth.getFullYear() + (direction === 'next' ? 1 : -1));
		currentMonth = newMonth;
	}
	
	function selectWeek(weekStart: Date) {
		onWeekSelect(weekStart);
		onClose();
	}
	
	function isSelectedWeek(weekStart: Date): boolean {
		return getWeekStart(selectedWeek).getTime() === weekStart.getTime();
	}
	
	function isCurrentWeek(weekStart: Date): boolean {
		const today = new Date();
		return getWeekStart(today).getTime() === weekStart.getTime();
	}
	
	function isInCurrentMonth(date: Date): boolean {
		return date.getMonth() === currentMonth.getMonth();
	}
	
	function handleWeekHover(weekStart: Date | null) {
		hoveredWeek = weekStart;
	}
	
	// Close on outside click
	function handleOutsideClick(event: MouseEvent) {
		const target = event.target as Element;
		if (!target.closest('.week-calendar-picker')) {
			onClose();
		}
	}
	
	// Position classes
	const positionClasses = position === 'top' 
		? 'bottom-full mb-2' 
		: 'top-full mt-2';
</script>

<!-- Backdrop for outside click detection -->
{#if isOpen}
	<div 
		class="fixed inset-0 z-40" 
		onclick={handleOutsideClick}
		aria-hidden="true"
	></div>
{/if}

<!-- Calendar Picker -->
{#if isOpen}
	<div 
		class="week-calendar-picker absolute {positionClasses} left-0 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 min-w-[320px] {className}"
		role="dialog"
		aria-label="Week selector calendar"
	>
		<!-- Header with month/year navigation -->
		<div class="flex items-center justify-between mb-4">
			<div class="flex items-center space-x-2">
				<button
					onclick={() => navigateYear('prev')}
					class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
					aria-label="Previous year"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
					</svg>
				</button>
				<button
					onclick={() => navigateMonth('prev')}
					class="p-1 text-gray-500 hover:text-gray-700 transition-colors"
					aria-label="Previous month"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
					</svg>
				</button>
			</div>
			
			<h3 class="text-lg font-semibold text-gray-900">{monthName}</h3>
			
			<div class="flex items-center space-x-2">
				<button
					onclick={() => navigateMonth('next')}
					class="p-1 text-gray-500 hover:text-gray-700 transition-colors"
					aria-label="Next month"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
					</svg>
				</button>
				<button
					onclick={() => navigateYear('next')}
					class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
					aria-label="Next year"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
					</svg>
				</button>
			</div>
		</div>
		
		<!-- Day headers -->
		<div class="grid grid-cols-7 gap-1 mb-2">
			{#each ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as day}
				<div class="text-center text-xs font-medium text-gray-500 py-1">
					{day}
				</div>
			{/each}
		</div>
		
		<!-- Calendar weeks -->
		<div class="space-y-1">
			{#each calendarWeeks as week}
				{@const weekStart = week[0]}
				{@const isSelected = isSelectedWeek(weekStart)}
				{@const isCurrent = isCurrentWeek(weekStart)}
				{@const isHovered = hoveredWeek?.getTime() === weekStart.getTime()}
				
				<button
					class="w-full grid grid-cols-7 gap-1 p-1 rounded transition-colors
						{isSelected ? 'bg-blue-100 border border-blue-300' : 
						 isCurrent ? 'bg-teal-50 border border-teal-200' :
						 isHovered ? 'bg-gray-100' : 'hover:bg-gray-50'}"
					onclick={() => selectWeek(weekStart)}
					onmouseenter={() => handleWeekHover(weekStart)}
					onmouseleave={() => handleWeekHover(null)}
					aria-label="Week of {formatWeekRange(weekStart)}"
				>
					{#each week as date}
						<div 
							class="text-center text-sm py-1 rounded
								{isInCurrentMonth(date) ? 'text-gray-900' : 'text-gray-400'}
								{isCurrent && isInCurrentMonth(date) ? 'font-semibold' : ''}"
						>
							{date.getDate()}
						</div>
					{/each}
				</button>
				
				<!-- Week range display on hover/selection -->
				{#if isHovered || isSelected}
					<div class="text-xs text-center text-gray-600 py-1">
						{formatWeekRange(weekStart)}
					</div>
				{/if}
			{/each}
		</div>
		
		<!-- Footer with quick actions -->
		<div class="mt-4 pt-3 border-t border-gray-200 flex justify-between items-center">
			<button
				onclick={() => selectWeek(getWeekStart(new Date()))}
				class="text-sm text-blue-600 hover:text-blue-800 transition-colors"
			>
				Go to Current Week
			</button>
			<button
				onclick={onClose}
				class="text-sm text-gray-500 hover:text-gray-700 transition-colors"
			>
				Cancel
			</button>
		</div>
	</div>
{/if}

<style>
	.week-calendar-picker {
		/* Ensure proper z-index layering */
		z-index: 50;
	}
</style>

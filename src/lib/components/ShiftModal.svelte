<script lang="ts">
	import type { Shift, Employee, Venue, ShiftFormData } from '$lib/types.js';
	import { validateShift, formatDate, calculateHours, generateId, formatCurrency } from '$lib/utils.js';
	import { serverSideCalculationService } from '$lib/services/serverSideCalculationService.js';
	import { Modal, Button } from '$lib/components/ui/index.js';

	interface Props {
		shift: Shift | null;
		employees: Employee[];
		venues: Venue[];
		mode: 'create' | 'edit';
		employee?: Employee | null; // Pre-selected employee for creation
		date?: Date | null; // Pre-selected date for creation
		onSave: (shift: Shift) => void;
		onDelete: (shiftId: string) => void;
		onClose: () => void;
	}

	let { shift, employees, venues, mode, employee = null, date = null, onSave, onDelete, onClose }: Props = $props();

	// Form data with proper initialization for pre-selected values
	let formData: ShiftFormData = $state({
		employeeId: shift?.employeeId || employee?.id || null,
		venueId: shift?.venueId || shift?.locationId || venues[0]?.id || '',
		date: shift ? formatDate(shift.date) : (date ? formatDate(date) : formatDate(new Date())),
		startTime: shift?.startTime || '09:00',
		endTime: shift?.endTime || '17:00',
		totalHours: shift?.totalHours || shift?.hoursWorked,
		dailyRate: shift?.dailyRate || employee?.defaultDailyRate || 100,
		isPaid: shift?.isPaid || false,
		advanceDeduction: shift?.advanceDeduction || 0,
		notes: shift?.notes || ''
	});



	// Validation
	let errors = $state<Record<string, string>>({});
	let isSubmitting = $state(false);

	// Reset isSubmitting when modal opens/closes to prevent stuck disabled state
	$effect(() => {
		if (shift || mode) {
			// Reset submitting state when modal data changes
			isSubmitting = false;
			errors = {};
		}
	});

	// Computed values
	let calculatedHours = $derived(calculateHours(formData.startTime, formData.endTime));

	// Server-side pay calculation and validation
	let payCalculation = $state({
		totalHours: 0,
		hourlyRate: 0,
		grossPay: 0,
		isValid: true,
		validationMessage: '',
		suggestedRate: 0
	});

	// Reactive validation using server-side RPC
	$effect(() => {
		const totalHours = calculatedHours;
		const dailyRate = formData.dailyRate;

		// Prevent division by zero and invalid inputs
		if (totalHours <= 0 || dailyRate < 0) {
			payCalculation = {
				totalHours: 0,
				hourlyRate: 0,
				grossPay: 0,
				isValid: false,
				validationMessage: 'Invalid hours or rate',
				suggestedRate: 0
			};
			return;
		}

		// Async validation in a separate function
		validatePayRules(formData.employeeId, totalHours, dailyRate);
	});

	// Separate async function for server-side validation
	async function validatePayRules(employeeId: string | null, totalHours: number, dailyRate: number) {
		try {
			// Call server-side validation
			const validation = await serverSideCalculationService.validateShiftPayRules(
				employeeId,
				totalHours,
				dailyRate
			);

			// Calculate display values
			const hourlyRate = dailyRate / totalHours;
			const grossPay = dailyRate; // dailyRate is total compensation

			payCalculation = {
				totalHours,
				hourlyRate,
				grossPay,
				isValid: validation.isValid,
				validationMessage: validation.validationMessage,
				suggestedRate: validation.suggestedRate
			};
		} catch (error) {
			console.error('Pay validation error:', error);
			payCalculation = {
				totalHours,
				hourlyRate: dailyRate / totalHours,
				grossPay: dailyRate,
				isValid: false,
				validationMessage: 'Validation failed',
				suggestedRate: 0
			};
		}
	}

	let totalPay = $derived(payCalculation.grossPay);
	let netPay = $derived(totalPay - formData.advanceDeduction);

	// Auto-update daily rate when employee changes
	$effect(() => {
		if (formData.employeeId && mode === 'create') {
			const employee = employees.find((e) => e.id === formData.employeeId);
			if (employee) {
				formData.dailyRate = employee.defaultDailyRate;
			}
		}
	});



	// Auto-update total hours effect removed - using calculated hours directly

	function handleSubmit(event?: SubmitEvent | MouseEvent) {


		event?.preventDefault();

		// Validate form
		const validation = validateShift({
			...formData,
			date: new Date(formData.date)
		});



		if (!validation.isValid) {

			errors = validation.errors.reduce(
				(acc, error) => {
					acc[error.field] = error.message;
					return acc;
				},
				{} as Record<string, string>
			);
			return;
		}



		errors = {};
		isSubmitting = true;

		try {
			const shiftData: Shift = {
				id: shift?.id || generateId(),
				employeeId: formData.employeeId,
				locationId: formData.venueId, // Map venueId to locationId
				venueId: formData.venueId, // Keep for backward compatibility
				date: new Date(formData.date),
				startTime: formData.startTime,
				endTime: formData.endTime,
				hoursWorked: calculatedHours, // Always use calculated hours from start/end times
				totalHours: calculatedHours, // Always use calculated hours from start/end times
				dailyRate: formData.dailyRate,
				isPaid: formData.isPaid,
				advanceDeduction: formData.advanceDeduction,
				notes: formData.notes || undefined,
				createdAt: shift?.createdAt || new Date(),
				updatedAt: new Date()
			};

			onSave(shiftData);
		} catch (error) {
			console.error('❌ ShiftModal.handleSubmit: Error in try block:', error);
		} finally {

			isSubmitting = false;
		}
	}

	function handleDelete() {
		if (shift && confirm('Are you sure you want to delete this shift?')) {
			onDelete(shift.id);
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			onClose();
		}
	}
</script>

<svelte:window onkeydown={handleKeydown} />

<!-- Shift Modal using reusable Modal component -->
<Modal
	isOpen={true}
	title={mode === 'create' ? 'Create Shift' : 'Edit Shift'}
	size="md"
	onclose={onClose}
>
	<form onsubmit={handleSubmit} class="space-y-4">
		<!-- Employee Selection -->
		<div>
			<label for="employee-select" class="block text-sm font-medium text-gray-700 mb-1">
				Employee
			</label>
			<select
				id="employee-select"
				bind:value={formData.employeeId}
				class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
					{errors.employeeId ? 'border-red-300' : ''}"
			>
				<option value={null}>Unassigned</option>
				{#each employees.filter(e => e.isActive) as employee}
					<option value={employee.id}>{employee.name} ({employee.role})</option>
				{/each}
			</select>
			{#if errors.employeeId}
				<p class="text-red-600 text-sm mt-1">{errors.employeeId}</p>
			{/if}
		</div>

		<!-- Venue Selection -->
		<div>
			<label for="venue-select" class="block text-sm font-medium text-gray-700 mb-1">
				Venue *
			</label>
			<select
				id="venue-select"
				bind:value={formData.venueId}
				required
				class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
					{errors.venueId ? 'border-red-300' : ''}"
			>
				{#each venues as venue}
					<option value={venue.id}>{venue.name}</option>
				{/each}
			</select>
			{#if errors.venueId}
				<p class="text-red-600 text-sm mt-1">{errors.venueId}</p>
			{/if}
		</div>

		<!-- Date -->
		<div>
			<label for="shift-date" class="block text-sm font-medium text-gray-700 mb-1">
				Date *
			</label>
			<input
				id="shift-date"
				type="date"
				bind:value={formData.date}
				required
				class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
					{errors.date ? 'border-red-300' : ''}"
			/>
			{#if errors.date}
				<p class="text-red-600 text-sm mt-1">{errors.date}</p>
			{/if}
		</div>

		<!-- Time Range -->
		<div class="grid grid-cols-2 gap-4">
			<div>
				<label for="start-time" class="block text-sm font-medium text-gray-700 mb-1">
					Start Time *
				</label>
				<input
					id="start-time"
					type="time"
					bind:value={formData.startTime}
					required
					class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
						{errors.startTime ? 'border-red-300' : ''}"
				/>
				{#if errors.startTime}
					<p class="text-red-600 text-sm mt-1">{errors.startTime}</p>
				{/if}
			</div>

			<div>
				<label for="end-time" class="block text-sm font-medium text-gray-700 mb-1">
					End Time *
				</label>
				<input
					id="end-time"
					type="time"
					bind:value={formData.endTime}
					required
					class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
						{errors.endTime ? 'border-red-300' : ''}"
				/>
				{#if errors.endTime}
					<p class="text-red-600 text-sm mt-1">{errors.endTime}</p>
				{/if}
			</div>
		</div>

		<!-- Total Hours field removed - using calculated hours from start/end times only -->
		<!--
		<div>
			<label for="total-hours" class="block text-sm font-medium text-gray-700 mb-1">
				Total Hours
			</label>
			<input
				id="total-hours"
				type="number"
				step="0.5"
				min="0"
				max="24"
				bind:value={formData.totalHours}
				placeholder={calculatedHours.toFixed(1)}
				class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
			/>
			<p class="text-gray-500 text-sm mt-1">Calculated: {calculatedHours.toFixed(1)} hours</p>
		</div>
		-->

		<!-- Daily Rate -->
		<div>
			<label for="daily-rate" class="block text-sm font-medium text-gray-700 mb-1">
				Daily Rate *
			</label>
			<div class="relative">
				<span class="absolute top-2 left-3 text-gray-500">$</span>
				<input
					id="daily-rate"
					type="number"
					step="0.01"
					min="0"
					bind:value={formData.dailyRate}
					required
					class="w-full border border-gray-300 rounded-md py-2 pr-3 pl-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
						{errors.dailyRate ? 'border-red-300' : ''}"
				/>
			</div>
			{#if errors.dailyRate}
				<p class="text-red-600 text-sm mt-1">{errors.dailyRate}</p>
			{/if}
		</div>

		<!-- Advance Deduction -->
		<div>
			<label for="advance-deduction" class="block text-sm font-medium text-gray-700 mb-1">
				Advance Deduction
			</label>
			<div class="relative">
				<span class="absolute top-2 left-3 text-gray-500">$</span>
				<input
					id="advance-deduction"
					type="number"
					step="0.01"
					min="0"
					bind:value={formData.advanceDeduction}
					class="w-full border border-gray-300 rounded-md py-2 pr-3 pl-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
						{errors.advanceDeduction ? 'border-red-300' : ''}"
				/>
			</div>
			{#if errors.advanceDeduction}
				<p class="text-red-600 text-sm mt-1">{errors.advanceDeduction}</p>
			{/if}
		</div>

		<!-- Notes -->
		<div>
			<label for="shift-notes" class="block text-sm font-medium text-gray-700 mb-1">
				Notes
			</label>
			<textarea
				id="shift-notes"
				bind:value={formData.notes}
				rows="3"
				placeholder="Optional notes about this shift..."
				class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
			></textarea>
		</div>

		<!-- Pay Summary -->
		<div class="rounded-md bg-gray-50 p-4">
			<h3 class="mb-2 text-sm font-medium text-gray-900">Pay Summary</h3>
			<div class="space-y-1 text-sm">
				<div class="flex justify-between">
					<span class="text-gray-600">Hours:</span>
					<span>{payCalculation.totalHours.toFixed(1)}h</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-600">Rate:</span>
					<span>{formatCurrency(payCalculation.hourlyRate)}/hr</span>
				</div>
				{#if !payCalculation.isValid}
					<div class="flex justify-between text-red-600">
						<span>⚠️ Validation:</span>
						<span class="text-xs">{payCalculation.validationMessage}</span>
					</div>
					{#if payCalculation.suggestedRate > 0}
						<div class="flex justify-between text-blue-600">
							<span>Suggested:</span>
							<span class="text-xs">{formatCurrency(payCalculation.suggestedRate)}</span>
						</div>
					{/if}
				{/if}
				<div class="flex justify-between">
					<span class="text-gray-600">Gross Pay:</span>
					<span>{formatCurrency(totalPay)}</span>
				</div>
				{#if formData.advanceDeduction > 0}
					<div class="flex justify-between text-orange-600">
						<span>Advance Deduction:</span>
						<span>-{formatCurrency(formData.advanceDeduction)}</span>
					</div>
				{/if}
				<div class="flex justify-between border-t border-gray-200 pt-1 font-medium">
					<span>Net Pay:</span>
					<span>{formatCurrency(netPay)}</span>
				</div>
			</div>
		</div>

		<!-- Payment Status Section -->
		<div class="rounded-md bg-blue-50 border border-blue-200 p-4">
			<h3 class="text-sm font-medium text-blue-900 mb-3">Payment Status</h3>
			<div class="flex items-center space-x-3">
				<input
					type="checkbox"
					id="isPaid"
					bind:checked={formData.isPaid}
					class="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
				/>
				<label for="isPaid" class="text-sm font-medium text-blue-900 cursor-pointer">
					Mark as Paid
				</label>
				{#if formData.isPaid}
					<div class="flex items-center text-green-600">
						<svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
						</svg>
						<span class="text-xs font-medium">Paid</span>
					</div>
				{/if}
			</div>
			<p class="text-xs text-blue-700 mt-2">
				{formData.isPaid ? 'This shift has been marked as paid.' : 'Check this box when payment has been processed.'}
			</p>
		</div>
		<!-- Modal Footer -->
		<div class="flex items-center justify-between border-t border-gray-200 p-6 mt-6">
			<div>
				{#if mode === 'edit' && shift}
					<Button variant="danger" onclick={handleDelete}>
						Delete Shift
					</Button>
				{/if}
			</div>

			<div class="flex items-center space-x-3">
				<Button variant="secondary" onclick={onClose}>
					Cancel
				</Button>
				<Button
					variant="primary"
					onclick={handleSubmit}
					disabled={isSubmitting}
					loading={isSubmitting}
				>
					{mode === 'create' ? 'Create Shift' : 'Save Changes'}
				</Button>
			</div>
		</div>
	</form>
</Modal>

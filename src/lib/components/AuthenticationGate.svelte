<!--
	AuthenticationGate Component
	Best-in-class authentication following Code Complete principles:
	- Single Responsibility: Handles only authentication UI
	- Clear Error Handling: Comprehensive error states
	- User-Centered Design: Intuitive sign-in experience
	- Security First: Proper validation and feedback
-->

<script lang="ts">
	import { enhancedAuth } from '$lib/security/index.js';
	import { LoadingSpinner } from '$lib/components/ui/index.js';

	// Component state following Code Complete: Clear, minimal state
	let mode: 'signin' | 'signup' = $state('signin');
	let email = $state('');
	let password = $state('');
	let confirmPassword = $state('');
	let isLoading = $state(false);
	let error = $state<string | null>(null);
	let validationErrors = $state<Record<string, string>>({});

	/**
	 * Validate form inputs
	 * Following Code Complete: Input validation with clear error messages
	 */
	function validateForm(): boolean {
		validationErrors = {};

		if (!email.trim()) {
			validationErrors.email = 'Email is required';
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
			validationErrors.email = 'Please enter a valid email address';
		}

		if (!password) {
			validationErrors.password = 'Password is required';
		} else if (password.length < 6) {
			validationErrors.password = 'Password must be at least 6 characters';
		}

		if (mode === 'signup' && password !== confirmPassword) {
			validationErrors.confirmPassword = 'Passwords do not match';
		}

		return Object.keys(validationErrors).length === 0;
	}

	/**
	 * Handle form submission
	 * Following Code Complete: Clear error handling and user feedback
	 */
	async function handleSubmit(): Promise<void> {
		if (!validateForm()) return;

		try {
			isLoading = true;
			error = null;

			const result = mode === 'signin'
				? await enhancedAuth.signIn({ email: email.trim(), password })
				: await enhancedAuth.signUp({ email: email.trim(), password });

			if (!result.success) {
				error = result.error || `${mode === 'signin' ? 'Sign in' : 'Sign up'} failed`;
			}
			// Success is handled by auth state change in parent component

		} catch (err) {
			error = err instanceof Error ? err.message : 'An unexpected error occurred';
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Switch between sign in and sign up modes
	 */
	function switchMode(): void {
		mode = mode === 'signin' ? 'signup' : 'signin';
		error = null;
		validationErrors = {};
	}

	/**
	 * Clear errors when user starts typing
	 */
	function clearErrors(): void {
		error = null;
		validationErrors = {};
	}
</script>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
	<div class="max-w-md w-full space-y-8">
		<!-- Header -->
		<div class="text-center">
			<h2 class="mt-6 text-3xl font-extrabold text-gray-900">
				{mode === 'signin' ? 'Sign in to your account' : 'Create your account'}
			</h2>
			<p class="mt-2 text-sm text-gray-600">
				{mode === 'signin' ? 'Access your restaurant scheduler' : 'Get started with restaurant scheduling'}
			</p>
		</div>

		<!-- Authentication Form -->
		<form class="mt-8 space-y-6" onsubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
			<div class="space-y-4">
				<!-- Email Field -->
				<div>
					<label for="email" class="block text-sm font-medium text-gray-700">
						Email address
					</label>
					<input
						id="email"
						name="email"
						type="email"
						autocomplete="email"
						required
						bind:value={email}
						oninput={() => clearErrors()}
						disabled={isLoading}
						class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
						class:border-red-300={validationErrors.email}
						class:focus:ring-red-500={validationErrors.email}
						class:focus:border-red-500={validationErrors.email}
						placeholder="Enter your email"
					/>
					{#if validationErrors.email}
						<p class="mt-1 text-sm text-red-600">{validationErrors.email}</p>
					{/if}
				</div>

				<!-- Password Field -->
				<div>
					<label for="password" class="block text-sm font-medium text-gray-700">
						Password
					</label>
					<input
						id="password"
						name="password"
						type="password"
						autocomplete={mode === 'signin' ? 'current-password' : 'new-password'}
						required
						bind:value={password}
						oninput={() => clearErrors()}
						disabled={isLoading}
						class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
						class:border-red-300={validationErrors.password}
						class:focus:ring-red-500={validationErrors.password}
						class:focus:border-red-500={validationErrors.password}
						placeholder="Enter your password"
					/>
					{#if validationErrors.password}
						<p class="mt-1 text-sm text-red-600">{validationErrors.password}</p>
					{/if}
				</div>

				<!-- Confirm Password Field (Sign Up Only) -->
				{#if mode === 'signup'}
					<div>
						<label for="confirmPassword" class="block text-sm font-medium text-gray-700">
							Confirm Password
						</label>
						<input
							id="confirmPassword"
							name="confirmPassword"
							type="password"
							autocomplete="new-password"
							required
							bind:value={confirmPassword}
							oninput={() => clearErrors()}
							disabled={isLoading}
							class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
							class:border-red-300={validationErrors.confirmPassword}
							class:focus:ring-red-500={validationErrors.confirmPassword}
							class:focus:border-red-500={validationErrors.confirmPassword}
							placeholder="Confirm your password"
						/>
						{#if validationErrors.confirmPassword}
							<p class="mt-1 text-sm text-red-600">{validationErrors.confirmPassword}</p>
						{/if}
					</div>
				{/if}
			</div>

			<!-- Error Display -->
			{#if error}
				<div class="bg-red-50 border border-red-200 rounded-md p-4">
					<div class="flex">
						<div class="flex-shrink-0">
							<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
								<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
							</svg>
						</div>
						<div class="ml-3">
							<p class="text-sm text-red-800">{error}</p>
						</div>
					</div>
				</div>
			{/if}

			<!-- Submit Button -->
			<div>
				<button
					type="submit"
					disabled={isLoading}
					class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
				>
					{#if isLoading}
						<LoadingSpinner size="sm" />
						<span class="ml-2">
							{mode === 'signin' ? 'Signing in...' : 'Creating account...'}
						</span>
					{:else}
						{mode === 'signin' ? 'Sign in' : 'Create account'}
					{/if}
				</button>
			</div>

			<!-- Mode Switch -->
			<div class="text-center">
				<button
					type="button"
					onclick={() => switchMode()}
					disabled={isLoading}
					class="text-sm text-blue-600 hover:text-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					{mode === 'signin'
						? "Don't have an account? Sign up"
						: 'Already have an account? Sign in'}
				</button>
			</div>
		</form>
	</div>
</div>

<script lang="ts">
	import type { WeeklySchedule, UIState, Shift, Employee } from '$lib/types.js';
	// Note: Removed formatCurrency and server-side calculations from display layer
	// Per Code Complete principles: UI components should be pure display layers
	import ShiftBlock from './ShiftBlock.svelte';

	interface Props {
		schedule: WeeklySchedule;
		weekDates: Date[];
		uiState: UIState;
		isToday: (date: Date) => boolean;
		onShiftClick: (shift: Shift | null, employee?: Employee | null) => void;
		onShiftCreate: (shift: Shift | null, employee?: Employee | null) => void;
		onShiftDelete?: (shiftId: string) => void;
		onDragCreateComplete?: () => void;
	}

	let { schedule, weekDates, uiState, isToday, onShiftClick, onShiftCreate, onShiftDelete, onDragCreateComplete }: Props = $props();

	// Removed debug logging to prevent interference with reactive calculations

	// Function to check if text is truncated within a given width
	function isTextTruncated(text: string, maxWidth: number, fontSize: string = '11px', fontWeight: string = '500'): boolean {
		// Create a temporary canvas element to measure text width
		const canvas = document.createElement('canvas');
		const context = canvas.getContext('2d');
		if (!context) return false;

		// Set font properties to match the actual text styling
		context.font = `${fontWeight} ${fontSize} -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif`;

		// Measure the text width
		const textWidth = context.measureText(text).width;

		// Add some padding to account for container padding and margins (approximately 8px total)
		const availableWidth = maxWidth - 8;

		return textWidth > availableWidth;
	}

	// Get shifts for a specific employee and date
	function getShiftsForEmployeeAndDate(employeeId: string, date: Date): Shift[] {
		const filteredShifts = schedule.shifts.filter(
			(shift) =>
				shift.employeeId === employeeId && shift.date.toDateString() === date.toDateString()
		);



		return filteredShifts;
	}

	// Get unassigned shifts for a specific date
	function getUnassignedShiftsForDate(date: Date): Shift[] {
		return schedule.shifts.filter(
			(shift) => !shift.employeeId && shift.date.toDateString() === date.toDateString()
		);
	}

	// Handle creating a new shift
	function handleCreateShift(employee: Employee, date: Date) {

		// Create a mock shift object with the correct date for the modal
		const shiftWithDate = {
			id: '', // Will be generated
			employeeId: employee.id,
			venueId: '', // Will be set in modal
			date: date,
			startTime: '09:00',
			endTime: '17:00',
			totalHours: 8.0,
			dailyRate: employee.defaultDailyRate,
			isPaid: false,
			advanceDeduction: 0,
			notes: ''
		};

		onShiftCreate(shiftWithDate as any, employee);
	}

	// Handle creating a new unassigned shift
	function handleCreateUnassignedShift(date: Date) {

		// Create a mock shift object with null employeeId for unassigned shift
		const unassignedShift = {
			id: '', // Will be generated
			employeeId: null, // Unassigned
			venueId: '', // Will be set in modal
			date: date,
			startTime: '09:00',
			endTime: '17:00',
			totalHours: 8.0,
			dailyRate: 100, // Default rate
			isPaid: false,
			advanceDeduction: 0,
			notes: ''
		};

		onShiftCreate(unassignedShift as any, null);
	}

	// Handle unassigned shift drag creation start
	function handleUnassignedCellDragStart(event: MouseEvent, date: Date, existingShift?: Shift) {
		event.preventDefault();
		event.stopPropagation();



		// Initialize drag create state for unassigned shifts
		uiState.dragCreateState = {
			isActive: true,
			mode: null, // Will be determined by drag direction
			direction: null,
			sourceEmployee: null, // No source employee for unassigned shifts
			sourceDate: date,
			sourceShift: existingShift || null,
			targetDates: [],
			targetEmployees: [],
			previewCells: []
		};

		// Add global mouse event listeners
		document.addEventListener('mousemove', handleUnassignedDragCreateMove);
		document.addEventListener('mouseup', handleUnassignedDragCreateEnd);
		document.body.classList.add('drag-create-active');
	}

	// Handle unassigned drag creation mouse move
	function handleUnassignedDragCreateMove(event: MouseEvent) {
		if (!uiState.dragCreateState.isActive) return;

		// Find the cell under the mouse - check both the element and its closest cell
		const element = document.elementFromPoint(event.clientX, event.clientY);
		const cell = element?.closest('[data-schedule-cell]') || element?.closest('[data-unassigned-cell]');

		if (cell) {
			const employeeId = cell.getAttribute('data-employee-id');
			const dateStr = cell.getAttribute('data-date');
			const isUnassignedRow = cell.getAttribute('data-unassigned-row') === 'true' || cell.getAttribute('data-unassigned-cell') === 'true';

			if (dateStr) {
				const targetDate = new Date(dateStr);
				const sourceDate = uiState.dragCreateState.sourceDate;

				if (!sourceDate) return;

				if (isUnassignedRow) {
					// Horizontal drag across unassigned row (multiple days, same time slot)
					const isSameDate = targetDate.toDateString() === sourceDate.toDateString();

					if (!isSameDate) {
						uiState.dragCreateState.direction = 'horizontal';
						uiState.dragCreateState.mode = 'unassigned-horizontal';

						const startDate = new Date(Math.min(sourceDate.getTime(), targetDate.getTime()));
						const endDate = new Date(Math.max(sourceDate.getTime(), targetDate.getTime()));

						const targetDates: Date[] = [];
						const previewCells: { employeeId: string | null; date: Date }[] = [];

						for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
							const currentDate = new Date(d);
							targetDates.push(currentDate);
							previewCells.push({ employeeId: null, date: currentDate });
						}

						uiState.dragCreateState.targetDates = targetDates;
						uiState.dragCreateState.previewCells = previewCells;


					}
				} else if (employeeId) {
					// Vertical drag from unassigned row to employee rows (multiple unassigned shifts, same date)
					const isSameDate = targetDate.toDateString() === sourceDate.toDateString();

					if (isSameDate) {
						uiState.dragCreateState.direction = 'vertical';
						uiState.dragCreateState.mode = 'unassigned-vertical';

						// Find all employees between source (unassigned row) and target employee
						const allEmployees = schedule.employees.filter(emp => emp.isActive);
						const targetIndex = allEmployees.findIndex(emp => emp.id === employeeId);

						if (targetIndex !== -1) {
							// Create multiple unassigned shifts (one for each employee row we're dragging over)
							// But all shifts remain unassigned (employeeId: null)
							const numberOfShifts = targetIndex + 1; // Number of employee rows we're covering
							const previewCells: { employeeId: string | null; date: Date }[] = [];

							// Create preview cells for multiple unassigned shifts on the same date
							for (let i = 0; i < numberOfShifts; i++) {
								previewCells.push({ employeeId: null, date: sourceDate });
							}

							uiState.dragCreateState.targetDates = [sourceDate]; // Same date, multiple shifts
							uiState.dragCreateState.previewCells = previewCells;
						}
					}
				}
			}
		}
	}

	// Handle unassigned drag creation end
	function handleUnassignedDragCreateEnd(_event: MouseEvent) {
		if (!uiState.dragCreateState.isActive) return;

		console.log('🔄 ScheduleGrid: Ending unassigned drag creation');

		// Clean up event listeners
		document.removeEventListener('mousemove', handleUnassignedDragCreateMove);
		document.removeEventListener('mouseup', handleUnassignedDragCreateEnd);
		document.body.classList.remove('drag-create-active');

		// Trigger creation if we have valid targets
		const hasTargets = uiState.dragCreateState.targetDates.length > 0 || uiState.dragCreateState.previewCells.length > 0;

		if (hasTargets) {
			console.log('🔄 ScheduleGrid: Calling onDragCreateComplete with targets:', hasTargets);
			onDragCreateComplete?.();
		} else {
			console.log('🔄 ScheduleGrid: No valid targets, resetting drag state');
			// Reset state if no valid drag occurred
			uiState.dragCreateState = {
				isActive: false,
				mode: null,
				direction: null,
				sourceEmployee: null,
				sourceDate: null,
				sourceShift: null,
				targetDates: [],
				targetEmployees: [],
				previewCells: []
			};
		}
	}

	// Handle duplicating an existing shift
	function handleDuplicateShift(shift: Shift) {
		console.log('🔄 ScheduleGrid: handleDuplicateShift called for shift:', shift.id);

		// Create a duplicate shift object with same data but new ID
		const duplicateShift = {
			id: '', // Will be generated
			employeeId: shift.employeeId,
			venueId: shift.venueId,
			date: shift.date,
			startTime: shift.startTime,
			endTime: shift.endTime,
			totalHours: shift.totalHours,
			dailyRate: shift.dailyRate,
			isPaid: false, // New shift starts as unpaid
			advanceDeduction: 0,
			notes: shift.notes ? `Copy of: ${shift.notes}` : 'Duplicated shift'
		};

		// Find the employee for this shift
		const employee = schedule.employees.find(emp => emp.id === shift.employeeId);
		onShiftCreate(duplicateShift as any, employee || null);
	}

	// New drag-to-create functionality

	// Handle empty cell drag creation (Method A)
	function handleEmptyCellDragStart(event: MouseEvent, employee: Employee, date: Date) {
		event.preventDefault();
		event.stopPropagation();

		console.log('🔄 ScheduleGrid: Starting empty cell drag creation for employee:', employee.name, 'date:', date);

		// Initialize drag create state
		uiState.dragCreateState = {
			isActive: true,
			mode: null, // Will be determined by drag direction
			direction: null,
			sourceEmployee: employee,
			sourceDate: date,
			sourceShift: null,
			targetDates: [],
			targetEmployees: [],
			previewCells: []
		};

		// Add global mouse event listeners
		document.addEventListener('mousemove', handleDragCreateMove);
		document.addEventListener('mouseup', handleDragCreateEnd);
		document.body.classList.add('drag-create-active');
	}

	// Handle drag creation mouse move
	function handleDragCreateMove(event: MouseEvent) {
		if (!uiState.dragCreateState.isActive) return;

		// Find the cell under the mouse
		const element = document.elementFromPoint(event.clientX, event.clientY);
		const cell = element?.closest('[data-schedule-cell]');

		if (cell) {
			const employeeId = cell.getAttribute('data-employee-id');
			const dateStr = cell.getAttribute('data-date');

			if (employeeId && dateStr) {
				const targetDate = new Date(dateStr);
				const sourceEmployee = uiState.dragCreateState.sourceEmployee;
				const sourceDate = uiState.dragCreateState.sourceDate;

				if (!sourceEmployee || !sourceDate) return;

				// Determine drag direction and mode
				const isSameEmployee = employeeId === sourceEmployee.id;
				const isSameDate = targetDate.toDateString() === sourceDate.toDateString();

				if (isSameEmployee && !isSameDate) {
					// Horizontal drag (same employee, different dates)
					uiState.dragCreateState.direction = 'horizontal';
					uiState.dragCreateState.mode = 'empty-cell-horizontal';

					const startDate = new Date(Math.min(sourceDate.getTime(), targetDate.getTime()));
					const endDate = new Date(Math.max(sourceDate.getTime(), targetDate.getTime()));

					const targetDates: Date[] = [];
					const previewCells: { employeeId: string; date: Date }[] = [];

					for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
						const currentDate = new Date(d);
						targetDates.push(currentDate);
						previewCells.push({ employeeId, date: currentDate });
					}

					uiState.dragCreateState.targetDates = targetDates;
					uiState.dragCreateState.previewCells = previewCells;

				} else if (!isSameEmployee && isSameDate) {
					// Vertical drag (different employees, same date)
					uiState.dragCreateState.direction = 'vertical';

					// Determine mode based on whether we have a source shift
					if (uiState.dragCreateState.sourceShift) {
						uiState.dragCreateState.mode = 'duplicate-shift-vertical';
					} else {
						uiState.dragCreateState.mode = 'empty-cell-vertical';
					}

					// Find all employees between source and target
					const allEmployees = schedule.employees.filter(emp => emp.isActive);
					const sourceIndex = allEmployees.findIndex(emp => emp.id === sourceEmployee.id);
					const targetIndex = allEmployees.findIndex(emp => emp.id === employeeId);

					if (sourceIndex !== -1 && targetIndex !== -1) {
						const startIndex = Math.min(sourceIndex, targetIndex);
						const endIndex = Math.max(sourceIndex, targetIndex);

						let targetEmployees = allEmployees.slice(startIndex, endIndex + 1);

						// For duplicate mode, exclude the source employee
						if (uiState.dragCreateState.sourceShift) {
							targetEmployees = targetEmployees.filter(emp => emp.id !== sourceEmployee.id);
						}

						const previewCells: { employeeId: string; date: Date }[] = [];

						for (const emp of targetEmployees) {
							previewCells.push({ employeeId: emp.id, date: sourceDate });
						}

						uiState.dragCreateState.targetEmployees = targetEmployees;
						uiState.dragCreateState.previewCells = previewCells;
					}
				}
			}
		}
	}

	// Handle drag creation end
	function handleDragCreateEnd(_event: MouseEvent) {
		if (!uiState.dragCreateState.isActive) return;

		console.log('🔄 ScheduleGrid: Ending drag creation');

		// Clean up event listeners
		document.removeEventListener('mousemove', handleDragCreateMove);
		document.removeEventListener('mouseup', handleDragCreateEnd);
		document.body.classList.remove('drag-create-active');

		// Trigger creation if we have valid targets
		const hasTargets = uiState.dragCreateState.targetDates.length > 0 || uiState.dragCreateState.targetEmployees.length > 0 || uiState.dragCreateState.previewCells.length > 0;

		if (hasTargets) {
			console.log('🔄 ScheduleGrid: Calling onDragCreateComplete with targets:', hasTargets);
			onDragCreateComplete?.();
		} else {
			console.log('🔄 ScheduleGrid: No valid targets, resetting drag state');
			// Reset state if no valid drag occurred
			uiState.dragCreateState = {
				isActive: false,
				mode: null,
				direction: null,
				sourceEmployee: null,
				sourceDate: null,
				sourceShift: null,
				targetDates: [],
				targetEmployees: [],
				previewCells: []
			};
		}
	}

	// Employee dragging functionality for direct shift creation
	function handleEmployeeDragStart(event: DragEvent, employee: Employee) {
		if (!event.dataTransfer) return;

		console.log('🔄 ScheduleGrid: Employee drag started:', employee.name);

		// Set drag data
		event.dataTransfer.effectAllowed = 'copy';
		event.dataTransfer.setData('text/plain', JSON.stringify({
			type: 'employee',
			employee: employee
		}));

		// Update UI state
		uiState.dragState = {
			isDragging: true,
			draggedShift: null,
			draggedEmployee: employee,
			dropTarget: null
		};

		// Add visual feedback
		if (event.target instanceof HTMLElement) {
			event.target.style.opacity = '0.7';
		}
	}

	function handleEmployeeDragEnd(event: DragEvent) {
		console.log('🔄 ScheduleGrid: Employee drag ended');

		// Reset visual feedback
		if (event.target instanceof HTMLElement) {
			event.target.style.opacity = '1';
		}

		// Reset drag state if not dropped successfully
		if (uiState.dragState.isDragging && !uiState.dragState.dropTarget) {
			uiState.dragState = {
				isDragging: false,
				draggedShift: null,
				draggedEmployee: null,
				dropTarget: null
			};
		}
	}

	// Handle shift assignment via drag and drop
	function handleShiftDrop(event: DragEvent, targetEmployeeId: string, date: Date) {
		event.preventDefault();
		console.log('🔄 ScheduleGrid: Drop detected on employee:', targetEmployeeId, 'date:', date);

		try {
			// Check if we're dropping an employee (either from direct drag or old Employee Pool)
			const dragDataText = event.dataTransfer?.getData('text/plain');
			const dragDataJson = event.dataTransfer?.getData('application/json');

			// Handle new direct employee dragging
			if (dragDataText) {
				try {
					const parsedData = JSON.parse(dragDataText);
					console.log('🔍 ScheduleGrid: Parsed text drag data:', parsedData);

					if (parsedData.type === 'employee') {
						const employee = parsedData.employee;
						console.log('✅ ScheduleGrid: Creating shift for dragged employee:', employee.name || employee.fullName, 'on date:', date.toDateString());

						// Create a mock shift object with the correct date for the modal
						const shiftWithDate = {
							id: '', // Will be generated
							employeeId: employee.id,
							venueId: '', // Will be set in modal
							date: date,
							startTime: '09:00',
							endTime: '17:00',
							totalHours: 8.0,
							dailyRate: employee.defaultDailyRate || 100,
							isPaid: false,
							advanceDeduction: 0,
							notes: 'Shift created by employee drag-and-drop'
						};

						// Create a new shift for this employee with the correct date
						onShiftCreate(shiftWithDate as any, employee);
						return;
					}
				} catch (parseError) {
					console.log('🔍 ScheduleGrid: Text data is not JSON, checking for shift ID');
					// This might be a shift ID from existing shift drag
				}
			}

			// Handle legacy Employee Pool format (application/json)
			if (dragDataJson) {
				const parsedData = JSON.parse(dragDataJson);
				console.log('🔍 ScheduleGrid: Parsed JSON drag data:', parsedData);

				if (parsedData.type === 'employee') {
					// Employee assignment from Employee Pool (legacy)
					const employee = parsedData.employee;
					console.log('✅ ScheduleGrid: Assigning employee to new shift:', employee.name || employee.fullName, 'on date:', date.toDateString());

					// Create a mock shift object with the correct date for the modal
					const shiftWithDate = {
						id: '', // Will be generated
						employeeId: employee.id,
						venueId: '', // Will be set in modal
						date: date,
						startTime: '09:00',
						endTime: '17:00',
						totalHours: 8.0,
						dailyRate: employee.defaultDailyRate || 100,
						isPaid: false,
						advanceDeduction: 0,
						notes: 'Shift created by employee pool drag-and-drop'
					};

					// Create a new shift for this employee with the correct date
					onShiftCreate(shiftWithDate as any, employee);
					return;
				}
			}

			// Handle existing shift movement
			if (uiState.dragState.draggedShift) {
				console.log('🔄 ScheduleGrid: Moving existing shift to new employee/date');

				// Update the shift with new employee and date
				const updatedShift = {
					...uiState.dragState.draggedShift,
					employeeId: targetEmployeeId,
					date: new Date(date)
				};

				const shiftIndex = schedule.shifts.findIndex(
					(s) => s.id === uiState.dragState.draggedShift!.id
				);
				if (shiftIndex !== -1) {
					schedule.shifts[shiftIndex] = updatedShift;
					console.log('✅ ScheduleGrid: Shift updated successfully');
				}
			}
		} catch (error) {
			console.error('❌ ScheduleGrid: Error handling drop:', error);
		}

		// Reset drag state
		uiState.dragState = {
			isDragging: false,
			draggedShift: null,
			draggedEmployee: null,
			dropTarget: null
		};
	}

	function handleDragOver(event: DragEvent) {
		event.preventDefault();
	}

	function handleDragEnter(event: DragEvent, employeeId: string, date: Date) {
		event.preventDefault();
		if (uiState.dragState.isDragging) {
			uiState.dragState.dropTarget = { employeeId, date };
		}
	}

	function handleDragLeave(event: DragEvent) {
		event.preventDefault();
		// Only clear drop target if we're leaving the grid entirely
		const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
		const x = event.clientX;
		const y = event.clientY;

		if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
			uiState.dragState.dropTarget = null;
		}
	}

	// Remove the function - we'll use reactive calculations in the template
</script>

<div class="bg-white schedule-grid-container">
	<!-- Schedule Grid - Optimized for full screen visibility with frozen header -->
	<div class="overflow-x-auto max-h-[calc(100vh-200px)] overflow-y-auto">
		<table class="w-full border-collapse table-fixed">
			<!-- Frozen Header Row -->
			<thead class="sticky top-0 z-20 bg-gray-50 schedule-grid-frozen-header">
				<tr class="border-b border-gray-200 bg-gray-50">
					<th
						class="sticky left-0 w-[126px] border-r border-gray-200 bg-gray-50 px-3 py-2 text-left text-sm font-semibold text-gray-900 z-30"
					>
						Employee
					</th>
					{#each weekDates as date}
						<th class="w-[120px] border-r border-gray-200 px-2 py-1 text-center text-sm font-semibold text-gray-900 bg-gray-50 z-20">
							{#if isToday(date)}
								<!-- Today's date with horizontal pill layout and bold text -->
								<div class="flex flex-col items-center">
									<div class="bg-teal-100 border border-teal-300 rounded-full px-2 py-1 min-w-[50px]">
										<div class="flex flex-row items-center justify-center space-x-1">
											<span class="text-sm font-bold tracking-wide uppercase text-teal-700">
												{date.toLocaleDateString('en-US', { weekday: 'short' })}
											</span>
											<span class="text-sm font-bold text-teal-900">
												{date.toLocaleDateString('en-US', { month: 'short' })}
											</span>
											<span class="text-sm font-bold text-teal-900">
												{date.toLocaleDateString('en-US', { day: 'numeric' })}
											</span>
										</div>
									</div>
								</div>
							{:else}
								<!-- Regular date display with enhanced font hierarchy -->
								<div class="flex flex-row items-center justify-center space-x-1">
									<span class="text-sm font-semibold tracking-wide uppercase text-gray-500">
										{date.toLocaleDateString('en-US', { weekday: 'short' })}
									</span>
									<span class="text-xs font-medium text-gray-900">
										{date.toLocaleDateString('en-US', { month: 'short' })}
									</span>
									<span class="text-xs font-medium text-gray-900">
										{date.toLocaleDateString('en-US', { day: 'numeric' })}
									</span>
								</div>
							{/if}
						</th>
					{/each}
					<th class="w-[100px] px-2 py-1 text-center text-sm font-semibold text-gray-900 bg-gray-50 z-20">
						Weekly Total
					</th>
				</tr>
			</thead>

			<!-- Employee Rows -->
			<tbody class="divide-y divide-gray-200">
				{#each schedule.employees.filter((emp) => emp.isActive) as employee}
					{@const employeeShifts = schedule.shifts.filter(s => s.employeeId === employee.id)}
					{@const totalHours = employeeShifts.reduce((sum, shift) => sum + (shift.totalHours || 0), 0)}
					{@const totalShifts = employeeShifts.length}
					<tr class="transition-colors hover:bg-gray-50">
						<!-- Employee Info - Draggable for direct shift creation -->
						<td class="sticky left-0 border-r border-gray-200 bg-white px-2 py-1 z-10">
							<div
								class="flex items-center space-x-1.5 cursor-move hover:bg-blue-50 rounded p-0.5 transition-colors"
								draggable="true"
								role="button"
								tabindex="0"
								aria-label="Drag {employee.name} to create shift"
								ondragstart={(e) => handleEmployeeDragStart(e, employee)}
								ondragend={handleEmployeeDragEnd}
							>
								<div class="flex-shrink-0">
									<div class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100">
										<span class="text-[9px] font-medium text-blue-700">
											{(employee.name || employee.fullName || 'U')
												.split(' ')
												.map((n) => n[0])
												.join('')}
										</span>
									</div>
								</div>
								<div class="min-w-0 flex-1 {
									// Show tooltip only if text is truncated AND not dragging
									isTextTruncated(employee.name || employee.fullName || 'Unknown Employee', 126) && !uiState.dragState.isDragging
										? 'tooltip-container'
										: ''
								}">
									<p class="truncate text-[11px] font-medium text-gray-900">
										{employee.name || employee.fullName || 'Unknown Employee'}
									</p>
									{#if isTextTruncated(employee.name || employee.fullName || 'Unknown Employee', 126) && !uiState.dragState.isDragging}
										<span class="tooltip-text">
											{employee.name || employee.fullName || 'Unknown Employee'}
										</span>
									{/if}
								</div>
								<!-- Drag handle indicator -->
								<div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
									<svg class="w-2.5 h-2.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
									</svg>
								</div>
							</div>
						</td>

						<!-- Daily Shift Cells -->
						{#each weekDates as date}
							{@const shifts = getShiftsForEmployeeAndDate(employee.id, date)}
							{@const isDropTarget =
								uiState.dragState.dropTarget?.employeeId === employee.id &&
								uiState.dragState.dropTarget?.date.toDateString() === date.toDateString()}

							<td
								class="relative border-r border-gray-200 w-[120px] align-top overflow-hidden
									{isDropTarget ? 'border-blue-300 bg-blue-50' : 'bg-white'}
									{uiState.dragState.isDragging ? 'border-dashed' : ''}
									{uiState.dragCreateState.previewCells.some(cell =>
										cell.employeeId === employee.id &&
										cell.date.toDateString() === date.toDateString()) ?
										(uiState.dragCreateState.direction === 'horizontal' ? 'drag-create-horizontal-target' : 'drag-create-vertical-target') : ''}
									{shifts.length === 0 ? 'cursor-crosshair' : ''}
									{shifts.length === 0 ? 'hover:bg-gray-50' : ''}"
								style="height: 40px; max-width: 120px;"
								data-schedule-cell="true"
								data-employee-id={employee.id}
								data-date={date.toISOString()}
								ondrop={(e) => handleShiftDrop(e, employee.id, date)}
								ondragover={handleDragOver}
								ondragenter={(e) => handleDragEnter(e, employee.id, date)}
								ondragleave={handleDragLeave}
							>
								<div class="group relative {shifts.length > 1 ? 'space-y-0.5' : 'h-[40px] flex flex-col'}" style="{shifts.length === 1 ? 'height: 40px;' : ''}">
									{#if shifts.length > 0}
										{#each shifts as shift}
											<ShiftBlock
												{shift}
												venue={schedule.venues.find((v) => v.id === shift.venueId)}
												{uiState}
												onClick={() => onShiftClick(shift)}
												onDelete={onShiftDelete}
												onDuplicate={handleDuplicateShift}
												onDragCreateComplete={onDragCreateComplete}
											/>
										{/each}
									{:else}
										<!-- Empty cell - compact "+" button with drag creation support -->
										<button
											onclick={(e) => {
												e.preventDefault();
												e.stopPropagation();
												handleCreateShift(employee, date);
											}}
											onmousedown={(e) => {
												// Check if this is a potential drag operation
												if (e.button === 0) { // Left mouse button
													handleEmptyCellDragStart(e, employee, date);
												}
											}}
											class="flex h-full min-h-[40px] w-full items-center justify-center rounded
												border border-dashed border-gray-200 opacity-0 group-hover:opacity-100
												text-gray-400 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:text-blue-500
												{uiState.dragCreateState.isActive ? 'drag-create-cursor' : ''}"
											aria-label="Add shift for {(employee.name || employee.fullName)} on {date.toLocaleDateString()} or drag to create multiple"
										>
											<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M12 6v6m0 0v6m0-6h6m-6 0H6"
												/>
											</svg>
										</button>
									{/if}


								</div>
							</td>
						{/each}

						<!-- Weekly Total - Compact layout with reduced font sizes and cell height -->
						<td class="text-center">
							<div class="text-[12.5px] font-semibold text-gray-900">
								{totalShifts} shifts
							</div>
							<div class="text-[10.5px] text-gray-500">
								{totalHours.toFixed(1)}h
							</div>
						</td>
					</tr>
				{/each}

				<!-- Unassigned Shifts Row - Compact layout -->
				<tr class="border-t-2 border-red-200 bg-red-50">
					<td class="sticky left-0 border-r border-gray-200 bg-red-50 px-3 py-1 z-10">
						<div class="flex items-center space-x-2">
							<div class="flex-shrink-0">
								<div class="flex h-7 w-7 items-center justify-center rounded-full bg-red-100">
									<svg
										class="h-4 w-4 text-red-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
										/>
									</svg>
								</div>
							</div>
							<div class="min-w-0 flex-1">
								<p class="text-sm font-medium text-red-900">Unassigned Shifts</p>
								<p class="text-xs text-red-600">Needs attention</p>
							</div>
						</div>
					</td>

					{#each weekDates as date}
						{@const unassignedShifts = getUnassignedShiftsForDate(date)}

						<td class="relative border-r border-gray-200 w-[120px] align-top overflow-hidden bg-red-50
							{unassignedShifts.length === 0 ? 'cursor-crosshair' : ''}
							{unassignedShifts.length === 0 ? 'hover:bg-red-100' : ''}
							{uiState.dragCreateState.previewCells.some(cell =>
								cell.employeeId === null &&
								cell.date.toDateString() === date.toDateString()) ?
								(uiState.dragCreateState.direction === 'horizontal' ? 'drag-create-horizontal-target' : 'drag-create-vertical-target') : ''}"
							style="height: {unassignedShifts.length === 0 ? '40px' : 'auto'}; min-height: 40px; max-width: 120px;"
							data-schedule-cell="true"
							data-unassigned-row="true"
							data-date={date.toISOString()}
						>
							<div class="group relative {unassignedShifts.length > 0 ? 'space-y-0.5' : 'h-[40px] flex flex-col'}" style="{unassignedShifts.length === 0 ? 'height: 40px;' : 'min-height: 40px;'}">
								<!-- Existing unassigned shifts -->
								{#each unassignedShifts as shift}
									<ShiftBlock
										{shift}
										venue={schedule.venues.find((v) => v.id === shift.venueId)}
										{uiState}
										onClick={() => onShiftClick(shift)}
										onDelete={onShiftDelete}
										onDuplicate={handleDuplicateShift}
										onDragCreateComplete={onDragCreateComplete}
										isUnassigned={true}
									/>
								{/each}

								<!-- Always show "+" button for creating additional unassigned shifts -->
								{#if unassignedShifts.length === 0}
									<!-- Empty cell - full height "+" button with drag support -->
									<button
										onclick={(e) => {
											console.log('🔄 ScheduleGrid: + button clicked for unassigned shift on date:', date.toDateString());
											e.preventDefault();
											e.stopPropagation();
											handleCreateUnassignedShift(date);
										}}
										onmousedown={(e) => {
											// Check if this is a potential drag operation
											if (e.button === 0) { // Left mouse button
												handleUnassignedCellDragStart(e, date);
											}
										}}
										class="flex h-full min-h-[40px] w-full items-center justify-center rounded
											border border-dashed border-red-300 opacity-0 group-hover:opacity-100
											text-red-400 transition-all duration-200 hover:border-red-400 hover:bg-red-100 hover:text-red-600
											{uiState.dragCreateState.isActive ? 'drag-create-cursor' : ''}"
										aria-label="Create unassigned shift on {date.toLocaleDateString()} or drag to create multiple"
										title="Click to create shift, or drag horizontally for multiple days or vertically for multiple unassigned shifts"
										data-unassigned-cell="true"
										data-unassigned-row="true"
										data-date={date.toISOString()}
									>
										<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M12 6v6m0 0v6m0-6h6m-6 0H6"
											/>
										</svg>
									</button>
								{:else}
									<!-- Compact "+" button when shifts exist with drag support -->
									<button
										onclick={(e) => {
											console.log('🔄 ScheduleGrid: + button clicked for additional unassigned shift on date:', date.toDateString());
											e.preventDefault();
											e.stopPropagation();
											handleCreateUnassignedShift(date);
										}}
										onmousedown={(e) => {
											// Check if this is a potential drag operation
											if (e.button === 0) { // Left mouse button
												handleUnassignedCellDragStart(e, date);
											}
										}}
										class="flex h-[20px] w-full items-center justify-center rounded mt-0.5
											border border-dashed border-red-300 opacity-0 group-hover:opacity-100
											text-red-400 transition-all duration-200 hover:border-red-400 hover:bg-red-100 hover:text-red-600
											{uiState.dragCreateState.isActive ? 'drag-create-cursor' : ''}"
										aria-label="Add another unassigned shift on {date.toLocaleDateString()} or drag to create multiple"
										title="Click to create shift, or drag horizontally for multiple days or vertically for multiple unassigned shifts"
										data-unassigned-cell="true"
										data-unassigned-row="true"
										data-date={date.toISOString()}
									>
										<svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M12 6v6m0 0v6m0-6h6m-6 0H6"
											/>
										</svg>
									</button>
								{/if}
							</div>
						</td>
					{/each}

					<td class="bg-red-50 text-center">
						<div class="text-[12.5px] font-semibold text-red-900">
							{schedule.shifts.filter((s) => !s.employeeId).length} shifts
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</div>

	<!-- Mobile View (simplified) -->
	{#if uiState.isMobileView}
		<div class="p-4 md:hidden">
			<div class="py-8 text-center text-gray-500">
				<svg
					class="mx-auto mb-4 h-12 w-12 text-gray-400"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
					/>
				</svg>
				<p class="mb-2 text-lg font-medium text-gray-900">Mobile View</p>
				<p class="text-sm text-gray-500">
					Full scheduling features are available on desktop.<br />
					Use mobile for viewing shifts only.
				</p>
			</div>
		</div>
	{/if}
</div>

<script lang="ts">
	import { onMount } from 'svelte';
	import type { Location } from '$lib/types.js';
	import { locationService } from '$lib/services/locationService.js';
	import Button from './ui/Button.svelte';

	// Props
	interface Props {
		isOpen?: boolean;
		onClose?: () => void;
	}

	let { isOpen = false, onClose }: Props = $props();

	// State
	let locations: Location[] = $state([]);
	let isLoading = $state(false);
	let showCreateForm = $state(false);
	let editingLocation: Location | null = $state(null);

	// Form state
	let formData = $state({
		name: '',
		addressLine1: '',
		addressLine2: '',
		city: '',
		postalCode: '',
		country: '',
		contactName: '',
		contactEmail: '',
		contactPhone: '',
		sortOrder: 0
	});

	// Load locations on mount
	onMount(async () => {
		if (isOpen) {
			await loadLocations();
		}
	});

	// Watch for isOpen changes
	$effect(() => {
		if (isOpen) {
			loadLocations();
		}
	});

	async function loadLocations() {
		isLoading = true;
		try {
			locations = await locationService.fetchAllLocations();
			console.log('✅ LocationManagement: Locations loaded:', locations);
		} catch (error) {
			console.error('❌ LocationManagement: Failed to load locations:', error);
		} finally {
			isLoading = false;
		}
	}

	function openCreateForm() {
		resetForm();
		editingLocation = null;
		showCreateForm = true;
	}

	function openEditForm(location: Location) {
		formData = {
			name: location.name,
			addressLine1: location.addressLine1 || '',
			addressLine2: location.addressLine2 || '',
			city: location.city || '',
			postalCode: location.postalCode || '',
			country: location.country || '',
			contactName: location.contactName || '',
			contactEmail: location.contactEmail || '',
			contactPhone: location.contactPhone || '',
			sortOrder: location.sortOrder
		};
		editingLocation = location;
		showCreateForm = true;
	}

	function resetForm() {
		formData = {
			name: '',
			addressLine1: '',
			addressLine2: '',
			city: '',
			postalCode: '',
			country: '',
			contactName: '',
			contactEmail: '',
			contactPhone: '',
			sortOrder: 0
		};
	}

	function closeForm() {
		showCreateForm = false;
		editingLocation = null;
		resetForm();
	}

	async function handleSubmit(event: Event) {
		event.preventDefault();
		try {
			if (editingLocation) {
				await locationService.updateLocation(editingLocation.id, formData);
				console.log('✅ LocationManagement: Location updated');
			} else {
				await locationService.createLocation(formData);
				console.log('✅ LocationManagement: Location created');
			}

			await loadLocations();
			closeForm();
		} catch (error) {
			console.error('❌ LocationManagement: Failed to save location:', error);
			alert(`Failed to save location: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	function handleClose() {
		onClose?.();
	}
</script>

{#if isOpen}
	<!-- Modal Backdrop -->
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
		<div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
			<!-- Header -->
			<div class="flex items-center justify-between p-6 border-b border-gray-200">
				<h2 class="text-xl font-semibold text-gray-900">Location Management</h2>
				<button
					onclick={handleClose}
					class="text-gray-400 hover:text-gray-600 transition-colors"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
					</svg>
				</button>
			</div>

			<!-- Content -->
			<div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
				{#if showCreateForm}
					<!-- Create/Edit Form -->
					<div class="mb-6">
						<h3 class="text-lg font-medium text-gray-900 mb-4">
							{editingLocation ? 'Edit Location' : 'Create New Location'}
						</h3>

						<form onsubmit={handleSubmit} class="space-y-4">
							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
									<input
										type="text"
										bind:value={formData.name}
										required
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
									/>
								</div>

								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
									<input
										type="number"
										bind:value={formData.sortOrder}
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
									/>
								</div>
							</div>

							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
								<input
									type="text"
									bind:value={formData.addressLine1}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								/>
							</div>

							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Address Line 2</label>
								<input
									type="text"
									bind:value={formData.addressLine2}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								/>
							</div>

							<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">City</label>
									<input
										type="text"
										bind:value={formData.city}
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
									/>
								</div>

								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
									<input
										type="text"
										bind:value={formData.postalCode}
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
									/>
								</div>

								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
									<input
										type="text"
										bind:value={formData.country}
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
									/>
								</div>
							</div>

							<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Contact Name</label>
									<input
										type="text"
										bind:value={formData.contactName}
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
									/>
								</div>

								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
									<input
										type="email"
										bind:value={formData.contactEmail}
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
									/>
								</div>

								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
									<input
										type="tel"
										bind:value={formData.contactPhone}
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
									/>
								</div>
							</div>

							<div class="flex justify-end space-x-3 pt-4">
								<Button variant="secondary" onclick={closeForm}>Cancel</Button>
								<Button type="submit" variant="primary">
									{editingLocation ? 'Update Location' : 'Create Location'}
								</Button>
							</div>
						</form>
					</div>
				{:else}
					<!-- Location List -->
					<div class="mb-4">
						<Button onclick={openCreateForm} variant="primary">Add New Location</Button>
					</div>

					{#if isLoading}
						<div class="text-center py-8">
							<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
							<p class="mt-2 text-gray-600">Loading locations...</p>
						</div>
					{:else if locations.length === 0}
						<div class="text-center py-8">
							<p class="text-gray-600">No locations found. Create your first location to get started.</p>
						</div>
					{:else}
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							{#each locations as location (location.id)}
								<div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
									<div class="flex justify-between items-start mb-2">
										<h3 class="font-medium text-gray-900">{location.name}</h3>
										<button
											onclick={() => openEditForm(location)}
											class="text-blue-600 hover:text-blue-800 text-sm"
										>
											Edit
										</button>
									</div>

									{#if location.addressLine1}
										<p class="text-sm text-gray-600">{location.addressLine1}</p>
									{/if}

									{#if location.city || location.postalCode}
										<p class="text-sm text-gray-600">
											{location.city}{location.city && location.postalCode ? ', ' : ''}{location.postalCode}
										</p>
									{/if}

									{#if location.contactName}
										<p class="text-sm text-gray-600 mt-2">
											<span class="font-medium">Contact:</span> {location.contactName}
										</p>
									{/if}

									{#if location.contactEmail}
										<p class="text-sm text-gray-600">
											<span class="font-medium">Email:</span> {location.contactEmail}
										</p>
									{/if}
								</div>
							{/each}
						</div>
					{/if}
				{/if}
			</div>
		</div>
	</div>
{/if}

<script lang="ts">
	import type { WeeklySummary, Venue } from '$lib/types.js';
	import { formatCurrency } from '$lib/utils.js';

	interface Props {
		weeklySummary: WeeklySummary;
		venues: Venue[];
		onMarkAllPaid: () => void;
		onExport: (format: 'csv' | 'pdf', venueId?: string) => void;
	}

	let { weeklySummary, venues, onMarkAllPaid, onExport }: Props = $props();

	// Export options
	let showExportMenu = $state(false);
	let selectedVenue = $state<string>('all');

	function handleExport(format: 'csv' | 'pdf') {
		const venueId = selectedVenue === 'all' ? undefined : selectedVenue;
		onExport(format, venueId);
		showExportMenu = false;
	}

	function handleMarkAllPaid() {
		// Calculate unpaid shifts count from employee summaries
		const unpaidShiftsCount = weeklySummary.employeeSummaries.reduce((total, emp) => {
			// Estimate unpaid shifts for this employee based on their unpaid amount vs total pay
			const empUnpaidShifts = emp.totalPay > 0 ? Math.round((emp.unpaidAmount / emp.totalPay) * emp.shiftsCount) : 0;
			return total + empUnpaidShifts;
		}, 0);

		const message = `Mark all ${unpaidShiftsCount} unpaid shifts as paid?\n\nTotal amount: ${formatCurrency(weeklySummary.unpaidAmount)}\n\nThis action cannot be undone.`;

		if (confirm(message)) {
			onMarkAllPaid();
			// Show success feedback
			setTimeout(() => {
				alert(`✅ Successfully marked ${unpaidShiftsCount} shifts as paid!`);
			}, 100);
		}
	}

	// Close export menu when clicking outside
	function handleClickOutside(event: MouseEvent) {
		if (showExportMenu && !(event.target as Element).closest('.export-menu')) {
			showExportMenu = false;
		}
	}
</script>

<svelte:window onclick={handleClickOutside} />

<footer class="border-t border-gray-200 bg-white px-6 py-4">
	<div class="flex items-center justify-between">
		<!-- Weekly Summary -->
		<div class="flex items-center space-x-8">
			<div class="flex items-center space-x-4">
				<div class="text-center">
					<div class="text-lg font-bold text-gray-900">
						{formatCurrency(weeklySummary.totalPay)}
					</div>
					<div class="text-xs text-gray-500">Total Pay</div>
				</div>

				<div class="text-center">
					<div class="text-lg font-bold text-green-600">
						{formatCurrency(weeklySummary.paidAmount)}
					</div>
					<div class="text-xs text-gray-500">Paid</div>
				</div>

				<div class="text-center">
					<div class="text-lg font-bold text-red-600">
						{formatCurrency(weeklySummary.unpaidAmount)}
					</div>
					<div class="text-xs text-gray-500">Unpaid</div>
				</div>

				{#if weeklySummary.advanceDeductions > 0}
					<div class="text-center">
						<div class="text-lg font-bold text-orange-600">
							{formatCurrency(weeklySummary.advanceDeductions)}
						</div>
						<div class="text-xs text-gray-500">Advances</div>
					</div>
				{/if}
			</div>

			<!-- Additional Stats -->
			<div class="hidden items-center space-x-4 text-sm text-gray-600 md:flex">
				<div class="flex items-center space-x-1">
					<span class="font-medium">{weeklySummary.totalHours.toFixed(1)}</span>
					<span>total hours</span>
				</div>

				{#if weeklySummary.unassignedShifts > 0}
					<div class="flex items-center space-x-1 text-red-600">
						<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
							/>
						</svg>
						<span class="font-medium">{weeklySummary.unassignedShifts}</span>
						<span>unassigned shifts</span>
					</div>
				{/if}
			</div>
		</div>

		<!-- Action Buttons -->
		<div class="flex items-center space-x-3">
			<!-- Mark All Paid Button -->
			{#if weeklySummary.unpaidAmount > 0}
				<button
					onclick={handleMarkAllPaid}
					class="flex items-center space-x-2 rounded-md bg-green-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-green-700"
				>
					<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
						/>
					</svg>
					<span>Mark All as Paid</span>
				</button>
			{/if}

			<!-- Export Button -->
			<div class="export-menu relative">
				<button
					onclick={() => (showExportMenu = !showExportMenu)}
					class="flex items-center space-x-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700"
				>
					<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
						/>
					</svg>
					<span>Export</span>
					<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M19 9l-7 7-7-7"
						/>
					</svg>
				</button>

				{#if showExportMenu}
					<div
						class="absolute right-0 bottom-full z-50 mb-2 w-64 rounded-lg border border-gray-200 bg-white py-2 shadow-lg"
					>
						<!-- Export Options Header -->
						<div class="border-b border-gray-200 px-4 py-2">
							<h3 class="text-sm font-medium text-gray-900">Export Schedule</h3>
						</div>

						<!-- Venue Filter -->
						<div class="border-b border-gray-200 px-4 py-3">
							<label for="venue-filter" class="mb-2 block text-xs font-medium text-gray-700">
								Filter by Venue
							</label>
							<select
								id="venue-filter"
								bind:value={selectedVenue}
								class="w-full rounded-md border border-gray-300 px-3 py-1 text-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
							>
								<option value="all">All Venues</option>
								{#each venues as venue}
									<option value={venue.id}>{venue.name}</option>
								{/each}
							</select>
						</div>

						<!-- Export Format Options -->
						<div class="py-2">
							<button
								onclick={() => handleExport('csv')}
								class="flex w-full items-center space-x-3 px-4 py-2 text-left text-sm text-gray-700 transition-colors hover:bg-gray-100"
							>
								<svg
									class="h-4 w-4 text-green-600"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
									/>
								</svg>
								<div>
									<div class="font-medium">Export as CSV</div>
									<div class="text-xs text-gray-500">Spreadsheet format</div>
								</div>
							</button>

							<button
								onclick={() => handleExport('pdf')}
								class="flex w-full items-center space-x-3 px-4 py-2 text-left text-sm text-gray-700 transition-colors hover:bg-gray-100"
							>
								<svg
									class="h-4 w-4 text-red-600"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 1H7a2 2 0 00-2 2v16a2 2 0 002 2z"
									/>
								</svg>
								<div>
									<div class="font-medium">Export as PDF</div>
									<div class="text-xs text-gray-500">Printable format</div>
								</div>
							</button>
						</div>

						<!-- Export Info -->
						<div class="border-t border-gray-200 px-4 py-2">
							<div class="text-xs text-gray-500">
								{selectedVenue === 'all'
									? 'All venues'
									: venues.find((v) => v.id === selectedVenue)?.name} • Current week
							</div>
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>

	<!-- Mobile Summary (collapsed view) -->
	<div class="mt-4 border-t border-gray-200 pt-4 md:hidden">
		<div class="grid grid-cols-3 gap-4 text-center">
			<div>
				<div class="text-sm font-bold text-gray-900">
					{formatCurrency(weeklySummary.totalPay)}
				</div>
				<div class="text-xs text-gray-500">Total</div>
			</div>
			<div>
				<div class="text-sm font-bold text-green-600">
					{formatCurrency(weeklySummary.paidAmount)}
				</div>
				<div class="text-xs text-gray-500">Paid</div>
			</div>
			<div>
				<div class="text-sm font-bold text-red-600">
					{formatCurrency(weeklySummary.unpaidAmount)}
				</div>
				<div class="text-xs text-gray-500">Unpaid</div>
			</div>
		</div>
	</div>
</footer>

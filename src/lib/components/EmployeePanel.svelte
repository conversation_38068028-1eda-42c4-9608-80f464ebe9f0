<script lang="ts">
	import type { Employee, Venue } from '$lib/types.js';
	import { formatCurrency } from '$lib/utils.js';
	import BulkEmployeeDeleteModal from './BulkEmployeeDeleteModal.svelte';

	interface Props {
		employees: Employee[];
		venues: Venue[];
		isCollapsed: boolean;
		onToggleCollapse: () => void;
		onEmployeeEdit: (employee: Employee) => void;
		onEmployeeAdd: () => void;
		onBulkEmployeeDelete?: (employeeIds: string[]) => Promise<{ deletedCount: number; message: string }>;
	}

	let { employees, venues, isCollapsed, onToggleCollapse, onEmployeeEdit, onEmployeeAdd, onBulkEmployeeDelete }: Props =
		$props();

	// Filter active employees
	let activeEmployees = $derived(employees.filter((emp) => emp.isActive));
	let inactiveEmployees = $derived(employees.filter((emp) => !emp.isActive));

	// Show inactive employees toggle
	let showInactive = $state(false);

	// Bulk delete modal state
	let isBulkEmployeeDeleteModalOpen = $state(false);

	// Functions
	function openBulkEmployeeDeleteModal() {
		if (onBulkEmployeeDelete && employees.length > 0) {
			isBulkEmployeeDeleteModalOpen = true;
		}
	}

	function closeBulkEmployeeDeleteModal() {
		isBulkEmployeeDeleteModalOpen = false;
	}

	async function handleBulkEmployeeDelete(employeeIds: string[]): Promise<{ deletedCount: number; message: string }> {
		if (!onBulkEmployeeDelete) {
			throw new Error('Bulk delete function not provided');
		}

		try {
			const result = await onBulkEmployeeDelete(employeeIds);
			closeBulkEmployeeDeleteModal();
			return result;
		} catch (error) {
			throw error; // Re-throw for modal error handling
		}
	}
</script>

<div class="flex h-full flex-col border-r border-gray-200 bg-white">
	<!-- Panel Header -->
	<div class="flex items-center justify-between border-b border-gray-200 p-4">
		{#if !isCollapsed}
			<h2 class="text-lg font-semibold text-gray-900">Employees</h2>
		{/if}

		<div class="flex items-center space-x-2">
			{#if !isCollapsed}
				<button
					onclick={onEmployeeAdd}
					class="rounded-md p-2 text-blue-600 transition-colors hover:bg-blue-50"
					aria-label="Add Employee"
				>
					<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 6v6m0 0v6m0-6h6m-6 0H6"
						/>
					</svg>
				</button>

				{#if onBulkEmployeeDelete && employees.length > 0}
					<button
						onclick={openBulkEmployeeDeleteModal}
						class="rounded-md p-2 text-red-600 transition-colors hover:bg-red-50"
						aria-label="Bulk Delete Employees"
						title="Bulk Delete Employees"
					>
						<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
							/>
						</svg>
					</button>
				{/if}
			{/if}

			<button
				onclick={onToggleCollapse}
				class="rounded-md p-2 text-gray-500 transition-colors hover:bg-gray-100"
				aria-label={isCollapsed ? 'Expand Panel' : 'Collapse Panel'}
			>
				<svg
					class="h-5 w-5 transform {isCollapsed ? 'rotate-180' : ''} transition-transform"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
					/>
				</svg>
			</button>
		</div>
	</div>

	{#if isCollapsed}
		<!-- Collapsed View -->
		<div class="flex-1 overflow-y-auto p-2">
			<div class="space-y-2">
				{#each activeEmployees as employee}
					<button
						onclick={() => onEmployeeEdit(employee)}
						class="group w-full rounded-md p-2 text-left transition-colors hover:bg-gray-50"
						title={employee.name}
					>
						<div
							class="mx-auto flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 sm:h-7 sm:w-7 md:h-8 md:w-8"
						>
							<span class="text-[10px] font-medium text-blue-700 sm:text-xs md:text-sm">
								{(employee.name || employee.fullName || '')
									.split(' ')
									.map((n) => n[0])
									.join('')}
							</span>
						</div>
					</button>
				{/each}
			</div>
		</div>
	{:else}
		<!-- Expanded View -->
		<div class="flex-1 overflow-y-auto">
			<!-- Active Employees -->
			<div class="p-4">
				<div class="space-y-3">
					{#each activeEmployees as employee}
						<div class="group">
							<button
								onclick={() => onEmployeeEdit(employee)}
								class="w-full rounded-lg border border-gray-200 bg-gray-50 p-3 text-left transition-colors hover:bg-gray-100"
							>
								<div class="flex items-center space-x-3">
									<div class="flex-shrink-0">
										<div
											class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 sm:h-9 sm:w-9 md:h-10 md:w-10"
										>
											<span class="text-xs font-medium text-blue-700 sm:text-sm md:text-base">
												{(employee.name || employee.fullName || '')
													.split(' ')
													.map((n) => n[0])
													.join('')}
											</span>
										</div>
									</div>
									<div class="min-w-0 flex-1">
										<p class="truncate text-sm font-medium text-gray-900">
											{employee.name}
										</p>
										<p class="truncate text-xs text-gray-500">
											{employee.role}
										</p>
										<p class="text-xs text-gray-600">
											{formatCurrency(employee.defaultDailyRate)}/day
										</p>
									</div>
									<div class="flex-shrink-0">
										<svg
											class="h-4 w-4 text-gray-400 group-hover:text-gray-600"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M9 5l7 7-7 7"
											/>
										</svg>
									</div>
								</div>
							</button>
						</div>
					{/each}

					{#if activeEmployees.length === 0}
						<div class="py-8 text-center">
							<svg
								class="mx-auto mb-4 h-12 w-12 text-gray-400"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
								/>
							</svg>
							<p class="mb-4 text-sm text-gray-500">No active employees</p>
							<button
								onclick={onEmployeeAdd}
								class="rounded-md bg-blue-600 px-4 py-2 text-sm text-white transition-colors hover:bg-blue-700"
							>
								Add First Employee
							</button>
						</div>
					{/if}
				</div>
			</div>

			<!-- Inactive Employees Section -->
			{#if inactiveEmployees.length > 0}
				<div class="border-t border-gray-200">
					<button
						onclick={() => (showInactive = !showInactive)}
						class="flex w-full items-center justify-between px-4 py-3 text-left text-sm text-gray-600 transition-colors hover:bg-gray-50"
					>
						<span>Inactive Employees ({inactiveEmployees.length})</span>
						<svg
							class="h-4 w-4 transform {showInactive ? 'rotate-180' : ''} transition-transform"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M19 9l-7 7-7-7"
							/>
						</svg>
					</button>

					{#if showInactive}
						<div class="px-4 pb-4">
							<div class="space-y-2">
								{#each inactiveEmployees as employee}
									<button
										onclick={() => onEmployeeEdit(employee)}
										class="w-full rounded-md bg-gray-50 p-2 text-left opacity-60 transition-colors hover:bg-gray-100"
									>
										<div class="flex items-center space-x-3">
											<div class="flex-shrink-0">
												<div
													class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 sm:h-7 sm:w-7 md:h-8 md:w-8"
												>
													<span class="text-[10px] font-medium text-gray-600 sm:text-xs md:text-sm">
														{(employee.name || employee.fullName || '')
															.split(' ')
															.map((n) => n[0])
															.join('')}
													</span>
												</div>
											</div>
											<div class="min-w-0 flex-1">
												<p class="truncate text-sm text-gray-700">
													{employee.name}
												</p>
												<p class="truncate text-xs text-gray-500">
													{employee.role} (Inactive)
												</p>
											</div>
										</div>
									</button>
								{/each}
							</div>
						</div>
					{/if}
				</div>
			{/if}
		</div>

		<!-- Panel Footer -->
		<div class="border-t border-gray-200 p-4">
			<!-- Venue Legend -->
			<div class="mb-4">
				<h3 class="mb-2 text-sm font-medium text-gray-900">Venues</h3>
				<div class="space-y-1">
					{#each venues as venue}
						<div class="flex items-center space-x-2">
							<div
								class="h-3 w-3 flex-shrink-0 rounded-full"
								style="background-color: {venue.color}"
							></div>
							<span class="truncate text-xs text-gray-600">{venue.name}</span>
						</div>
					{/each}
				</div>
			</div>

			<!-- Status Legend -->
			<div>
				<h3 class="mb-2 text-sm font-medium text-gray-900">Status</h3>
				<div class="space-y-1">
					<div class="flex items-center space-x-2">
						<div class="h-3 w-3 rounded-full bg-gray-200"></div>
						<span class="text-xs text-gray-600">Unassigned</span>
					</div>
					<div class="flex items-center space-x-2">
						<div class="h-3 w-3 rounded-full bg-blue-500"></div>
						<span class="text-xs text-gray-600">Scheduled</span>
					</div>
					<div class="flex items-center space-x-2">
						<div class="h-3 w-3 rounded-full bg-green-500"></div>
						<span class="text-xs text-gray-600">Paid</span>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>

<!-- Bulk Employee Delete Modal -->
{#if isBulkEmployeeDeleteModalOpen}
	<BulkEmployeeDeleteModal
		isOpen={isBulkEmployeeDeleteModalOpen}
		{employees}
		onClose={closeBulkEmployeeDeleteModal}
		onBulkDelete={handleBulkEmployeeDelete}
	/>
{/if}

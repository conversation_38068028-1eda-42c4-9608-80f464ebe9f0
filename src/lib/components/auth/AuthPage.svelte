<script lang="ts">
	/**
	 * Authentication Page Component
	 * Handles switching between login and signup forms
	 * Following Code Complete principles: Clear state management, user experience
	 */
	
	import LoginForm from './LoginForm.svelte';
	import SignupForm from './SignupForm.svelte';
	
	// Props
	interface Props {
		onAuthSuccess?: () => void;
	}
	
	let { onAuthSuccess }: Props = $props();
	
	// State
	let mode = $state<'login' | 'signup'>('login');
	
	/**
	 * Switch to signup mode
	 */
	function switchToSignup(): void {
		mode = 'signup';
	}
	
	/**
	 * Switch to login mode
	 */
	function switchToLogin(): void {
		mode = 'login';
	}
	
	/**
	 * Handle successful authentication
	 */
	function handleAuthSuccess(): void {
		onAuthSuccess?.();
	}
</script>

<!-- Authentication Page Container -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
	<div class="w-full max-w-md">
		<!-- Logo/Branding Section -->
		<div class="text-center mb-8">
			<div class="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
				<svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
				</svg>
			</div>
			<h1 class="text-3xl font-bold text-gray-900 mb-2">
				Restaurant Scheduler
			</h1>
			<p class="text-gray-600">
				Professional staff scheduling made simple
			</p>
		</div>
		
		<!-- Authentication Forms -->
		{#if mode === 'login'}
			<LoginForm 
				onSuccess={handleAuthSuccess}
				onSwitchToSignup={switchToSignup}
			/>
		{:else}
			<SignupForm 
				onSuccess={handleAuthSuccess}
				onSwitchToLogin={switchToLogin}
			/>
		{/if}
		
		<!-- Features Section -->
		<div class="mt-12 text-center">
			<h2 class="text-lg font-semibold text-gray-900 mb-4">
				Why Choose Restaurant Scheduler?
			</h2>
			<div class="grid grid-cols-1 gap-4 text-sm text-gray-600">
				<div class="flex items-center justify-center space-x-2">
					<svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
					</svg>
					<span>Drag & drop scheduling interface</span>
				</div>
				<div class="flex items-center justify-center space-x-2">
					<svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
					</svg>
					<span>Automatic payroll calculations</span>
				</div>
				<div class="flex items-center justify-center space-x-2">
					<svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
					</svg>
					<span>Multi-venue support</span>
				</div>
				<div class="flex items-center justify-center space-x-2">
					<svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
					</svg>
					<span>Mobile responsive design</span>
				</div>
			</div>
		</div>
		
		<!-- Security Notice -->
		<div class="mt-8 text-center">
			<p class="text-xs text-gray-500">
				🔒 Your data is secured with enterprise-grade encryption
			</p>
		</div>
	</div>
</div>

<style>
	/* Custom gradient background */
	.bg-gradient-to-br {
		background-image: linear-gradient(to bottom right, 
			rgb(239 246 255), 
			rgb(255 255 255), 
			rgb(239 246 255)
		);
	}
	
	/* Subtle animation for the logo */
	.mx-auto.h-16.w-16 {
		animation: pulse 2s infinite;
	}
	
	@keyframes pulse {
		0%, 100% {
			opacity: 1;
		}
		50% {
			opacity: 0.8;
		}
	}
</style>

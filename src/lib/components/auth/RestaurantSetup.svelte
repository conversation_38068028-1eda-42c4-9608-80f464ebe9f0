<!--
	Restaurant Setup Component
	Handles first-time restaurant creation for new users
	Following Code Complete principles: Clear user flow, proper validation
-->
<script lang="ts">
	import { Button, Input, ErrorDisplay, LoadingSpinner } from '$lib/components/ui/index.js';
	import { supabase } from '$lib/supabaseClient.js';

	interface Props {
		onComplete: (restaurantId: string) => void;
	}

	let { onComplete }: Props = $props();

	// Component state
	let restaurantName = $state('');
	let isLoading = $state(false);
	let error = $state<string | null>(null);

	/**
	 * Create restaurant for new user
	 */
	async function createRestaurant(): Promise<void> {
		if (!restaurantName.trim()) {
			error = 'Restaurant name is required';
			return;
		}

		try {
			isLoading = true;
			error = null;

			// Create restaurant using RPC
			const { data, error: createError } = await supabase.rpc('create_restaurant', {
				p_name: restaurantName.trim()
			});

			if (createError) {
				throw createError;
			}

			if (!data || data.length === 0) {
				throw new Error('Failed to create restaurant');
			}

			const restaurant = data[0];
			onComplete(restaurant.id);

		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to create restaurant';
			console.error('❌ Failed to create restaurant:', err);
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Create default restaurant automatically
	 */
	async function createDefaultRestaurant(): Promise<void> {
		try {
			isLoading = true;
			error = null;

			// Create default restaurant and migrate existing data
			const { data, error: createError } = await supabase.rpc('create_default_restaurant_for_user');

			if (createError) {
				throw createError;
			}

			if (!data) {
				throw new Error('Failed to create default restaurant');
			}

			onComplete(data);

		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to create default restaurant';
			console.error('❌ Failed to create default restaurant:', err);
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Handle form submission
	 */
	function handleSubmit(event: Event): void {
		event.preventDefault();
		createRestaurant();
	}
</script>

<!-- Restaurant Setup Container -->
<div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
	<div class="max-w-md w-full space-y-8">
		<!-- Header -->
		<div class="text-center">
			<h2 class="mt-6 text-3xl font-extrabold text-gray-900">
				Set Up Your Restaurant
			</h2>
			<p class="mt-2 text-sm text-gray-600">
				Create your restaurant to start managing schedules
			</p>
		</div>

		{#if error}
			<ErrorDisplay
				title="Setup Failed"
				message={error}
				showRetry={false}
			/>
		{/if}

		<!-- Restaurant Setup Form -->
		<form class="mt-8 space-y-6" onsubmit={handleSubmit}>
			<div class="space-y-4">
				<!-- Restaurant Name -->
				<div>
					<Input
						id="restaurant-name"
						type="text"
						label="Restaurant Name"
						placeholder="Enter your restaurant name"
						bind:value={restaurantName}
						required={true}
						disabled={isLoading}
						error={error && !restaurantName.trim() ? 'Restaurant name is required' : ''}
					/>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="space-y-3">
				<!-- Create Restaurant Button -->
				<Button
					type="submit"
					variant="primary"
					size="lg"
					fullWidth={true}
					disabled={isLoading || !restaurantName.trim()}
				>
					{#if isLoading}
						<LoadingSpinner size="sm" />
						Creating Restaurant...
					{:else}
						Create Restaurant
					{/if}
				</Button>

				<!-- Quick Setup Button -->
				<Button
					type="button"
					variant="secondary"
					size="lg"
					fullWidth={true}
					disabled={isLoading}
					onclick={createDefaultRestaurant}
				>
					{#if isLoading}
						<LoadingSpinner size="sm" />
						Setting Up...
					{:else}
						Quick Setup (Use "My Restaurant")
					{/if}
				</Button>
			</div>

			<!-- Help Text -->
			<div class="text-center">
				<p class="text-xs text-gray-500">
					You can change your restaurant name later in settings
				</p>
			</div>
		</form>
	</div>
</div>

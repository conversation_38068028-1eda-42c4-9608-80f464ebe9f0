<script lang="ts">
	/**
	 * Signup Form Component
	 * Handles new user registration with email/password
	 * Following Code Complete principles: Clear validation, error handling
	 */

	import { signUp } from '$lib/stores/auth.js';
	import { Button, Input, LoadingSpinner } from '$lib/components/ui/index.js';

	// Props
	interface Props {
		onSuccess?: () => void;
		onSwitchToLogin?: () => void;
	}

	let { onSuccess, onSwitchToLogin }: Props = $props();

	// Form state
	let email = $state('');
	let password = $state('');
	let confirmPassword = $state('');
	let restaurantName = $state('');
	let isLoading = $state(false);
	let error = $state<string | null>(null);
	let success = $state<string | null>(null);

	// Validation state
	let emailError = $state<string | null>(null);
	let passwordError = $state<string | null>(null);
	let confirmPasswordError = $state<string | null>(null);
	let restaurantNameError = $state<string | null>(null);

	/**
	 * Validate form inputs
	 */
	function validateForm(): boolean {
		let isValid = true;

		// Reset errors
		emailError = null;
		passwordError = null;
		confirmPasswordError = null;
		restaurantNameError = null;

		// Email validation
		if (!email.trim()) {
			emailError = 'Email is required';
			isValid = false;
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
			emailError = 'Please enter a valid email address';
			isValid = false;
		}

		// Password validation
		if (!password) {
			passwordError = 'Password is required';
			isValid = false;
		} else if (password.length < 8) {
			passwordError = 'Password must be at least 8 characters';
			isValid = false;
		} else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
			passwordError = 'Password must contain uppercase, lowercase, and number';
			isValid = false;
		}

		// Confirm password validation
		if (!confirmPassword) {
			confirmPasswordError = 'Please confirm your password';
			isValid = false;
		} else if (password !== confirmPassword) {
			confirmPasswordError = 'Passwords do not match';
			isValid = false;
		}

		// Restaurant name validation
		if (!restaurantName.trim()) {
			restaurantNameError = 'Restaurant name is required';
			isValid = false;
		} else if (restaurantName.trim().length < 2) {
			restaurantNameError = 'Restaurant name must be at least 2 characters';
			isValid = false;
		}

		return isValid;
	}

	/**
	 * Handle form submission
	 */
	async function handleSubmit(event: Event): Promise<void> {
		event.preventDefault();

		if (!validateForm()) {
			return;
		}

		try {
			isLoading = true;
			error = null;
			success = null;

			const result = await signUp(email.trim(), password);

			if (result.success) {
				success = 'Account created successfully! Please check your email to verify your account.';
				// Note: We'll create the restaurant after email verification
				setTimeout(() => {
					onSuccess?.();
				}, 3000);
			} else {
				error = result.error || 'Failed to create account';
			}
		} catch (err) {
			error = err instanceof Error ? err.message : 'An unexpected error occurred';
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Clear form errors when user starts typing
	 */
	function clearErrors(): void {
		error = null;
		success = null;
		emailError = null;
		passwordError = null;
		confirmPasswordError = null;
		restaurantNameError = null;
	}
</script>

<div class="mx-auto w-full max-w-md">
	<div class="rounded-lg bg-white p-8 shadow-lg">
		<!-- Header -->
		<div class="mb-8 text-center">
			<h1 class="mb-2 text-2xl font-bold text-gray-900">Create Your Account</h1>
			<p class="text-gray-600">Start managing your restaurant schedule today</p>
		</div>

		<!-- Success Message -->
		{#if success}
			<div class="mb-6 rounded-md border border-green-200 bg-green-50 p-4">
				<div class="flex">
					<div class="flex-shrink-0">
						<svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
							<path
								fill-rule="evenodd"
								d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
								clip-rule="evenodd"
							/>
						</svg>
					</div>
					<div class="ml-3">
						<p class="text-sm text-green-800">{success}</p>
					</div>
				</div>
			</div>
		{/if}

		<!-- Error Display -->
		{#if error}
			<div class="mb-6 rounded-md border border-red-200 bg-red-50 p-4">
				<div class="flex">
					<div class="flex-shrink-0">
						<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
							<path
								fill-rule="evenodd"
								d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
								clip-rule="evenodd"
							/>
						</svg>
					</div>
					<div class="ml-3">
						<p class="text-sm text-red-800">{error}</p>
					</div>
				</div>
			</div>
		{/if}

		<!-- Signup Form -->
		<form onsubmit={handleSubmit} class="space-y-6">
			<!-- Restaurant Name Field -->
			<div>
				<label for="restaurantName" class="mb-2 block text-sm font-medium text-gray-700">
					Restaurant Name
				</label>
				<Input
					id="restaurantName"
					type="text"
					bind:value={restaurantName}
					placeholder="Enter your restaurant name"
					error={restaurantNameError || undefined}
					disabled={isLoading}
					required
				/>
			</div>

			<!-- Email Field -->
			<div>
				<label for="email" class="mb-2 block text-sm font-medium text-gray-700">
					Email Address
				</label>
				<Input
					id="email"
					type="email"
					bind:value={email}
					placeholder="Enter your email"
					error={emailError || undefined}
					disabled={isLoading}
					required
					autocomplete="email"
				/>
			</div>

			<!-- Password Field -->
			<div>
				<label for="password" class="mb-2 block text-sm font-medium text-gray-700">
					Password
				</label>
				<Input
					id="password"
					type="password"
					bind:value={password}
					placeholder="Create a strong password"
					error={passwordError || undefined}
					disabled={isLoading}
					required
					autocomplete="new-password"
				/>
				<p class="mt-1 text-xs text-gray-500">
					Must be 8+ characters with uppercase, lowercase, and number
				</p>
			</div>

			<!-- Confirm Password Field -->
			<div>
				<label for="confirmPassword" class="mb-2 block text-sm font-medium text-gray-700">
					Confirm Password
				</label>
				<Input
					id="confirmPassword"
					type="password"
					bind:value={confirmPassword}
					placeholder="Confirm your password"
					error={confirmPasswordError || undefined}
					disabled={isLoading}
					required
					autocomplete="new-password"
				/>
			</div>

			<!-- Submit Button -->
			<Button
				type="submit"
				variant="primary"
				size="lg"
				fullWidth={true}
				disabled={isLoading || !!success}
				className="mt-8"
			>
				{#if isLoading}
					<LoadingSpinner size="sm" class="mr-2" />
					Creating Account...
				{:else if success}
					Account Created!
				{:else}
					Create Account
				{/if}
			</Button>
		</form>

		<!-- Footer Links -->
		<div class="mt-8 text-center">
			{#if onSwitchToLogin}
				<div class="border-t border-gray-200 pt-4">
					<p class="text-sm text-gray-600">
						Already have an account?
						<button
							type="button"
							onclick={onSwitchToLogin}
							class="font-medium text-blue-600 underline hover:text-blue-500"
							disabled={isLoading}
						>
							Sign in here
						</button>
					</p>
				</div>
			{/if}
		</div>
	</div>
</div>

<style>
	/* Additional styling for better visual hierarchy */
	.bg-white {
		box-shadow:
			0 10px 25px -5px rgba(0, 0, 0, 0.1),
			0 10px 10px -5px rgba(0, 0, 0, 0.04);
	}
</style>

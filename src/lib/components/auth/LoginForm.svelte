<script lang="ts">
	/**
	 * Login Form Component
	 * Handles user authentication with email/password
	 * Following Code Complete principles: Clear validation, error handling
	 */

	import { signIn } from '$lib/stores/auth.js';
	import { Button, Input, LoadingSpinner } from '$lib/components/ui/index.js';

	// Props
	interface Props {
		onSuccess?: () => void;
		onSwitchToSignup?: () => void;
	}

	let { onSuccess, onSwitchToSignup }: Props = $props();

	// Form state
	let email = $state('');
	let password = $state('');
	let isLoading = $state(false);
	let error = $state<string | null>(null);

	// Validation state
	let emailError = $state<string | null>(null);
	let passwordError = $state<string | null>(null);

	/**
	 * Validate form inputs
	 */
	function validateForm(): boolean {
		let isValid = true;

		// Reset errors
		emailError = null;
		passwordError = null;

		// Email validation
		if (!email.trim()) {
			emailError = 'Email is required';
			isValid = false;
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
			emailError = 'Please enter a valid email address';
			isValid = false;
		}

		// Password validation
		if (!password) {
			passwordError = 'Password is required';
			isValid = false;
		} else if (password.length < 6) {
			passwordError = 'Password must be at least 6 characters';
			isValid = false;
		}

		return isValid;
	}

	/**
	 * Handle form submission
	 */
	async function handleSubmit(event: Event): Promise<void> {
		event.preventDefault();

		if (!validateForm()) {
			return;
		}

		try {
			isLoading = true;
			error = null;

			const result = await signIn(email.trim(), password);

			if (result.success) {
				onSuccess?.();
			} else {
				error = result.error || 'Failed to sign in';
			}
		} catch (err) {
			error = err instanceof Error ? err.message : 'An unexpected error occurred';
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Clear form errors when user starts typing
	 */
	function clearErrors(): void {
		error = null;
		emailError = null;
		passwordError = null;
	}
</script>

<div class="mx-auto w-full max-w-md">
	<div class="rounded-lg bg-white p-8 shadow-lg">
		<!-- Header -->
		<div class="mb-8 text-center">
			<h1 class="mb-2 text-2xl font-bold text-gray-900">Welcome Back</h1>
			<p class="text-gray-600">Sign in to your restaurant scheduling account</p>
		</div>

		<!-- Error Display -->
		{#if error}
			<div class="mb-6 rounded-md border border-red-200 bg-red-50 p-4">
				<div class="flex">
					<div class="flex-shrink-0">
						<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
							<path
								fill-rule="evenodd"
								d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
								clip-rule="evenodd"
							/>
						</svg>
					</div>
					<div class="ml-3">
						<p class="text-sm text-red-800">{error}</p>
					</div>
				</div>
			</div>
		{/if}

		<!-- Login Form -->
		<form onsubmit={handleSubmit} class="space-y-6">
			<!-- Email Field -->
			<div>
				<label for="email" class="mb-2 block text-sm font-medium text-gray-700">
					Email Address
				</label>
				<Input
					id="email"
					type="email"
					bind:value={email}
					placeholder="Enter your email"
					error={emailError || undefined}
					disabled={isLoading}
					required
					autocomplete="email"
				/>
			</div>

			<!-- Password Field -->
			<div>
				<label for="password" class="mb-2 block text-sm font-medium text-gray-700">
					Password
				</label>
				<Input
					id="password"
					type="password"
					bind:value={password}
					placeholder="Enter your password"
					error={passwordError || undefined}
					disabled={isLoading}
					required
					autocomplete="current-password"
				/>
			</div>

			<!-- Submit Button -->
			<Button
				type="submit"
				variant="primary"
				size="lg"
				fullWidth={true}
				disabled={isLoading}
				className="mt-8"
			>
				{#if isLoading}
					<LoadingSpinner size="sm" class="mr-2" />
					Signing In...
				{:else}
					Sign In
				{/if}
			</Button>
		</form>

		<!-- Footer Links -->
		<div class="mt-8 space-y-4 text-center">
			<!-- Forgot Password -->
			<div>
				<button
					type="button"
					class="text-sm text-blue-600 underline hover:text-blue-500"
					disabled={isLoading}
				>
					Forgot your password?
				</button>
			</div>

			<!-- Switch to Signup -->
			{#if onSwitchToSignup}
				<div class="border-t border-gray-200 pt-4">
					<p class="text-sm text-gray-600">
						Don't have an account?
						<button
							type="button"
							onclick={onSwitchToSignup}
							class="font-medium text-blue-600 underline hover:text-blue-500"
							disabled={isLoading}
						>
							Sign up here
						</button>
					</p>
				</div>
			{/if}
		</div>
	</div>
</div>

<style>
	/* Additional styling for better visual hierarchy */
	.bg-white {
		box-shadow:
			0 10px 25px -5px rgba(0, 0, 0, 0.1),
			0 10px 10px -5px rgba(0, 0, 0, 0.04);
	}
</style>

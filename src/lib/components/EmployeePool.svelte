<script lang="ts">
	/**
	 * Employee Pool Component
	 * Drag-and-drop employee assignment interface
	 * Following Code Complete principles: Clear interaction patterns, accessibility
	 */
	
	import type { Employee } from '$lib/types.js';
	import { formatCurrency } from '$lib/utils.js';
	
	interface Props {
		employees: Employee[];
		onEmployeeAssign: (employeeId: string, shiftId: string) => void;
		onClose: () => void;
	}
	
	let { employees, onEmployeeAssign, onClose }: Props = $props();
	
	// State
	let searchTerm = $state('');
	let filterRole = $state('all');
	let draggedEmployee: Employee | null = $state(null);
	
	// Computed
	let filteredEmployees = $derived(() => {
		// Use both active and isActive for backward compatibility
		let filtered = employees.filter(emp => emp.isActive !== false && emp.active !== false);

		// Filter by role
		if (filterRole !== 'all') {
			filtered = filtered.filter(emp => emp.role === filterRole);
		}

		// Filter by search term
		if (searchTerm.trim()) {
			const term = searchTerm.toLowerCase();
			filtered = filtered.filter(emp =>
				emp.name?.toLowerCase().includes(term) ||
				emp.fullName?.toLowerCase().includes(term) ||
				emp.role.toLowerCase().includes(term)
			);
		}

		return filtered;
	});

	let uniqueRoles = $derived(() => {
		const roles = [...new Set(employees.map(emp => emp.role))];
		return roles.sort();
	});
	
	// Drag and drop handlers
	function handleDragStart(event: DragEvent, employee: Employee) {
		if (!event.dataTransfer) return;

		draggedEmployee = employee;
		event.dataTransfer.effectAllowed = 'move';

		// Set data for employee assignment (not shift movement)
		event.dataTransfer.setData('text/plain', employee.id);
		event.dataTransfer.setData('application/json', JSON.stringify({
			type: 'employee',
			employee: employee
		}));

		console.log('🔄 EmployeePool: Starting drag for employee:', employee.name || employee.fullName);

		// Add visual feedback
		const target = event.target as HTMLElement;
		target.classList.add('opacity-50');
	}

	function handleDragEnd(event: DragEvent) {
		console.log('✅ EmployeePool: Drag ended for employee:', draggedEmployee?.name || draggedEmployee?.fullName);
		draggedEmployee = null;

		// Remove visual feedback
		const target = event.target as HTMLElement;
		target.classList.remove('opacity-50');
	}
	
	// Quick assign function (for click-to-assign)
	function handleEmployeeClick(employee: Employee) {
		// This could open a modal to select which shift to assign to
		console.log('Employee clicked for assignment:', employee.name);
		// For now, we'll just log - in a real implementation, this might show available shifts
	}
</script>

<!-- Employee Pool Sidebar -->
<div class="h-full flex flex-col bg-white">
	<!-- Header -->
	<div class="flex items-center justify-between p-4 border-b border-gray-200">
		<h2 class="text-lg font-semibold text-gray-900">Employee Pool</h2>
		<button
			onclick={onClose}
			class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
			aria-label="Close employee pool"
		>
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
			</svg>
		</button>
	</div>
	
	<!-- Instructions -->
	<div class="p-4 bg-blue-50 border-b border-blue-200">
		<p class="text-sm text-blue-800">
			<svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
			</svg>
			Drag employees to empty shift slots to assign them
		</p>
	</div>
	
	<!-- Filters -->
	<div class="p-4 border-b border-gray-200 space-y-3">
		<!-- Search -->
		<div class="relative">
			<svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
			</svg>
			<input
				type="text"
				bind:value={searchTerm}
				placeholder="Search employees..."
				class="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
			/>
		</div>
		
		<!-- Role Filter -->
		<select
			bind:value={filterRole}
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
		>
			<option value="all">All Roles</option>
			{#each uniqueRoles() as role}
				<option value={role}>{role}</option>
			{/each}
		</select>
	</div>
	
	<!-- Employee List -->
	<div class="flex-1 overflow-y-auto p-4">
		{#if filteredEmployees().length === 0}
			<div class="text-center py-8">
				<svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
				</svg>
				<p class="text-gray-500 text-sm">
					{searchTerm || filterRole !== 'all' ? 'No employees match your filters' : 'No active employees found'}
				</p>
			</div>
		{:else}
			<div class="space-y-2">
				{#each filteredEmployees() as employee}
					<div
						draggable="true"
						ondragstart={(e) => handleDragStart(e, employee)}
						ondragend={handleDragEnd}
						onclick={() => handleEmployeeClick(employee)}
						onkeydown={(e) => e.key === 'Enter' && handleEmployeeClick(employee)}
						class="group p-3 border border-gray-200 rounded-lg cursor-move hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 {draggedEmployee?.id === employee.id ? 'opacity-50' : ''}"
						role="button"
						tabindex="0"
						aria-label="Drag {employee.name || employee.fullName} to assign to a shift"
					>
						<div class="flex items-center space-x-3">
							<!-- Avatar -->
							<div class="w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
								<span class="text-[10px] sm:text-xs md:text-sm font-medium text-blue-700">
									{(employee.name || employee.fullName || '').split(' ').map((n: string) => n[0]).join('')}
								</span>
							</div>

							<!-- Employee Info -->
							<div class="flex-1 min-w-0">
								<div class="text-sm font-medium text-gray-900 truncate">
									{employee.fullName || employee.name}
								</div>
								<div class="flex items-center justify-between">
									<span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-700">
										{employee.role}
									</span>
									<span class="text-xs text-gray-500">
										{formatCurrency(employee.defaultDailyRate)}/day
									</span>
								</div>
							</div>

							<!-- Drag Handle -->
							<div class="text-gray-400 group-hover:text-blue-500 transition-colors">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
								</svg>
							</div>
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</div>
	
	<!-- Footer -->
	<div class="p-4 border-t border-gray-200 bg-gray-50">
		<div class="text-xs text-gray-500 text-center">
			{filteredEmployees().length} of {employees.filter(e => e.isActive !== false && e.active !== false).length} employees shown
		</div>
	</div>
</div>

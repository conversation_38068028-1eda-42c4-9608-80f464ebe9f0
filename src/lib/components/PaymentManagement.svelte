<script lang="ts">
	import { onMount } from 'svelte';
	import type { Employee, EmployeePayment } from '$lib/types.js';
	import { paymentService } from '$lib/services/paymentService.js';
	import { employeeService } from '$lib/services/employeeService.js';
	import { formatCurrency } from '$lib/utils.js';
	import Button from './ui/Button.svelte';

	// Props
	interface Props {
		isOpen?: boolean;
		onClose?: () => void;
	}

	let { isOpen = false, onClose }: Props = $props();

	// State
	let employees: Employee[] = $state([]);
	let unpaidSummary: Array<{
		employeeId: string;
		employeeName: string;
		totalUnpaidHours: number;
		totalUnpaidAmount: number;
		oldestUnpaidDate: Date;
		unpaidShiftsCount: number;
	}> = $state([]);
	let selectedEmployee: Employee | null = $state(null);
	let paymentHistory: EmployeePayment[] = $state([]);
	let isLoading = $state(false);
	let showPaymentForm = $state(false);

	// Payment form state
	let paymentFormData = $state({
		employeeId: '',
		weekStartDate: '',
		amountPaid: 0,
		paymentMethod: 'CASH' as 'CASH' | 'TRANSFER' | 'OTHER',
		notes: ''
	});

	// Load data on mount
	onMount(async () => {
		if (isOpen) {
			await loadData();
		}
	});

	// Watch for isOpen changes
	$effect(() => {
		if (isOpen) {
			loadData();
		}
	});

	async function loadData() {
		isLoading = true;
		try {
			const [employeesData, unpaidData] = await Promise.all([
				employeeService.fetchActiveEmployees(),
				paymentService.getUnpaidShiftsSummary()
			]);

			employees = employeesData;
			unpaidSummary = unpaidData;

			console.log('✅ PaymentManagement: Data loaded:', { employees: employees.length, unpaid: unpaidSummary.length });
		} catch (error) {
			console.error('❌ PaymentManagement: Failed to load data:', error);
		} finally {
			isLoading = false;
		}
	}

	async function selectEmployee(employee: Employee) {
		selectedEmployee = employee;
		try {
			paymentHistory = await paymentService.getEmployeePaymentHistory(employee.id, 20);
			console.log('✅ PaymentManagement: Payment history loaded for:', employee.fullName || employee.name);
		} catch (error) {
			console.error('❌ PaymentManagement: Failed to load payment history:', error);
		}
	}

	function openPaymentForm(employeeId: string) {
		const employee = employees.find(e => e.id === employeeId);
		if (!employee) return;

		// Calculate current week start (Monday)
		const today = new Date();
		const dayOfWeek = today.getDay();
		const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
		const weekStart = new Date(today);
		weekStart.setDate(today.getDate() + mondayOffset);

		paymentFormData = {
			employeeId,
			weekStartDate: weekStart.toISOString().split('T')[0],
			amountPaid: 0,
			paymentMethod: 'CASH',
			notes: ''
		};
		showPaymentForm = true;
	}

	function closePaymentForm() {
		showPaymentForm = false;
		paymentFormData = {
			employeeId: '',
			weekStartDate: '',
			amountPaid: 0,
			paymentMethod: 'CASH',
			notes: ''
		};
	}

	async function handlePaymentSubmit(event: Event) {
		event.preventDefault();
		try {
			// First create/update the payment record
			const payment = await paymentService.createOrUpdateEmployeePayment(
				paymentFormData.employeeId,
				new Date(paymentFormData.weekStartDate),
				paymentFormData.paymentMethod,
				paymentFormData.notes
			);

			// Then mark it as paid
			await paymentService.markPaymentAsPaid(
				payment.id,
				paymentFormData.amountPaid,
				paymentFormData.paymentMethod,
				paymentFormData.notes
			);

			console.log('✅ PaymentManagement: Payment processed successfully');

			// Reload data
			await loadData();
			if (selectedEmployee) {
				await selectEmployee(selectedEmployee);
			}

			closePaymentForm();
		} catch (error) {
			console.error('❌ PaymentManagement: Failed to process payment:', error);
			alert(`Failed to process payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	function formatDate(date: Date): string {
		return date.toLocaleDateString();
	}

	function handleClose() {
		selectedEmployee = null;
		onClose?.();
	}
</script>

{#if isOpen}
	<!-- Modal Backdrop -->
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
		<div class="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
			<!-- Header -->
			<div class="flex items-center justify-between p-6 border-b border-gray-200">
				<h2 class="text-xl font-semibold text-gray-900">Payment Management</h2>
				<button
					onclick={handleClose}
					class="text-gray-400 hover:text-gray-600 transition-colors"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
					</svg>
				</button>
			</div>

			<!-- Content -->
			<div class="flex h-[calc(90vh-120px)]">
				<!-- Left Panel - Unpaid Summary -->
				<div class="w-1/2 border-r border-gray-200 p-6 overflow-y-auto">
					<h3 class="text-lg font-medium text-gray-900 mb-4">Unpaid Shifts Summary</h3>

					{#if isLoading}
						<div class="text-center py-8">
							<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
							<p class="mt-2 text-gray-600">Loading payment data...</p>
						</div>
					{:else if unpaidSummary.length === 0}
						<div class="text-center py-8">
							<p class="text-gray-600">All employees are up to date with payments! 🎉</p>
						</div>
					{:else}
						<div class="space-y-3">
							{#each unpaidSummary as summary (summary.employeeId)}
								<div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
									<div class="flex justify-between items-start mb-2">
										<h4 class="font-medium text-gray-900">{summary.employeeName}</h4>
										<div class="flex space-x-2">
											<Button
												size="sm"
												variant="secondary"
												onclick={() => selectEmployee(employees.find(e => e.id === summary.employeeId)!)}
											>
												View History
											</Button>
											<Button
												size="sm"
												variant="primary"
												onclick={() => openPaymentForm(summary.employeeId)}
											>
												Pay Now
											</Button>
										</div>
									</div>

									<div class="grid grid-cols-2 gap-4 text-sm">
										<div>
											<span class="text-gray-600">Total Owed:</span>
											<span class="font-medium text-red-600">{formatCurrency(summary.totalUnpaidAmount)}</span>
										</div>
										<div>
											<span class="text-gray-600">Hours:</span>
											<span class="font-medium">{summary.totalUnpaidHours}</span>
										</div>
										<div>
											<span class="text-gray-600">Shifts:</span>
											<span class="font-medium">{summary.unpaidShiftsCount}</span>
										</div>
										<div>
											<span class="text-gray-600">Oldest:</span>
											<span class="font-medium">{formatDate(summary.oldestUnpaidDate)}</span>
										</div>
									</div>
								</div>
							{/each}
						</div>
					{/if}
				</div>

				<!-- Right Panel - Employee Details -->
				<div class="w-1/2 p-6 overflow-y-auto">
					{#if selectedEmployee}
						<h3 class="text-lg font-medium text-gray-900 mb-4">
							Payment History - {selectedEmployee.fullName || selectedEmployee.name}
						</h3>

						{#if paymentHistory.length === 0}
							<div class="text-center py-8">
								<p class="text-gray-600">No payment history found for this employee.</p>
							</div>
						{:else}
							<div class="space-y-3">
								{#each paymentHistory as payment (payment.id)}
									<div class="border border-gray-200 rounded-lg p-4">
										<div class="flex justify-between items-start mb-2">
											<div>
												<span class="font-medium">Week of {formatDate(payment.weekStartDate)}</span>
												{#if payment.paidAt}
													<span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
														Paid
													</span>
												{:else}
													<span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
														Unpaid
													</span>
												{/if}
											</div>
											<div class="text-right">
												<div class="font-medium">{formatCurrency(payment.totalDue)}</div>
												{#if payment.paidAt}
													<div class="text-sm text-gray-600">Paid: {formatCurrency(payment.totalPaid)}</div>
												{/if}
											</div>
										</div>

										{#if payment.paymentMethod}
											<div class="text-sm text-gray-600">
												Method: {payment.paymentMethod}
											</div>
										{/if}

										{#if payment.paidAt}
											<div class="text-sm text-gray-600">
												Paid on: {formatDate(payment.paidAt)}
											</div>
										{/if}

										{#if payment.notes}
											<div class="text-sm text-gray-600 mt-2">
												Notes: {payment.notes}
											</div>
										{/if}
									</div>
								{/each}
							</div>
						{/if}
					{:else}
						<div class="text-center py-8">
							<p class="text-gray-600">Select an employee from the left panel to view their payment history.</p>
						</div>
					{/if}
				</div>
			</div>
		</div>
	</div>

	<!-- Payment Form Modal -->
	{#if showPaymentForm}
		<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
			<div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
				<div class="p-6">
					<h3 class="text-lg font-medium text-gray-900 mb-4">Process Payment</h3>

					<form onsubmit={handlePaymentSubmit} class="space-y-4">
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Employee</label>
							<input
								type="text"
								value={employees.find(e => e.id === paymentFormData.employeeId)?.fullName || employees.find(e => e.id === paymentFormData.employeeId)?.name || ''}
								readonly
								class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
							/>
						</div>

						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Week Starting</label>
							<input
								type="date"
								bind:value={paymentFormData.weekStartDate}
								required
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>

						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Amount Paid</label>
							<input
								type="number"
								step="0.01"
								min="0"
								bind:value={paymentFormData.amountPaid}
								required
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>

						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
							<select
								bind:value={paymentFormData.paymentMethod}
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							>
								<option value="CASH">Cash</option>
								<option value="TRANSFER">Bank Transfer</option>
								<option value="OTHER">Other</option>
							</select>
						</div>

						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
							<textarea
								bind:value={paymentFormData.notes}
								rows="3"
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							></textarea>
						</div>

						<div class="flex justify-end space-x-3 pt-4">
							<Button variant="secondary" onclick={closePaymentForm}>Cancel</Button>
							<Button type="submit" variant="primary">Process Payment</Button>
						</div>
					</form>
				</div>
			</div>
		</div>
	{/if}
{/if}

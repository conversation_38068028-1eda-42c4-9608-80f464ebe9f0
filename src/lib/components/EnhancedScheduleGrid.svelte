<script lang="ts">
	/**
	 * Enhanced Schedule Grid Component
	 * Integrates leave management, availability checking, and conflict detection
	 * Following Code Complete principles: Clear separation of concerns, comprehensive integration
	 */

	import { enhancedScheduleService } from '$lib/services/enhancedScheduleService.js';
	import ShiftBlock from './ShiftBlock.svelte';
	import EmployeeAvailabilityCard from './EmployeeAvailabilityCard.svelte';
	import type { 
		EnhancedWeeklySchedule, 
		UIState, 
		Shift, 
		Employee, 
		EmployeeAvailabilityStatus,
		ShiftConflict,
		LeaveRequest,
		EmployeeUnavailability
	} from '$lib/types.js';
	import { formatCurrency, calculateEmployeeWeeklySummary } from '$lib/utils.js';

	interface Props {
		schedule: EnhancedWeeklySchedule;
		weekDates: Date[];
		uiState: UIState;
		organizationId: string;
		onShiftClick: (shift: Shift | null, employee?: Employee | null) => void;
		onShiftCreate: (shift: Shift | null, employee?: Employee | null) => void;
		onLeaveRequestClick: (leaveRequest: LeaveRequest) => void;
		onUnavailabilityClick: (unavailability: EmployeeUnavailability) => void;
		onConflictDetected: (conflicts: ShiftConflict[]) => void;
		onShiftDrop?: (event: { employeeId: string; date: Date; shift: Shift }) => void;

	}

	let {
		schedule,
		weekDates,
		uiState,
		organizationId,
		onShiftClick,
		onShiftCreate,
		onLeaveRequestClick,
		onUnavailabilityClick,
		onConflictDetected,
		onShiftDrop
	}: Props = $props();

	// Removed debug logging to prevent interference with reactive calculations

	// UI state
	let dragOverCell = $state<{ employeeId: string; date: Date } | null>(null);
	let conflictCache = $state<Map<string, ShiftConflict[]>>(new Map());

	/**
	 * Get shifts for a specific employee and date
	 * Following Code Complete: Pure function, clear filtering
	 */
	function getShiftsForEmployeeAndDate(employeeId: string, date: Date): Shift[] {
		return schedule.shifts.filter(
			(shift) =>
				shift.employeeId === employeeId && 
				shift.date.toDateString() === date.toDateString()
		);
	}

	/**
	 * Get employee availability for a specific date
	 * Following Code Complete: Safe data access with fallbacks
	 */
	function getEmployeeAvailability(employeeId: string, date: Date): EmployeeAvailabilityStatus | null {
		const employeeAvailability = schedule.employeeAvailability.get(employeeId);
		if (!employeeAvailability) return null;

		return employeeAvailability.find(
			availability => availability.date.toDateString() === date.toDateString()
		) || null;
	}

	/**
	 * Check if employee has leave on specific date
	 * Following Code Complete: Clear boolean check
	 */
	function hasLeaveOnDate(employeeId: string, date: Date): boolean {
		return schedule.leaveRequests.some(lr =>
			lr.employeeId === employeeId &&
			lr.status === 'approved' &&
			date >= lr.startDate &&
			date <= lr.endDate
		);
	}

	/**
	 * Get leave requests for employee on date
	 * Following Code Complete: Clear filtering with type safety
	 */
	function getLeaveRequestsForDate(employeeId: string, date: Date): LeaveRequest[] {
		return schedule.leaveRequests.filter(lr =>
			lr.employeeId === employeeId &&
			date >= lr.startDate &&
			date <= lr.endDate
		);
	}

	/**
	 * Handle drag over cell
	 * Following Code Complete: Clear event handling
	 */
	function handleDragOver(event: DragEvent) {
		event.preventDefault();
		event.dataTransfer!.dropEffect = 'move';
	}

	/**
	 * Handle drag enter cell
	 * Following Code Complete: Clear state management
	 */
	function handleDragEnter(event: DragEvent, employeeId: string, date: Date) {
		event.preventDefault();
		dragOverCell = { employeeId, date };
	}

	/**
	 * Handle drag leave cell
	 * Following Code Complete: Clear state cleanup
	 */
	function handleDragLeave(event: DragEvent) {
		event.preventDefault();
		dragOverCell = null;
	}

	/**
	 * Handle shift drop with availability validation
	 * Following Code Complete: Comprehensive validation with user feedback
	 */
	async function handleShiftDrop(event: DragEvent, employeeId: string, date: Date) {
		event.preventDefault();
		dragOverCell = null;

		if (!uiState.dragState.draggedShift) return;

		const shift = uiState.dragState.draggedShift;

		try {
			// Validate availability before allowing drop
			const conflicts = await enhancedScheduleService.validateShiftAssignment(
				employeeId,
				{ ...shift, date }, // Update shift with new date
				organizationId
			);

			if (conflicts.length > 0) {
				// Store conflicts and notify parent
				const conflictKey = `${employeeId}-${date.toDateString()}`;
				conflictCache.set(conflictKey, conflicts);
				onConflictDetected(conflicts);
				
				// Still allow the drop but with warning
				console.warn('⚠️ EnhancedScheduleGrid: Shift assignment has conflicts:', conflicts);
			}

			// Call the drop event handler
			onShiftDrop?.({ employeeId, date, shift });

		} catch (error) {
			console.error('❌ EnhancedScheduleGrid: Failed to validate shift drop:', error);
			// TODO: Show error message to user
		}
	}



	/**
	 * Handle create shift with availability check
	 * Following Code Complete: Validation before creation
	 */
	async function handleCreateShift(employee: Employee, date: Date) {
		// Check if employee is available
		const availability = getEmployeeAvailability(employee.id, date);
		
		if (availability && !availability.isAvailable) {
			const hasApprovedLeave = availability.leaveRequests.some(lr => lr.status === 'approved');
			
			if (hasApprovedLeave) {
				if (!confirm(`${employee.name} has approved leave on this date. Create shift anyway?`)) {
					return;
				}
			} else {
				if (!confirm(`${employee.name} has unavailability on this date. Create shift anyway?`)) {
					return;
				}
			}
		}

		onShiftCreate(null, employee);
	}

	/**
	 * Get cell background color based on availability
	 * Following Code Complete: Clear visual feedback
	 */
	function getCellBackgroundColor(employeeId: string, date: Date): string {
		const availability = getEmployeeAvailability(employeeId, date);
		
		if (!availability) return 'bg-white';
		
		if (!availability.isAvailable) {
			const hasApprovedLeave = availability.leaveRequests.some(lr => lr.status === 'approved');
			if (hasApprovedLeave) {
				return 'bg-red-50'; // Approved leave
			}
			return 'bg-yellow-50'; // Other unavailability
		}
		
		return 'bg-white';
	}

	/**
	 * Get cell border color for drag state
	 * Following Code Complete: Clear visual feedback
	 */
	function getCellBorderColor(employeeId: string, date: Date): string {
		const isDropTarget = dragOverCell?.employeeId === employeeId && 
						   dragOverCell?.date.toDateString() === date.toDateString();
		
		if (isDropTarget) {
			const availability = getEmployeeAvailability(employeeId, date);
			if (availability && !availability.isAvailable) {
				return 'border-red-300'; // Warning border for unavailable
			}
			return 'border-blue-300'; // Normal drop target
		}
		
		return 'border-gray-200';
	}

	/**
	 * Get unassigned shifts for a specific date
	 * Following Code Complete: Clear filtering
	 */
	function getUnassignedShiftsForDate(date: Date): Shift[] {
		return schedule.shifts.filter(
			(shift) => !shift.employeeId && shift.date.toDateString() === date.toDateString()
		);
	}

	/**
	 * Handle creating a new unassigned shift
	 * Following Code Complete: Clear function purpose
	 */
	function handleCreateUnassignedShift(date: Date) {
		console.log('🔄 EnhancedScheduleGrid: handleCreateUnassignedShift called for date:', date.toDateString());

		// Create a mock shift object with null employeeId for unassigned shift
		const unassignedShift = {
			id: '', // Will be generated
			employeeId: null, // Unassigned
			venueId: '', // Will be set in modal
			date: date,
			startTime: '09:00',
			endTime: '17:00',
			totalHours: 8.0,
			dailyRate: 100, // Default rate
			isPaid: false,
			advanceDeduction: 0,
			notes: ''
		};

		onShiftCreate(unassignedShift as any, null);
	}

	/**
	 * Handle unassigned shift drag creation start
	 * Following Code Complete: Clear function purpose and error handling
	 */
	function handleUnassignedCellDragStart(event: MouseEvent, date: Date, existingShift?: Shift) {
		event.preventDefault();
		event.stopPropagation();

		console.log('🔄 EnhancedScheduleGrid: Starting unassigned cell drag creation for date:', date.toDateString());

		// Initialize drag create state for unassigned shifts
		uiState.dragCreateState = {
			isActive: true,
			mode: null, // Will be determined by drag direction
			direction: null,
			sourceEmployee: null, // No source employee for unassigned shifts
			sourceDate: date,
			sourceShift: existingShift || null,
			targetDates: [],
			targetEmployees: [],
			previewCells: []
		};

		// Add global mouse event listeners
		document.addEventListener('mousemove', handleUnassignedDragCreateMove);
		document.addEventListener('mouseup', handleUnassignedDragCreateEnd);
		document.body.classList.add('drag-create-active');
	}

	/**
	 * Handle unassigned drag creation mouse move
	 * Following Code Complete: Clear logic flow and error handling
	 */
	function handleUnassignedDragCreateMove(event: MouseEvent) {
		if (!uiState.dragCreateState.isActive) return;

		// Find the cell under the mouse
		const element = document.elementFromPoint(event.clientX, event.clientY);
		const cell = element?.closest('[data-schedule-cell]');

		if (cell) {
			const employeeId = cell.getAttribute('data-employee-id');
			const dateStr = cell.getAttribute('data-date');
			const isUnassignedRow = cell.getAttribute('data-unassigned-row') === 'true';

			if (dateStr) {
				const targetDate = new Date(dateStr);
				const sourceDate = uiState.dragCreateState.sourceDate;

				if (!sourceDate) return;

				if (isUnassignedRow) {
					// Horizontal drag across unassigned row (multiple days, same time slot)
					const isSameDate = targetDate.toDateString() === sourceDate.toDateString();

					if (!isSameDate) {
						uiState.dragCreateState.direction = 'horizontal';
						uiState.dragCreateState.mode = 'unassigned-horizontal';

						const startDate = new Date(Math.min(sourceDate.getTime(), targetDate.getTime()));
						const endDate = new Date(Math.max(sourceDate.getTime(), targetDate.getTime()));

						const targetDates: Date[] = [];
						const previewCells: { employeeId: string | null; date: Date }[] = [];

						for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
							const currentDate = new Date(d);
							targetDates.push(currentDate);
							previewCells.push({ employeeId: null, date: currentDate });
						}

						uiState.dragCreateState.targetDates = targetDates;
						uiState.dragCreateState.previewCells = previewCells;
					}
				} else if (employeeId) {
					// Vertical drag from unassigned row to employee rows (multiple unassigned shifts, same date)
					const isSameDate = targetDate.toDateString() === sourceDate.toDateString();

					if (isSameDate) {
						uiState.dragCreateState.direction = 'vertical';
						uiState.dragCreateState.mode = 'unassigned-vertical';

						// Find all employees between source (unassigned row) and target employee
						const allEmployees = schedule.employees.filter(emp => emp.isActive);
						const targetIndex = allEmployees.findIndex(emp => emp.id === employeeId);

						if (targetIndex !== -1) {
							// Create multiple unassigned shifts (one for each employee row we're dragging over)
							// But all shifts remain unassigned (employeeId: null)
							const numberOfShifts = targetIndex + 1; // Number of employee rows we're covering
							const previewCells: { employeeId: string | null; date: Date }[] = [];

							// Create preview cells for multiple unassigned shifts on the same date
							for (let i = 0; i < numberOfShifts; i++) {
								previewCells.push({ employeeId: null, date: sourceDate });
							}

							uiState.dragCreateState.targetDates = [sourceDate]; // Same date, multiple shifts
							uiState.dragCreateState.previewCells = previewCells;
						}
					}
				}
			}
		}
	}

	/**
	 * Handle unassigned drag creation end
	 * Following Code Complete: Clear cleanup and state management
	 */
	function handleUnassignedDragCreateEnd(_event: MouseEvent) {
		if (!uiState.dragCreateState.isActive) return;

		console.log('🔄 EnhancedScheduleGrid: Ending unassigned drag creation');

		// Clean up event listeners
		document.removeEventListener('mousemove', handleUnassignedDragCreateMove);
		document.removeEventListener('mouseup', handleUnassignedDragCreateEnd);
		document.body.classList.remove('drag-create-active');

		// Trigger creation if we have valid targets
		const hasTargets = uiState.dragCreateState.targetDates.length > 0 || uiState.dragCreateState.previewCells.length > 0;

		if (hasTargets) {
			console.log('🔄 EnhancedScheduleGrid: Calling onShiftCreate for unassigned drag completion with targets:', hasTargets);
			// For unassigned shifts, we need to trigger the creation through the parent component
			// Create a mock unassigned shift to trigger the modal
			const mockUnassignedShift = {
				id: '',
				employeeId: null,
				venueId: '',
				date: uiState.dragCreateState.sourceDate || new Date(),
				startTime: '09:00',
				endTime: '17:00',
				totalHours: 8,
				dailyRate: 100,
				isPaid: false,
				advanceDeduction: 0,
				notes: `Drag create: ${uiState.dragCreateState.mode} - ${uiState.dragCreateState.previewCells.length} shifts`
			};

			// Pass the drag state information through the shift creation
			onShiftCreate(mockUnassignedShift as any, null);
		} else {
			console.log('🔄 EnhancedScheduleGrid: No valid targets, resetting drag state');
			// Reset state if no valid drag occurred
			uiState.dragCreateState = {
				isActive: false,
				mode: null,
				direction: null,
				sourceEmployee: null,
				sourceDate: null,
				sourceShift: null,
				targetDates: [],
				targetEmployees: [],
				previewCells: []
			};
		}
	}
</script>

<div class="bg-white schedule-grid-container">
	<!-- Schedule Grid with frozen header -->
	<div class="overflow-x-auto max-h-[calc(100vh-200px)] overflow-y-auto">
		<table class="w-full border-collapse">
			<!-- Frozen Header Row -->
			<thead class="sticky top-0 z-20 bg-gray-50 schedule-grid-frozen-header">
				<tr class="border-b border-gray-200 bg-gray-50">
					<th class="sticky left-0 min-w-[200px] border-r border-gray-200 bg-gray-50 px-4 py-3 text-left text-sm font-semibold text-gray-900 z-30">
						Employee
					</th>
					{#each weekDates as date}
						<th class="min-w-[140px] border-r border-gray-200 px-3 py-3 text-center text-sm font-semibold text-gray-900 bg-gray-50 z-20">
							<div class="flex flex-col">
								<span class="text-xs tracking-wide text-gray-500 uppercase">
									{date.toLocaleDateString('en-US', { weekday: 'short' })}
								</span>
								<span class="text-sm font-medium">
									{date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
								</span>
							</div>
						</th>
					{/each}
					<th class="min-w-[120px] border-r border-gray-200 px-3 py-3 text-center text-sm font-semibold text-gray-900 bg-gray-50 z-20">
						Weekly Total
					</th>
				</tr>
			</thead>

			<!-- Employee Rows -->
			<tbody class="divide-y divide-gray-200">
				{#each schedule.employees.filter((emp) => emp.isActive) as employee}
					{@const weeklySummary = calculateEmployeeWeeklySummary(employee, schedule.shifts)}
					
					<tr class="transition-colors hover:bg-gray-50">
						<!-- Employee Info -->
						<td class="sticky left-0 border-r border-gray-200 bg-white px-4 py-1 z-10">
							<div class="flex items-center space-x-3">
								<div class="flex-shrink-0">
									<div class="flex h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 lg:h-9 lg:w-9 items-center justify-center rounded-full bg-blue-100">
										<span class="text-[8px] sm:text-[9px] md:text-[10px] lg:text-xs font-medium text-blue-700">
											{(employee.name || 'Unknown').split(' ').map((n) => n[0]).join('')}
										</span>
									</div>
								</div>
								<div class="min-w-0 flex-1">
									<p class="truncate text-sm font-medium text-gray-900">
										{employee.name}
									</p>
									<p class="truncate text-xs text-gray-500">
										{employee.role} • {formatCurrency(employee.defaultDailyRate)}/day
									</p>
								</div>
							</div>
						</td>

						<!-- Daily Shift Cells -->
						{#each weekDates as date}
							{@const shifts = getShiftsForEmployeeAndDate(employee.id, date)}
							{@const availability = getEmployeeAvailability(employee.id, date)}
							{@const cellBg = getCellBackgroundColor(employee.id, date)}
							{@const cellBorder = getCellBorderColor(employee.id, date)}

							<td
								class="relative border-r align-top {cellBg} {cellBorder}
									{uiState.dragState.isDragging ? 'border-dashed' : ''}
									{uiState.dragCreateState.previewCells.some(cell =>
										cell.employeeId === employee.id &&
										cell.date.toDateString() === date.toDateString()) ?
										(uiState.dragCreateState.direction === 'horizontal' ? 'drag-create-horizontal-target' : 'drag-create-vertical-target') : ''}"
								style="height: 40px;"
								data-schedule-cell="true"
								data-employee-id={employee.id}
								data-date={date.toISOString()}
								ondrop={(e) => handleShiftDrop(e, employee.id, date)}
								ondragover={handleDragOver}
								ondragenter={(e) => handleDragEnter(e, employee.id, date)}
								ondragleave={handleDragLeave}
							>
								<div class="group {shifts.length > 1 ? 'space-y-0.5' : 'h-[40px] flex flex-col'}" style="{shifts.length === 1 ? 'height: 40px;' : ''}">
									<!-- Availability Overlay -->
									{#if uiState.showUnavailabilityOverlay && availability}
										<div class="absolute inset-0 pointer-events-none">
											<EmployeeAvailabilityCard
												{employee}
												availabilityStatus={availability}
												{onLeaveRequestClick}
												{onUnavailabilityClick}
												showDetails={false}
											/>
										</div>
									{/if}

									<!-- Shifts -->
									{#if shifts.length > 0}
										{#each shifts as shift}
											<ShiftBlock
												{shift}
												venue={schedule.venues.find((v) => v.id === shift.venueId)}
												{uiState}
												onClick={() => onShiftClick(shift)}
											/>
										{/each}
									{:else}
										<!-- Empty cell - click to add shift (hidden by default, shown on hover) -->
										<button
											onclick={() => handleCreateShift(employee, date)}
											class="flex h-full w-full items-center justify-center rounded-md border-2 border-dashed border-gray-300 opacity-0 group-hover:opacity-100 text-gray-400 transition-all duration-200 hover:border-gray-400 hover:text-gray-500"
											aria-label="Create shift for {employee.name} on {date.toLocaleDateString()}"
										>
											<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
											</svg>
										</button>
									{/if}

									<!-- Leave Request Indicators -->
									{#if hasLeaveOnDate(employee.id, date)}
										{@const leaveRequests = getLeaveRequestsForDate(employee.id, date)}
										<div class="absolute top-1 right-1 space-y-1">
											{#each leaveRequests as leaveRequest}
												<button
													onclick={() => onLeaveRequestClick(leaveRequest)}
													class="inline-flex items-center rounded-full px-1.5 py-0.5 text-xs font-medium
														{leaveRequest.requestType === 'vacation' ? 'bg-yellow-100 text-yellow-800' :
														 leaveRequest.requestType === 'sick' ? 'bg-red-100 text-red-800' :
														 leaveRequest.requestType === 'personal' ? 'bg-blue-100 text-blue-800' :
														 leaveRequest.requestType === 'emergency' ? 'bg-purple-100 text-purple-800' :
														 'bg-gray-100 text-gray-800'}"
													title="{leaveRequest.requestType} leave ({leaveRequest.status})"
												>
													{leaveRequest.requestType.charAt(0).toUpperCase()}
													{#if leaveRequest.status === 'pending'}
														<svg class="ml-0.5 h-2 w-2 text-yellow-600" fill="currentColor" viewBox="0 0 8 8">
															<circle cx="4" cy="4" r="3" />
														</svg>
													{:else if leaveRequest.status === 'approved'}
														<svg class="ml-0.5 h-2 w-2 text-green-600" fill="currentColor" viewBox="0 0 8 8">
															<circle cx="4" cy="4" r="3" />
														</svg>
													{/if}
												</button>
											{/each}
										</div>
									{/if}

									<!-- Conflict Indicators -->
									{#if conflictCache.has(`${employee.id}-${date.toDateString()}`)}
										{@const conflictKey = `${employee.id}-${date.toDateString()}`}
										{@const conflicts = conflictCache.get(conflictKey) || []}
										<div class="absolute bottom-1 left-1">
											<div class="flex items-center space-x-1">
												{#each conflicts as conflict}
													<div
														class="inline-flex items-center rounded-full px-1 py-0.5 text-xs
															{conflict.severity === 'error' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}"
														title={conflict.conflictDetails}
													>
														{#if conflict.severity === 'error'}
															<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
																<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
															</svg>
														{:else}
															<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
																<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
															</svg>
														{/if}
													</div>
												{/each}
											</div>
										</div>
									{/if}
								</div>
							</td>
						{/each}

						<!-- Weekly Summary -->
						<td class="border-r border-gray-200 bg-gray-50 text-center">
							<div class="text-sm">
								<div class="text-[13.5px] font-semibold text-gray-900">
									{weeklySummary.totalHours}h
								</div>
								<div class="text-[13.5px] text-gray-500">
									{formatCurrency(weeklySummary.totalPay)}
								</div>
								{#if weeklySummary.unpaidAmount > 0}
									<div class="text-[11.5px] text-red-600">
										{formatCurrency(weeklySummary.unpaidAmount)} unpaid
									</div>
								{/if}
							</div>
						</td>
					</tr>
				{/each}

				<!-- Unassigned Shifts Row -->
				{#if schedule.shifts.some((s) => !s.employeeId)}
					<tr class="bg-red-50">
						<td class="sticky left-0 border-r border-gray-200 bg-red-50 px-4 py-1 z-10">
							<div class="flex items-center space-x-2">
								<svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
								</svg>
								<div>
									<p class="text-sm font-medium text-red-900">Unassigned Shifts</p>
									<p class="text-xs text-red-600">Needs attention</p>
								</div>
							</div>
						</td>

						{#each weekDates as date}
							{@const unassignedShifts = getUnassignedShiftsForDate(date)}

							<td class="relative border-r border-gray-200 bg-red-50 align-top overflow-hidden
								{unassignedShifts.length === 0 ? 'cursor-crosshair' : ''}
								{unassignedShifts.length === 0 ? 'hover:bg-red-100' : ''}
								{uiState.dragCreateState.previewCells.some(cell =>
									cell.employeeId === null &&
									cell.date.toDateString() === date.toDateString()) ?
									(uiState.dragCreateState.direction === 'horizontal' ? 'drag-create-horizontal-target' : 'drag-create-vertical-target') : ''}"
								style="height: {unassignedShifts.length === 0 ? '40px' : 'auto'}; min-height: 40px;"
								data-schedule-cell="true"
								data-unassigned-row="true"
								data-date={date.toISOString()}
							>
								<div class="group relative {unassignedShifts.length > 0 ? 'space-y-0.5' : 'h-[40px] flex flex-col'}" style="{unassignedShifts.length === 0 ? 'height: 40px;' : 'min-height: 40px;'}">
									<!-- Existing unassigned shifts -->
									{#each unassignedShifts as shift}
										<ShiftBlock
											{shift}
											venue={schedule.venues.find((v) => v.id === shift.venueId)}
											{uiState}
											onClick={() => onShiftClick(shift)}
											isUnassigned={true}
										/>
									{/each}

									<!-- Always show "+" button for creating additional unassigned shifts -->
									{#if unassignedShifts.length === 0}
										<!-- Empty cell - full height "+" button with drag support -->
										<button
											onclick={(e) => {
												console.log('🔄 EnhancedScheduleGrid: + button clicked for unassigned shift on date:', date.toDateString());
												e.preventDefault();
												e.stopPropagation();
												handleCreateUnassignedShift(date);
											}}
											onmousedown={(e) => {
												// Check if this is a potential drag operation
												if (e.button === 0) { // Left mouse button
													handleUnassignedCellDragStart(e, date);
												}
											}}
											class="flex h-full min-h-[40px] w-full items-center justify-center rounded
												border border-dashed border-red-300 opacity-0 group-hover:opacity-100
												text-red-400 transition-all duration-200 hover:border-red-400 hover:bg-red-100 hover:text-red-600
												{uiState.dragCreateState.isActive ? 'drag-create-cursor' : ''}"
											aria-label="Create unassigned shift on {date.toLocaleDateString()} or drag to create multiple"
											title="Click to create shift, or drag horizontally for multiple days or vertically for multiple unassigned shifts"
										>
											<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M12 6v6m0 0v6m0-6h6m-6 0H6"
												/>
											</svg>
										</button>
									{:else}
										<!-- Compact "+" button when shifts exist with drag support -->
										<button
											onclick={(e) => {
												console.log('🔄 EnhancedScheduleGrid: + button clicked for additional unassigned shift on date:', date.toDateString());
												e.preventDefault();
												e.stopPropagation();
												handleCreateUnassignedShift(date);
											}}
											onmousedown={(e) => {
												// Check if this is a potential drag operation
												if (e.button === 0) { // Left mouse button
													handleUnassignedCellDragStart(e, date);
												}
											}}
											class="flex h-[20px] w-full items-center justify-center rounded mt-0.5
												border border-dashed border-red-300 opacity-0 group-hover:opacity-100
												text-red-400 transition-all duration-200 hover:border-red-400 hover:bg-red-100 hover:text-red-600
												{uiState.dragCreateState.isActive ? 'drag-create-cursor' : ''}"
											aria-label="Add another unassigned shift on {date.toLocaleDateString()} or drag to create multiple"
											title="Click to create shift, or drag horizontally for multiple days or vertically for multiple unassigned shifts"
										>
											<svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M12 6v6m0 0v6m0-6h6m-6 0H6"
												/>
											</svg>
										</button>
									{/if}
								</div>
							</td>
						{/each}

						<td class="bg-red-50 text-center">
							<div class="text-[13.5px] font-semibold text-red-900">
								{schedule.shifts.filter((s) => !s.employeeId).length} shifts
							</div>
						</td>
					</tr>
				{/if}
			</tbody>
		</table>
	</div>

	<!-- Mobile View (simplified) -->
	{#if uiState.isMobileView}
		<div class="p-4 md:hidden">
			<div class="py-8 text-center text-gray-500">
				<svg class="mx-auto mb-4 h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
				</svg>
				<p class="mb-2 text-lg font-medium text-gray-900">Mobile View</p>
				<p class="text-sm text-gray-500">
					Full scheduling features are available on desktop. Use the employee pool to manage assignments.
				</p>
			</div>
		</div>
	{/if}
</div>

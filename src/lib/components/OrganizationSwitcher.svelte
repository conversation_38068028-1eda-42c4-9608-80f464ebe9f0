<script lang="ts">
	import { onMount } from 'svelte';
	import type { Organization } from '$lib/types.js';
	import { organizationService } from '$lib/services/organizationService.js';
	import { authState } from '$lib/stores/auth.js';

	// State
	let organizations: Array<Organization & { userRole: string; membershipActive: boolean }> = $state(
		[]
	);
	let currentOrganization: Organization | null = $state(null);
	let isDropdownOpen = $state(false);
	let isLoading = $state(false);

	// Load organizations on mount
	onMount(async () => {
		await loadOrganizations();
	});

	// Watch for auth state changes
	$effect(() => {
		const auth = $authState;
		if (auth.currentRestaurant) {
			currentOrganization = {
				id: auth.currentRestaurant.id,
				name: auth.currentRestaurant.name,
				ownerUserId: auth.currentRestaurant.owner_id,
				plan: 'free',
				logoUrl: undefined,
				isActive: true,
				createdAt: new Date(auth.currentRestaurant.created_at),
				updatedAt: auth.currentRestaurant.updated_at
					? new Date(auth.currentRestaurant.updated_at)
					: new Date()
			};
		}
	});

	async function loadOrganizations() {
		isLoading = true;
		try {
			organizations = await organizationService.getUserOrganizations();

			// Set current organization if not already set
			if (!currentOrganization && organizations.length > 0) {
				currentOrganization = organizations[0];
			}
		} catch (error) {
			console.error('❌ OrganizationSwitcher: Failed to load organizations:', error);
		} finally {
			isLoading = false;
		}
	}

	async function switchOrganization(
		organization: Organization & { userRole: string; membershipActive: boolean }
	) {
		try {
			console.log('🔄 OrganizationSwitcher: Switching to organization:', organization.name);

			// Switch context in the backend
			await organizationService.switchOrganizationContext(organization.id);

			// Update current organization
			currentOrganization = organization;

			// Close dropdown
			isDropdownOpen = false;

			console.log('✅ OrganizationSwitcher: Organization switched successfully');

			// Reload the page to refresh all data with new organization context
			window.location.reload();
		} catch (error) {
			console.error('❌ OrganizationSwitcher: Failed to switch organization:', error);
			alert(
				`Failed to switch organization: ${error instanceof Error ? error.message : 'Unknown error'}`
			);
		}
	}

	function toggleDropdown() {
		isDropdownOpen = !isDropdownOpen;
	}

	function closeDropdown() {
		isDropdownOpen = false;
	}

	// Close dropdown when clicking outside
	function handleClickOutside(event: MouseEvent) {
		const target = event.target as Element;
		if (!target.closest('.organization-switcher')) {
			closeDropdown();
		}
	}

	onMount(() => {
		document.addEventListener('click', handleClickOutside);
		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});

	function getRoleColor(role: string): string {
		switch (role) {
			case 'superadmin':
				return 'bg-purple-100 text-purple-800';
			case 'admin':
				return 'bg-blue-100 text-blue-800';
			case 'editor':
				return 'bg-green-100 text-green-800';
			case 'viewer':
				return 'bg-gray-100 text-gray-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	}

	function getRoleLabel(role: string): string {
		switch (role) {
			case 'superadmin':
				return 'Super Admin';
			case 'admin':
				return 'Admin';
			case 'editor':
				return 'Editor';
			case 'viewer':
				return 'Viewer';
			default:
				return role;
		}
	}
</script>

<div class="organization-switcher relative">
	<!-- Current Organization Button -->
	<button
		onclick={toggleDropdown}
		class="flex items-center space-x-2 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
		class:ring-2={isDropdownOpen}
		class:ring-blue-500={isDropdownOpen}
		class:border-blue-500={isDropdownOpen}
	>
		<!-- Organization Icon -->
		<div class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100">
			{#if currentOrganization?.logoUrl}
				<img
					src={currentOrganization.logoUrl}
					alt={currentOrganization.name}
					class="h-6 w-6 rounded-full object-cover"
				/>
			{:else}
				<span class="text-xs font-bold text-blue-600">
					{currentOrganization?.name?.charAt(0)?.toUpperCase() || 'O'}
				</span>
			{/if}
		</div>

		<!-- Organization Name -->
		<span class="max-w-32 truncate">
			{currentOrganization?.name || 'Select Organization'}
		</span>

		<!-- Dropdown Arrow -->
		<svg
			class="h-4 w-4 text-gray-400 transition-transform"
			class:rotate-180={isDropdownOpen}
			fill="none"
			stroke="currentColor"
			viewBox="0 0 24 24"
		>
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
		</svg>
	</button>

	<!-- Dropdown Menu -->
	{#if isDropdownOpen}
		<div
			class="absolute top-full left-0 z-50 mt-1 w-80 rounded-md border border-gray-200 bg-white shadow-lg"
		>
			<div class="py-1">
				<!-- Header -->
				<div class="border-b border-gray-200 px-4 py-2">
					<h3 class="text-sm font-medium text-gray-900">Switch Organization</h3>
				</div>

				<!-- Loading State -->
				{#if isLoading}
					<div class="px-4 py-3 text-center">
						<div class="mx-auto h-4 w-4 animate-spin rounded-full border-b-2 border-blue-600"></div>
						<p class="mt-1 text-xs text-gray-600">Loading organizations...</p>
					</div>
				{:else if organizations.length === 0}
					<div class="px-4 py-3 text-center">
						<p class="text-sm text-gray-600">No organizations found</p>
					</div>
				{:else}
					<!-- Organization List -->
					<div class="max-h-64 overflow-y-auto">
						{#each organizations as org (org.id)}
							<button
								onclick={() => switchOrganization(org)}
								class="w-full px-4 py-3 text-left transition-colors hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
								class:bg-blue-50={currentOrganization?.id === org.id}
								class:border-l-4={currentOrganization?.id === org.id}
								class:border-blue-500={currentOrganization?.id === org.id}
							>
								<div class="flex items-center justify-between">
									<div class="flex items-center space-x-3">
										<!-- Organization Icon -->
										<div
											class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-blue-100"
										>
											{#if org.logoUrl}
												<img
													src={org.logoUrl}
													alt={org.name}
													class="h-8 w-8 rounded-full object-cover"
												/>
											{:else}
												<span class="text-sm font-bold text-blue-600">
													{org.name.charAt(0).toUpperCase()}
												</span>
											{/if}
										</div>

										<!-- Organization Info -->
										<div class="min-w-0 flex-1">
											<p class="truncate text-sm font-medium text-gray-900">
												{org.name}
											</p>
											<div class="mt-1 flex items-center space-x-2">
												<span
													class="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium {getRoleColor(
														org.userRole
													)}"
												>
													{getRoleLabel(org.userRole)}
												</span>
												<span class="text-xs text-gray-500">
													{org.plan}
												</span>
											</div>
										</div>
									</div>

									<!-- Current Indicator -->
									{#if currentOrganization?.id === org.id}
										<div class="flex-shrink-0">
											<svg class="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
												<path
													fill-rule="evenodd"
													d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
													clip-rule="evenodd"
												/>
											</svg>
										</div>
									{/if}
								</div>
							</button>
						{/each}
					</div>

					<!-- Footer -->
					<div class="border-t border-gray-200 px-4 py-2">
						<button
							onclick={() => {
								closeDropdown();
								// TODO: Open organization management modal
								console.log('Open organization management');
							}}
							class="w-full text-left text-sm text-blue-600 transition-colors hover:text-blue-800"
						>
							+ Create New Organization
						</button>
					</div>
				{/if}
			</div>
		</div>
	{/if}
</div>

<style>
	.organization-switcher {
		position: relative;
	}
</style>

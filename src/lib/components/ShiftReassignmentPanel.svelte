<script lang="ts">
	/**
	 * Shift Reassignment Panel Component
	 * Handles shift coverage when employees are unavailable
	 * Following Code Complete principles: Clear workflow, intelligent suggestions
	 */

	import { createEventDispatcher } from 'svelte';
	import { enhancedScheduleService } from '$lib/services/enhancedScheduleService.js';
	import { shiftService } from '$lib/services/shiftService.js';
	import type {
		ReassignmentPanelState,
		EmployeeReplacement,
		Shift,
		LeaveRequest,
		Employee
	} from '$lib/types.js';

	interface Props {
		panelState: ReassignmentPanelState;
		organizationId: string;
		onClose: () => void;
	}

	let { panelState, organizationId, onClose }: Props = $props();

	const dispatch = createEventDispatcher<{
		reassign: { shiftId: string; newEmployeeId: string };
		unassign: { shiftId: string };
		close: void;
	}>();

	// UI state
	let isLoading = $state(false);
	let selectedReplacements = $state<Map<string, string>>(new Map()); // shiftId -> employeeId
	let expandedShift = $state<string | null>(null);

	// Computed
	let hasSelections = $derived(selectedReplacements.size > 0);
	let affectedEmployeeName = $derived(
		panelState.leaveRequest ? getEmployeeName(panelState.leaveRequest.employeeId) : 'Unknown'
	);

	/**
	 * Get employee name by ID
	 * Following Code Complete: Helper function with fallback
	 */
	function getEmployeeName(employeeId: string): string {
		const replacement = panelState.suggestedReplacements.find((r) => r.employee.id === employeeId);
		return replacement?.employee.name || 'Unknown Employee';
	}

	/**
	 * Get venue name for shift
	 * Following Code Complete: Helper function with fallback
	 */
	function getVenueName(shift: Shift): string {
		// This would need to be passed in or fetched
		return 'Venue'; // Placeholder
	}

	/**
	 * Handle replacement selection
	 * Following Code Complete: Clear state management
	 */
	function selectReplacement(shiftId: string, employeeId: string) {
		selectedReplacements.set(shiftId, employeeId);
		selectedReplacements = new Map(selectedReplacements); // Trigger reactivity
	}

	/**
	 * Handle replacement removal
	 * Following Code Complete: Clear state management
	 */
	function removeReplacement(shiftId: string) {
		selectedReplacements.delete(shiftId);
		selectedReplacements = new Map(selectedReplacements); // Trigger reactivity
	}

	/**
	 * Apply all reassignments
	 * Following Code Complete: Batch operation with error handling
	 */
	async function applyReassignments() {
		if (!hasSelections) return;

		isLoading = true;

		try {
			// Apply each reassignment
			for (const [shiftId, newEmployeeId] of selectedReplacements) {
				await shiftService.updateShiftPartial(shiftId, { employeeId: newEmployeeId });
				dispatch('reassign', { shiftId, newEmployeeId });
			}

			// Close panel after successful reassignments
			dispatch('close');
			onClose();
		} catch (error) {
			console.error('❌ ShiftReassignmentPanel: Failed to apply reassignments:', error);
			// TODO: Show error message to user
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Unassign shift (leave it unassigned)
	 * Following Code Complete: Clear action with confirmation
	 */
	async function unassignShift(shift: Shift) {
		if (
			!confirm(`Are you sure you want to unassign the ${shift.startTime}-${shift.endTime} shift?`)
		) {
			return;
		}

		isLoading = true;

		try {
			await shiftService.updateShiftPartial(shift.id, { employeeId: null });
			dispatch('unassign', { shiftId: shift.id });
		} catch (error) {
			console.error('❌ ShiftReassignmentPanel: Failed to unassign shift:', error);
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Toggle shift details expansion
	 * Following Code Complete: Simple state toggle
	 */
	function toggleShiftDetails(shiftId: string) {
		expandedShift = expandedShift === shiftId ? null : shiftId;
	}

	/**
	 * Get replacement score color
	 * Following Code Complete: Clear color mapping
	 */
	function getScoreColor(score: number): string {
		if (score >= 90) return 'text-green-600';
		if (score >= 70) return 'text-yellow-600';
		return 'text-red-600';
	}

	/**
	 * Get replacement score label
	 * Following Code Complete: Clear labeling
	 */
	function getScoreLabel(score: number): string {
		if (score >= 90) return 'Excellent';
		if (score >= 70) return 'Good';
		if (score >= 50) return 'Fair';
		return 'Poor';
	}
</script>

<!-- Slide-out Panel -->
<div
	class="fixed inset-y-0 right-0 z-50 w-96 transform border-l border-gray-200 bg-white shadow-xl transition-transform duration-300"
>
	<!-- Header -->
	<div class="flex items-center justify-between border-b border-gray-200 px-4 py-3">
		<div>
			<h3 class="text-lg font-medium text-gray-900">Shift Reassignment</h3>
			{#if panelState.leaveRequest}
				<p class="text-sm text-gray-500">
					{affectedEmployeeName} • {panelState.leaveRequest.requestType} leave
				</p>
			{/if}
		</div>
		<button
			onclick={onClose}
			class="rounded-md text-gray-400 hover:text-gray-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
		>
			<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M6 18L18 6M6 6l12 12"
				/>
			</svg>
		</button>
	</div>

	<!-- Content -->
	<div class="flex-1 overflow-y-auto">
		<!-- Leave Request Info -->
		{#if panelState.leaveRequest}
			<div class="border-b border-gray-200 p-4">
				<div class="rounded-lg bg-yellow-50 p-3">
					<div class="flex items-start">
						<svg class="mt-0.5 h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
							<path
								fill-rule="evenodd"
								d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
								clip-rule="evenodd"
							/>
						</svg>
						<div class="ml-3">
							<h4 class="text-sm font-medium text-yellow-800">Leave Request Approved</h4>
							<p class="mt-1 text-sm text-yellow-700">
								{panelState.leaveRequest.startDate.toLocaleDateString()} - {panelState.leaveRequest.endDate.toLocaleDateString()}
							</p>
							{#if panelState.leaveRequest.reason}
								<p class="mt-1 text-sm text-yellow-700">{panelState.leaveRequest.reason}</p>
							{/if}
						</div>
					</div>
				</div>
			</div>
		{/if}

		<!-- Affected Shifts -->
		<div class="p-4">
			<h4 class="mb-3 text-sm font-medium text-gray-900">
				Affected Shifts ({panelState.affectedShifts.length})
			</h4>

			<div class="space-y-3">
				{#each panelState.affectedShifts as shift}
					<div class="rounded-lg border border-gray-200">
						<!-- Shift Header -->
						<div class="p-3">
							<div class="flex items-center justify-between">
								<div>
									<p class="text-sm font-medium text-gray-900">
										{shift.date.toLocaleDateString('en-US', {
											weekday: 'short',
											month: 'short',
											day: 'numeric'
										})}
									</p>
									<p class="text-sm text-gray-500">
										{shift.startTime} - {shift.endTime} • {getVenueName(shift)}
									</p>
								</div>
								<button
									onclick={() => toggleShiftDetails(shift.id)}
									class="text-gray-400 hover:text-gray-500"
								>
									<svg
										class="h-5 w-5 transform transition-transform {expandedShift === shift.id
											? 'rotate-180'
											: ''}"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M19 9l-7 7-7-7"
										/>
									</svg>
								</button>
							</div>

							<!-- Current Selection -->
							{#if selectedReplacements.has(shift.id)}
								{@const selectedEmployeeId = selectedReplacements.get(shift.id)}
								{@const selectedEmployee = panelState.suggestedReplacements.find(
									(r) => r.employee.id === selectedEmployeeId
								)}
								{#if selectedEmployee}
									<div class="mt-2 flex items-center justify-between rounded-md bg-blue-50 p-2">
										<div class="flex items-center space-x-2">
											<div
												class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100"
											>
												<span class="text-xs font-medium text-blue-700">
													{(selectedEmployee.employee.name || 'Unknown')
														.split(' ')
														.map((n) => n[0])
														.join('')}
												</span>
											</div>
											<span class="text-sm font-medium text-blue-900">
												{selectedEmployee.employee.name}
											</span>
										</div>
										<button
											onclick={() => removeReplacement(shift.id)}
											class="text-blue-400 hover:text-blue-500"
										>
											<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M6 18L18 6M6 6l12 12"
												/>
											</svg>
										</button>
									</div>
								{/if}
							{/if}
						</div>

						<!-- Replacement Options (when expanded) -->
						{#if expandedShift === shift.id}
							<div class="border-t border-gray-200 p-3">
								<h5 class="mb-2 text-xs font-medium text-gray-700">Suggested Replacements</h5>

								<div class="max-h-48 space-y-2 overflow-y-auto">
									{#each panelState.suggestedReplacements.filter((r) => r.availabilityScore > 0) as replacement}
										<button
											onclick={() => selectReplacement(shift.id, replacement.employee.id)}
											class="w-full rounded-md border p-2 text-left transition-colors hover:bg-gray-50
												{selectedReplacements.get(shift.id) === replacement.employee.id
												? 'border-blue-300 bg-blue-50'
												: 'border-gray-200'}"
										>
											<div class="flex items-center justify-between">
												<div class="flex items-center space-x-2">
													<div
														class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100"
													>
														<span class="text-xs font-medium text-gray-700">
															{(replacement.employee.name || 'Unknown')
																.split(' ')
																.map((n) => n[0])
																.join('')}
														</span>
													</div>
													<div>
														<p class="text-sm font-medium text-gray-900">
															{replacement.employee.name}
														</p>
														<p class="text-xs text-gray-500">
															{replacement.employee.role}
														</p>
													</div>
												</div>
												<div class="text-right">
													<div class="flex items-center space-x-1">
														<span
															class="text-xs font-medium {getScoreColor(
																replacement.availabilityScore
															)}"
														>
															{replacement.availabilityScore}%
														</span>
														{#if replacement.isRecommended}
															<svg
																class="h-3 w-3 text-green-500"
																fill="currentColor"
																viewBox="0 0 20 20"
															>
																<path
																	fill-rule="evenodd"
																	d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
																	clip-rule="evenodd"
																/>
															</svg>
														{/if}
													</div>
													<p class="text-xs text-gray-500">
														{getScoreLabel(replacement.availabilityScore)}
													</p>
												</div>
											</div>

											{#if replacement.conflictReason}
												<p class="mt-1 text-xs text-yellow-600">
													⚠️ {replacement.conflictReason}
												</p>
											{/if}

											{#if replacement.performanceScore}
												<div class="mt-1 flex items-center space-x-2 text-xs text-gray-500">
													<span>Performance: {replacement.performanceScore}%</span>
													{#if replacement.tipEarnings}
														<span>• Recent tips: ${replacement.tipEarnings}</span>
													{/if}
												</div>
											{/if}
										</button>
									{/each}

									{#if panelState.suggestedReplacements.filter((r) => r.availabilityScore > 0).length === 0}
										<div class="py-4 text-center">
											<svg
												class="mx-auto h-8 w-8 text-gray-400"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
												/>
											</svg>
											<p class="mt-2 text-sm text-gray-500">No available replacements</p>
											<button
												onclick={() => unassignShift(shift)}
												class="mt-2 text-xs text-red-600 hover:text-red-700"
											>
												Leave unassigned
											</button>
										</div>
									{/if}
								</div>
							</div>
						{/if}
					</div>
				{/each}
			</div>
		</div>
	</div>

	<!-- Footer Actions -->
	<div class="border-t border-gray-200 p-4">
		<div class="flex space-x-3">
			<button
				onclick={onClose}
				disabled={isLoading}
				class="flex-1 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50"
			>
				Cancel
			</button>

			<button
				onclick={applyReassignments}
				disabled={!hasSelections || isLoading}
				class="flex-1 rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50"
			>
				{#if isLoading}
					<svg
						class="mx-auto h-4 w-4 animate-spin"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
						/>
					</svg>
				{:else}
					Apply Changes ({selectedReplacements.size})
				{/if}
			</button>
		</div>

		{#if hasSelections}
			<p class="mt-2 text-center text-xs text-gray-500">
				{selectedReplacements.size} of {panelState.affectedShifts.length} shifts will be reassigned
			</p>
		{/if}
	</div>
</div>

<!-- Backdrop -->
<div
	class="bg-opacity-75 fixed inset-0 z-40 bg-gray-500 transition-opacity"
	onclick={onClose}
></div>

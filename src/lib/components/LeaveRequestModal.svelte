<script lang="ts">
	/**
	 * Leave Request Modal Component
	 * Following Code Complete principles: Clear form validation, user feedback
	 * Integrates with leave management system
	 */

	import { leaveService } from '$lib/services/leaveService.js';
	import type {
		LeaveRequest,
		LeaveRequestFormData,
		LeaveRequestType,
		Employee
	} from '$lib/types.js';

	interface Props {
		leaveRequest?: LeaveRequest | null;
		employees: Employee[];
		currentUserId: string;
		organizationId: string;
		mode: 'create' | 'edit' | 'approve';
		onClose: () => void;
		onSave?: (leaveRequest: LeaveRequest) => void;
		onApprove?: (data: { requestId: string; status: 'approved' | 'denied'; notes?: string }) => void;
		onDelete?: (requestId: string) => void;
	}

	let {
		leaveRequest = null,
		employees,
		currentUserId,
		organizationId,
		mode,
		onClose,
		onSave,
		onApprove,
		onDelete
	}: Props = $props();

	// Form state
	let formData: LeaveRequestFormData = $state({
		employeeId: leaveRequest?.employeeId || currentUserId,
		requestType: leaveRequest?.requestType || 'vacation',
		startDate: leaveRequest?.startDate.toISOString().split('T')[0] || '',
		endDate: leaveRequest?.endDate.toISOString().split('T')[0] || '',
		reason: leaveRequest?.reason || ''
	});

	let managerNotes = $state(leaveRequest?.managerNotes || '');
	let approvalStatus: 'approved' | 'denied' = $state('approved');

	// UI state
	let isSubmitting = $state(false);
	let errors: Record<string, string> = $state({});

	// Computed
	let selectedEmployee = $derived(
		employees.find(emp => emp.id === formData.employeeId)
	);

	let isApprovalMode = $derived(mode === 'approve');
	let canEditRequest = $derived(
		mode === 'create' || 
		(mode === 'edit' && leaveRequest?.status === 'pending')
	);

	let leaveTypeOptions: { value: LeaveRequestType; label: string; color: string }[] = [
		{ value: 'vacation', label: 'Vacation', color: 'bg-yellow-100 text-yellow-800' },
		{ value: 'sick', label: 'Sick Leave', color: 'bg-red-100 text-red-800' },
		{ value: 'personal', label: 'Personal', color: 'bg-blue-100 text-blue-800' },
		{ value: 'emergency', label: 'Emergency', color: 'bg-purple-100 text-purple-800' },
		{ value: 'other', label: 'Other', color: 'bg-gray-100 text-gray-800' }
	];

	/**
	 * Validate form data
	 * Following Code Complete: Comprehensive validation with clear error messages
	 */
	function validateForm(): boolean {
		errors = {};

		if (!formData.employeeId) {
			errors.employeeId = 'Employee is required';
		}

		if (!formData.startDate) {
			errors.startDate = 'Start date is required';
		}

		if (!formData.endDate) {
			errors.endDate = 'End date is required';
		}

		if (formData.startDate && formData.endDate) {
			const startDate = new Date(formData.startDate);
			const endDate = new Date(formData.endDate);
			
			if (startDate > endDate) {
				errors.endDate = 'End date must be after start date';
			}

			// Check if dates are too far in the past
			const thirtyDaysAgo = new Date();
			thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
			
			if (startDate < thirtyDaysAgo) {
				errors.startDate = 'Cannot create leave requests more than 30 days in the past';
			}
		}

		return Object.keys(errors).length === 0;
	}

	/**
	 * Handle form submission
	 * Following Code Complete: Clear error handling, user feedback
	 */
	async function handleSubmit() {
		if (!validateForm()) {
			return;
		}

		isSubmitting = true;

		try {
			if (mode === 'create') {
				const newLeaveRequest = await leaveService.createLeaveRequest(
					organizationId,
					formData
				);
				onSave?.(newLeaveRequest);
			} else if (mode === 'edit') {
				// For edit mode, we would need an update function
				// For now, treating as create with existing data
				console.log('Edit mode not fully implemented yet');
			}

			onClose();

		} catch (error) {
			console.error('❌ LeaveRequestModal: Failed to save leave request:', error);
			errors.submit = error instanceof Error ? error.message : 'Failed to save leave request';
		} finally {
			isSubmitting = false;
		}
	}

	/**
	 * Handle approval/denial
	 * Following Code Complete: Clear approval workflow
	 */
	async function handleApproval() {
		if (!leaveRequest) return;

		isSubmitting = true;

		try {
			await leaveService.updateLeaveRequestStatus(
				leaveRequest.id,
				approvalStatus,
				managerNotes.trim() || undefined
			);

			onApprove?.({
				requestId: leaveRequest.id,
				status: approvalStatus,
				notes: managerNotes.trim() || undefined
			});

			onClose();

		} catch (error) {
			console.error('❌ LeaveRequestModal: Failed to update leave request:', error);
			errors.submit = error instanceof Error ? error.message : 'Failed to update leave request';
		} finally {
			isSubmitting = false;
		}
	}

	/**
	 * Handle delete request
	 * Following Code Complete: Clear confirmation workflow
	 */
	async function handleDelete() {
		if (!leaveRequest || !confirm('Are you sure you want to cancel this leave request?')) {
			return;
		}

		isSubmitting = true;

		try {
			await leaveService.cancelLeaveRequest(leaveRequest.id);
			onDelete?.(leaveRequest.id);
			onClose();

		} catch (error) {
			console.error('❌ LeaveRequestModal: Failed to cancel leave request:', error);
			errors.submit = error instanceof Error ? error.message : 'Failed to cancel leave request';
		} finally {
			isSubmitting = false;
		}
	}

	/**
	 * Handle form submission with event prevention
	 * Following Code Complete: Clear event handling
	 */
	function handleFormSubmit(event: Event) {
		event.preventDefault();
		if (isApprovalMode) {
			handleApproval();
		} else {
			handleSubmit();
		}
	}

	/**
	 * Calculate number of days for the leave request
	 * Following Code Complete: Pure function, clear calculation
	 */
	function calculateLeaveDays(): number {
		if (!formData.startDate || !formData.endDate) return 0;

		const start = new Date(formData.startDate);
		const end = new Date(formData.endDate);
		const diffTime = Math.abs(end.getTime() - start.getTime());
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates

		return diffDays;
	}
</script>

<!-- Modal Backdrop -->
<div class="fixed inset-0 z-50 overflow-y-auto">
	<div class="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
		<!-- Background overlay -->
		<div 
			class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
			onclick={onClose}
		></div>

		<!-- Modal panel -->
		<div class="inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
			<!-- Header -->
			<div class="mb-4">
				<div class="flex items-center justify-between">
					<h3 class="text-lg font-medium leading-6 text-gray-900">
						{#if mode === 'create'}
							Request Leave
						{:else if mode === 'edit'}
							Edit Leave Request
						{:else if mode === 'approve'}
							Review Leave Request
						{/if}
					</h3>
					<button
						onclick={onClose}
						class="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
					>
						<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					</button>
				</div>

				{#if leaveRequest && mode === 'approve'}
					<div class="mt-2 flex items-center space-x-2">
						<span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium {leaveTypeOptions.find(opt => opt.value === leaveRequest.requestType)?.color || 'bg-gray-100 text-gray-800'}">
							{leaveTypeOptions.find(opt => opt.value === leaveRequest.requestType)?.label || leaveRequest.requestType}
						</span>
						<span class="text-sm text-gray-500">
							Requested by {leaveRequest.requestedByName || 'Unknown'}
						</span>
					</div>
				{/if}
			</div>

			<!-- Form -->
			<form onsubmit={handleFormSubmit}>
				<div class="space-y-4">
					<!-- Employee Selection (only for managers creating requests for others) -->
					{#if canEditRequest && employees.length > 1}
						<div>
							<label for="employee" class="block text-sm font-medium text-gray-700">
								Employee
							</label>
							<select
								id="employee"
								bind:value={formData.employeeId}
								disabled={!canEditRequest}
								class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
								class:border-red-300={errors.employeeId}
							>
								{#each employees as employee}
									<option value={employee.id}>{employee.name}</option>
								{/each}
							</select>
							{#if errors.employeeId}
								<p class="mt-1 text-sm text-red-600">{errors.employeeId}</p>
							{/if}
						</div>
					{:else if selectedEmployee}
						<div>
							<label class="block text-sm font-medium text-gray-700">Employee</label>
							<div class="mt-1 flex items-center space-x-3">
								<div class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
									<span class="text-sm font-medium text-blue-700">
										{(selectedEmployee.name || 'Unknown').split(' ').map(n => n[0]).join('')}
									</span>
								</div>
								<span class="text-sm font-medium text-gray-900">{selectedEmployee.name || 'Unknown Employee'}</span>
							</div>
						</div>
					{/if}

					<!-- Leave Type -->
					{#if canEditRequest}
						<div>
							<label for="requestType" class="block text-sm font-medium text-gray-700">
								Leave Type
							</label>
							<select
								id="requestType"
								bind:value={formData.requestType}
								class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
							>
								{#each leaveTypeOptions as option}
									<option value={option.value}>{option.label}</option>
								{/each}
							</select>
						</div>
					{:else if leaveRequest}
						<div>
							<label class="block text-sm font-medium text-gray-700">Leave Type</label>
							<span class="mt-1 inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium {leaveTypeOptions.find(opt => opt.value === leaveRequest.requestType)?.color || 'bg-gray-100 text-gray-800'}">
								{leaveTypeOptions.find(opt => opt.value === leaveRequest.requestType)?.label || leaveRequest.requestType}
							</span>
						</div>
					{/if}

					<!-- Date Range -->
					<div class="grid grid-cols-2 gap-4">
						<div>
							<label for="startDate" class="block text-sm font-medium text-gray-700">
								Start Date
							</label>
							<input
								id="startDate"
								type="date"
								bind:value={formData.startDate}
								disabled={!canEditRequest}
								class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
								class:border-red-300={errors.startDate}
							/>
							{#if errors.startDate}
								<p class="mt-1 text-sm text-red-600">{errors.startDate}</p>
							{/if}
						</div>

						<div>
							<label for="endDate" class="block text-sm font-medium text-gray-700">
								End Date
							</label>
							<input
								id="endDate"
								type="date"
								bind:value={formData.endDate}
								disabled={!canEditRequest}
								class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
								class:border-red-300={errors.endDate}
							/>
							{#if errors.endDate}
								<p class="mt-1 text-sm text-red-600">{errors.endDate}</p>
							{/if}
						</div>
					</div>

					<!-- Duration Display -->
					{#if formData.startDate && formData.endDate}
						<div class="rounded-md bg-blue-50 p-3">
							<div class="flex">
								<svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
								</svg>
								<div class="ml-3">
									<p class="text-sm text-blue-700">
										<span class="font-medium">{calculateLeaveDays()} day{calculateLeaveDays() !== 1 ? 's' : ''}</span>
										of leave requested
									</p>
								</div>
							</div>
						</div>
					{/if}

					<!-- Reason -->
					<div>
						<label for="reason" class="block text-sm font-medium text-gray-700">
							Reason {canEditRequest ? '(optional)' : ''}
						</label>
						<textarea
							id="reason"
							bind:value={formData.reason}
							disabled={!canEditRequest}
							rows="3"
							class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
							placeholder="Provide additional details about your leave request..."
						></textarea>
					</div>

					<!-- Approval Section (for managers) -->
					{#if isApprovalMode}
						<div class="border-t border-gray-200 pt-4">
							<div class="space-y-4">
								<div>
									<label class="block text-sm font-medium text-gray-700">Decision</label>
									<div class="mt-2 space-y-2">
										<label class="flex items-center">
											<input
												type="radio"
												bind:group={approvalStatus}
												value="approved"
												class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
											/>
											<span class="ml-2 text-sm text-gray-700">Approve</span>
										</label>
										<label class="flex items-center">
											<input
												type="radio"
												bind:group={approvalStatus}
												value="denied"
												class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
											/>
											<span class="ml-2 text-sm text-gray-700">Deny</span>
										</label>
									</div>
								</div>

								<div>
									<label for="managerNotes" class="block text-sm font-medium text-gray-700">
										Manager Notes (optional)
									</label>
									<textarea
										id="managerNotes"
										bind:value={managerNotes}
										rows="2"
										class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
										placeholder="Add notes about your decision..."
									></textarea>
								</div>
							</div>
						</div>
					{/if}

					<!-- Error Message -->
					{#if errors.submit}
						<div class="rounded-md bg-red-50 p-4">
							<div class="flex">
								<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
								</svg>
								<div class="ml-3">
									<p class="text-sm text-red-700">{errors.submit}</p>
								</div>
							</div>
						</div>
					{/if}
				</div>

				<!-- Actions -->
				<div class="mt-6 flex justify-between">
					<div>
						{#if mode === 'edit' && leaveRequest && leaveRequest.status === 'pending'}
							<button
								type="button"
								onclick={handleDelete}
								disabled={isSubmitting}
								class="inline-flex justify-center rounded-md border border-red-300 bg-white px-4 py-2 text-sm font-medium text-red-700 shadow-sm hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50"
							>
								Cancel Request
							</button>
						{/if}
					</div>

					<div class="flex space-x-3">
						<button
							type="button"
							onclick={onClose}
							disabled={isSubmitting}
							class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
						>
							Cancel
						</button>

						<button
							type="submit"
							disabled={isSubmitting}
							class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
						>
							{#if isSubmitting}
								<svg class="mr-2 h-4 w-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
								</svg>
							{/if}
							{#if isApprovalMode}
								{approvalStatus === 'approved' ? 'Approve' : 'Deny'} Request
							{:else if mode === 'create'}
								Submit Request
							{:else}
								Update Request
							{/if}
						</button>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>

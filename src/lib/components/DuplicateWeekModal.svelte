<script lang="ts">
	import type { WeeklySchedule, DuplicateWeekResult, DuplicateWeekRequest } from '$lib/types.js';
	import { formatWeekRange, getWeekStart, getWeekDates } from '$lib/utils.js';
	import { Button, Modal } from '$lib/components/ui/index.js';
	import WeekCalendarPicker from './WeekCalendarPicker.svelte';

	interface Props {
		isOpen: boolean;
		currentWeekStart: Date;
		schedule: WeeklySchedule;
		organizationId?: string;
		onClose: () => void;
		onDuplicate: (request: DuplicateWeekRequest) => Promise<DuplicateWeekResult>;
	}

	let { 
		isOpen, 
		currentWeekStart, 
		schedule, 
		organizationId,
		onClose, 
		onDuplicate 
	}: Props = $props();

	// Modal state
	let step = $state<'source' | 'target' | 'preview' | 'progress' | 'results'>('source');
	let sourceWeekStart = $state<Date>(currentWeekStart);
	let targetWeekStarts = $state<Date[]>([]);
	let conflictResolution = $state<'merge' | 'replace'>('merge');
	let isProcessing = $state(false);
	let results = $state<DuplicateWeekResult | null>(null);
	let errorMessage = $state('');

	// Calendar picker state
	let isSourceCalendarOpen = $state(false);

	// Computed values
	let sourceWeekShifts = $derived(
		schedule.shifts.filter(shift => {
			// Ensure shift.date is a Date object
			const shiftDate = shift.date instanceof Date ? shift.date : new Date(shift.date);

			// Get the week start for both the shift date and source week
			const shiftWeekStart = getWeekStart(shiftDate);
			const sourceWeekStartNormalized = getWeekStart(sourceWeekStart);

			// Compare using date strings to avoid timezone issues
			const shiftWeekStartStr = shiftWeekStart.toISOString().split('T')[0];
			const sourceWeekStartStr = sourceWeekStartNormalized.toISOString().split('T')[0];

			// Removed debug logging for production

			return shiftWeekStartStr === sourceWeekStartStr;
		})
	);

	let sourceWeekDates = $derived(getWeekDates(sourceWeekStart));
	let hasShiftsInSourceWeek = $derived(sourceWeekShifts.length > 0);

	// Removed debug effect for production

	// Reset modal state when opened
	$effect(() => {
		if (isOpen) {
			step = 'source';
			sourceWeekStart = currentWeekStart;
			targetWeekStarts = [];
			conflictResolution = 'merge';
			isProcessing = false;
			results = null;
			errorMessage = '';
			isSourceCalendarOpen = false;
		}
	});

	// Calendar picker functions
	function openSourceCalendar() {
		isSourceCalendarOpen = true;
	}

	function closeSourceCalendar() {
		isSourceCalendarOpen = false;
	}

	function handleSourceWeekSelect(weekStart: Date) {
		sourceWeekStart = weekStart;
		closeSourceCalendar();
	}

	function navigateSourceWeek(direction: 'prev' | 'next') {
		const newDate = new Date(sourceWeekStart);
		newDate.setDate(sourceWeekStart.getDate() + (direction === 'next' ? 7 : -7));
		sourceWeekStart = newDate;
	}

	function addTargetWeek() {
		const newWeek = new Date(sourceWeekStart);
		newWeek.setDate(sourceWeekStart.getDate() + 7);
		
		// Find next available week that's not already selected
		while (targetWeekStarts.some(week => week.getTime() === newWeek.getTime())) {
			newWeek.setDate(newWeek.getDate() + 7);
		}
		
		targetWeekStarts = [...targetWeekStarts, newWeek];
	}

	function removeTargetWeek(index: number) {
		targetWeekStarts = targetWeekStarts.filter((_, i) => i !== index);
	}

	function addMultipleWeeks() {
		const startWeek = new Date(sourceWeekStart);
		startWeek.setDate(sourceWeekStart.getDate() + 7);
		
		const newWeeks: Date[] = [];
		for (let i = 0; i < 4; i++) {
			const week = new Date(startWeek);
			week.setDate(startWeek.getDate() + (i * 7));
			
			// Only add if not already selected
			if (!targetWeekStarts.some(existing => existing.getTime() === week.getTime())) {
				newWeeks.push(week);
			}
		}
		
		targetWeekStarts = [...targetWeekStarts, ...newWeeks];
	}

	function proceedToTarget() {
		if (!hasShiftsInSourceWeek) {
			errorMessage = 'Selected source week has no shifts to duplicate.';
			return;
		}
		errorMessage = '';
		step = 'target';
	}

	function proceedToPreview() {
		if (targetWeekStarts.length === 0) {
			errorMessage = 'Please select at least one target week.';
			return;
		}
		errorMessage = '';
		step = 'preview';
	}

	async function executeDuplication() {
		step = 'progress';
		isProcessing = true;
		errorMessage = '';

		try {
			// Starting duplication process

			const request: DuplicateWeekRequest = {
				sourceWeekStart,
				targetWeekStarts,
				conflictResolution,
				organizationId: organizationId || 'auto' // Let RPC function determine from auth context
			};

			// Add a client-side timeout as additional protection
			const timeoutPromise = new Promise<never>((_, reject) => {
				setTimeout(() => {
					reject(new Error('Modal operation timed out after 45 seconds'));
				}, 45000); // 45 second timeout (longer than service timeout)
			});

			results = await Promise.race([onDuplicate(request), timeoutPromise]);
			step = 'results';
		} catch (error) {
			console.error('❌ DuplicateWeekModal: Duplication failed:', error);
			errorMessage = error instanceof Error ? error.message : 'Duplication failed';
			step = 'preview';
		} finally {
			isProcessing = false;
		}
	}

	function handleClose() {
		if (!isProcessing) {
			onClose();
		}
	}

	function goBack() {
		if (step === 'target') {
			step = 'source';
		} else if (step === 'preview') {
			step = 'target';
		} else if (step === 'results') {
			step = 'preview';
		}
		errorMessage = '';
	}
</script>

<Modal
	isOpen={isOpen}
	title="Duplicate Week"
	size="lg"
	onclose={handleClose}
>
	{#if step === 'source'}
		<!-- Step 1: Source Week Selection -->
		<div class="space-y-6">
			<div>
				<h3 class="text-lg font-medium text-gray-900 mb-4">Select Source Week</h3>
				<p class="text-sm text-gray-600 mb-4">
					Choose the week containing the shifts you want to duplicate.
				</p>
			</div>

			<!-- Source Week Navigation -->
			<div class="flex items-center justify-center space-x-4">
				<button
					onclick={() => navigateSourceWeek('prev')}
					class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
					aria-label="Previous week"
				>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
					</svg>
				</button>

				<!-- Week Display with Calendar Picker -->
				<div class="relative">
					<button
						onclick={openSourceCalendar}
						class="text-center min-w-[200px] px-3 py-2 hover:bg-gray-100 rounded-lg transition-colors group"
						aria-label="Select source week from calendar"
					>
						<div class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
							{formatWeekRange(sourceWeekStart)}
						</div>
						<div class="text-sm text-gray-500 group-hover:text-blue-500 transition-colors">
							{sourceWeekShifts.length} shifts • Click to select
						</div>
					</button>

					<!-- Calendar Picker -->
					<WeekCalendarPicker
						selectedWeek={sourceWeekStart}
						isOpen={isSourceCalendarOpen}
						onWeekSelect={handleSourceWeekSelect}
						onClose={closeSourceCalendar}
						position="bottom"
					/>
				</div>

				<button
					onclick={() => navigateSourceWeek('next')}
					class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
					aria-label="Next week"
				>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
					</svg>
				</button>
			</div>

			<!-- Source Week Preview -->
			{#if hasShiftsInSourceWeek}
				<div class="bg-gray-50 rounded-lg p-4">
					<h4 class="text-sm font-medium text-gray-900 mb-3">Shifts to be duplicated:</h4>
					<div class="grid grid-cols-7 gap-2 text-xs">
						{#each sourceWeekDates as date}
							{@const dayShifts = sourceWeekShifts.filter(s => {
								const shiftDate = s.date instanceof Date ? s.date : new Date(s.date);
								return shiftDate.toDateString() === date.toDateString();
							})}
							<div class="text-center">
								<div class="font-medium text-gray-700 mb-1">
									{date.toLocaleDateString('en-US', { weekday: 'short' })}
								</div>
								<div class="text-gray-600">
									{dayShifts.length} shifts
								</div>
							</div>
						{/each}
					</div>
				</div>
			{:else}
				<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
					<div class="flex items-center">
						<svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
						</svg>
						<span class="text-sm text-yellow-800">No shifts found in selected week</span>
					</div>
				</div>
			{/if}

			{#if errorMessage}
				<div class="bg-red-50 border border-red-200 rounded-lg p-4">
					<div class="flex items-center">
						<svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
						</svg>
						<span class="text-sm text-red-800">{errorMessage}</span>
					</div>
				</div>
			{/if}

			<!-- Actions -->
			<div class="flex justify-between pt-4">
				<Button variant="secondary" onclick={handleClose}>
					Cancel
				</Button>
				<Button onclick={proceedToTarget} disabled={!hasShiftsInSourceWeek}>
					Next: Select Target Weeks
				</Button>
			</div>
		</div>
	{/if}

	{#if step === 'target'}
		<!-- Step 2: Target Week Selection -->
		<div class="space-y-6">
			<div>
				<h3 class="text-lg font-medium text-gray-900 mb-4">Select Target Weeks</h3>
				<p class="text-sm text-gray-600 mb-4">
					Choose one or more weeks where you want to duplicate the shifts.
				</p>
			</div>

			<!-- Quick Actions -->
			<div class="flex space-x-3">
				<Button variant="secondary" size="sm" onclick={addTargetWeek}>
					<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
					</svg>
					Add Week
				</Button>
				<Button variant="secondary" size="sm" onclick={addMultipleWeeks}>
					<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
					</svg>
					Add Next 4 Weeks
				</Button>
			</div>

			<!-- Selected Target Weeks -->
			{#if targetWeekStarts.length > 0}
				<div class="space-y-2">
					<h4 class="text-sm font-medium text-gray-900">Selected target weeks:</h4>
					{#each targetWeekStarts as targetWeek, index}
						<div class="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-3">
							<div>
								<div class="font-medium text-blue-900">
									{formatWeekRange(targetWeek)}
								</div>
								<div class="text-sm text-blue-700">
									Week of {targetWeek.toLocaleDateString()}
								</div>
							</div>
							<button
								onclick={() => removeTargetWeek(index)}
								class="text-blue-600 hover:text-blue-800 transition-colors"
								aria-label="Remove week"
							>
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
								</svg>
							</button>
						</div>
					{/each}
				</div>
			{:else}
				<div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
					<svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
					</svg>
					<p class="text-gray-600">No target weeks selected</p>
					<p class="text-sm text-gray-500 mt-1">Click "Add Week" to select target weeks</p>
				</div>
			{/if}

			{#if errorMessage}
				<div class="bg-red-50 border border-red-200 rounded-lg p-4">
					<div class="flex items-center">
						<svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
						</svg>
						<span class="text-sm text-red-800">{errorMessage}</span>
					</div>
				</div>
			{/if}

			<!-- Actions -->
			<div class="flex justify-between pt-4">
				<Button variant="secondary" onclick={goBack}>
					Back
				</Button>
				<Button onclick={proceedToPreview} disabled={targetWeekStarts.length === 0}>
					Next: Preview & Confirm
				</Button>
			</div>
		</div>
	{/if}

	{#if step === 'preview'}
		<!-- Step 3: Preview & Confirmation -->
		<div class="space-y-6">
			<div>
				<h3 class="text-lg font-medium text-gray-900 mb-4">Preview & Confirm</h3>
				<p class="text-sm text-gray-600 mb-4">
					Review the duplication settings and choose how to handle conflicts.
				</p>
			</div>

			<!-- Summary -->
			<div class="bg-gray-50 rounded-lg p-4">
				<h4 class="text-sm font-medium text-gray-900 mb-3">Duplication Summary</h4>
				<div class="grid grid-cols-2 gap-4 text-sm">
					<div>
						<span class="text-gray-600">Source Week:</span>
						<div class="font-medium">{formatWeekRange(sourceWeekStart)}</div>
					</div>
					<div>
						<span class="text-gray-600">Shifts to Duplicate:</span>
						<div class="font-medium">{sourceWeekShifts.length} shifts</div>
					</div>
					<div>
						<span class="text-gray-600">Target Weeks:</span>
						<div class="font-medium">{targetWeekStarts.length} weeks</div>
					</div>
					<div>
						<span class="text-gray-600">Total New Shifts:</span>
						<div class="font-medium">{sourceWeekShifts.length * targetWeekStarts.length} shifts</div>
					</div>
				</div>
			</div>

			<!-- Conflict Resolution -->
			<div>
				<h4 class="text-sm font-medium text-gray-900 mb-3">Conflict Resolution</h4>
				<p class="text-xs text-gray-600 mb-3">
					How should conflicts be handled when an employee already has a shift at the same time?
				</p>
				<div class="space-y-3">
					<label class="flex items-start">
						<input
							type="radio"
							bind:group={conflictResolution}
							value="merge"
							class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mt-0.5"
						/>
						<div class="ml-3">
							<div class="text-sm font-medium text-gray-900">Merge (Skip Conflicts)</div>
							<div class="text-xs text-gray-600">Skip creating shifts when conflicts are detected. Existing shifts remain unchanged.</div>
						</div>
					</label>
					<label class="flex items-start">
						<input
							type="radio"
							bind:group={conflictResolution}
							value="replace"
							class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mt-0.5"
						/>
						<div class="ml-3">
							<div class="text-sm font-medium text-gray-900">Replace (Overwrite Conflicts)</div>
							<div class="text-xs text-gray-600">Replace existing shifts with duplicated ones when conflicts are detected.</div>
						</div>
					</label>
				</div>
			</div>

			{#if errorMessage}
				<div class="bg-red-50 border border-red-200 rounded-lg p-4">
					<div class="flex items-center">
						<svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
						</svg>
						<span class="text-sm text-red-800">{errorMessage}</span>
					</div>
				</div>
			{/if}

			<!-- Actions -->
			<div class="flex justify-between pt-4">
				<Button variant="secondary" onclick={goBack}>
					Back
				</Button>
				<Button onclick={executeDuplication}>
					<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
					</svg>
					Duplicate Week
				</Button>
			</div>
		</div>
	{/if}

	{#if step === 'progress'}
		<!-- Step 4: Progress -->
		<div class="space-y-6">
			<div class="text-center">
				<h3 class="text-lg font-medium text-gray-900 mb-4">Duplicating Week</h3>
				<p class="text-sm text-gray-600 mb-6">
					Please wait while we duplicate your shifts...
				</p>
			</div>

			<!-- Progress Indicator -->
			<div class="flex justify-center">
				<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
			</div>

			<!-- Progress Details -->
			<div class="bg-blue-50 rounded-lg p-4 text-center">
				<div class="text-sm text-blue-800">
					Duplicating {sourceWeekShifts.length} shifts to {targetWeekStarts.length} week{targetWeekStarts.length === 1 ? '' : 's'}
				</div>
				<div class="text-xs text-blue-600 mt-1">
					This may take a few moments...
				</div>
			</div>
		</div>
	{/if}

	{#if step === 'results'}
		<!-- Step 5: Results -->
		<div class="space-y-6">
			<div>
				<h3 class="text-lg font-medium text-gray-900 mb-4">Duplication Complete</h3>
			</div>

			{#if results}
				<!-- Success Summary -->
				{#if results.success}
					<div class="bg-green-50 border border-green-200 rounded-lg p-4">
						<div class="flex items-center mb-3">
							<svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
							</svg>
							<h4 class="text-lg font-medium text-green-900">Success!</h4>
						</div>
						<p class="text-sm text-green-800 mb-4">{results.message}</p>

						<!-- Results Summary -->
						<div class="grid grid-cols-2 gap-4 text-sm">
							<div>
								<span class="text-green-700">Shifts Duplicated:</span>
								<div class="font-medium text-green-900">{results.totalShiftsDuplicated}</div>
							</div>
							<div>
								<span class="text-green-700">Weeks Processed:</span>
								<div class="font-medium text-green-900">{results.targetWeeksProcessed}</div>
							</div>
							{#if results.conflictsDetected > 0}
								<div>
									<span class="text-green-700">Conflicts Detected:</span>
									<div class="font-medium text-green-900">{results.conflictsDetected}</div>
								</div>
								<div>
									<span class="text-green-700">Conflicts Resolved:</span>
									<div class="font-medium text-green-900">{results.conflictsResolved}</div>
								</div>
							{/if}
						</div>
					</div>

					<!-- Detailed Results -->
					{#if results.details && results.details.length > 0}
						<div class="space-y-3">
							<h4 class="text-sm font-medium text-gray-900">Week-by-Week Results:</h4>
							{#each results.details as detail}
								<div class="bg-gray-50 rounded-lg p-3">
									<div class="flex justify-between items-start mb-2">
										<div class="font-medium text-gray-900">
											{formatWeekRange(new Date(detail.targetWeekStart))}
										</div>
										<div class="text-sm text-gray-600">
											{detail.shiftsDuplicated} shifts created
										</div>
									</div>

									{#if detail.conflictsFound && detail.conflictsFound.length > 0}
										<div class="text-xs text-gray-600">
											{detail.conflictsFound.length} conflicts detected, {detail.conflictsResolved} resolved
										</div>
									{/if}
								</div>
							{/each}
						</div>
					{/if}
				{:else}
					<!-- Error State -->
					<div class="bg-red-50 border border-red-200 rounded-lg p-4">
						<div class="flex items-center mb-3">
							<svg class="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
							</svg>
							<h4 class="text-lg font-medium text-red-900">Duplication Failed</h4>
						</div>
						<p class="text-sm text-red-800">{results.message}</p>
					</div>
				{/if}
			{/if}

			<!-- Actions -->
			<div class="flex justify-between pt-4">
				<Button variant="secondary" onclick={goBack}>
					Back to Preview
				</Button>
				<Button onclick={handleClose}>
					Close
				</Button>
			</div>
		</div>
	{/if}
</Modal>

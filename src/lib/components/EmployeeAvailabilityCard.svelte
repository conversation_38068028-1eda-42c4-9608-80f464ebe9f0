<script lang="ts">
	/**
	 * Employee Availability Card Component
	 * Shows availability status, leave requests, and unavailability patterns
	 * Following Code Complete principles: Clear visual hierarchy, informative display
	 */

	import type {
		Employee,
		EmployeeAvailabilityStatus,
		LeaveRequest,
		EmployeeUnavailability,
		UnavailabilityReason
	} from '$lib/types.js';

	interface Props {
		employee: Employee;
		availabilityStatus: EmployeeAvailabilityStatus;
		onLeaveRequestClick?: (leaveRequest: LeaveRequest) => void;
		onUnavailabilityClick?: (unavailability: EmployeeUnavailability) => void;
		showDetails?: boolean;
	}

	let {
		employee,
		availabilityStatus,
		onLeaveRequestClick,
		onUnavailabilityClick,
		showDetails = false
	}: Props = $props();

	// Computed values
	let availabilityColor = $derived(
		availabilityStatus.isAvailable
			? 'bg-green-100 text-green-800 border-green-200'
			: 'bg-red-100 text-red-800 border-red-200'
	);

	let availabilityIcon = $derived(
		availabilityStatus.isAvailable
			? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' // Check circle
			: 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' // X circle
	);

	let statusText = $derived(availabilityStatus.isAvailable ? 'Available' : 'Unavailable');

	/**
	 * Get color for leave request type
	 * Following Code Complete: Clear color mapping
	 */
	function getLeaveTypeColor(requestType: string): string {
		const colors: Record<string, string> = {
			vacation: 'bg-yellow-100 text-yellow-800',
			sick: 'bg-red-100 text-red-800',
			personal: 'bg-blue-100 text-blue-800',
			emergency: 'bg-purple-100 text-purple-800',
			other: 'bg-gray-100 text-gray-800'
		};
		return colors[requestType] || colors.other;
	}

	/**
	 * Get color for unavailability reason type
	 * Following Code Complete: Clear color mapping
	 */
	function getUnavailabilityColor(reasonType: string): string {
		const colors: Record<string, string> = {
			leave: 'bg-yellow-100 text-yellow-800',
			recurring: 'bg-blue-100 text-blue-800',
			one_time: 'bg-purple-100 text-purple-800'
		};
		return colors[reasonType] || 'bg-gray-100 text-gray-800';
	}

	/**
	 * Format time range for display
	 * Following Code Complete: Pure function, clear formatting
	 */
	function formatTimeRange(timeRange?: { start: string; end: string }): string {
		if (!timeRange) return 'All day';
		return `${timeRange.start} - ${timeRange.end}`;
	}

	/**
	 * Get readable reason type label
	 * Following Code Complete: Clear labeling
	 */
	function getReasonTypeLabel(type: string): string {
		const labels: Record<string, string> = {
			leave: 'Leave',
			recurring: 'Recurring',
			one_time: 'One-time'
		};
		return labels[type] || type;
	}
</script>

<div class="rounded-lg border {availabilityColor} p-3">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div class="flex items-center space-x-2">
			<!-- Employee Avatar -->
			<div
				class="flex h-6 w-6 items-center justify-center rounded-full bg-white sm:h-7 sm:w-7 md:h-8 md:w-8"
			>
				<span class="text-[8px] font-medium text-gray-700 sm:text-[9px] md:text-[10px]">
					{(employee.name || '')
						.split(' ')
						.map((n) => n[0])
						.join('')}
				</span>
			</div>

			<!-- Employee Info -->
			<div>
				<p class="text-sm font-medium text-gray-900">{employee.name}</p>
				<p class="text-xs text-gray-500">{employee.role}</p>
			</div>
		</div>

		<!-- Availability Status -->
		<div class="flex items-center space-x-1">
			<svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
				<path fill-rule="evenodd" d={availabilityIcon} clip-rule="evenodd" />
			</svg>
			<span class="text-xs font-medium">{statusText}</span>
		</div>
	</div>

	<!-- Date -->
	<div class="mt-2">
		<p class="text-xs text-gray-600">
			{availabilityStatus.date.toLocaleDateString('en-US', {
				weekday: 'short',
				month: 'short',
				day: 'numeric'
			})}
		</p>
	</div>

	<!-- Unavailability Reasons -->
	{#if availabilityStatus.unavailabilityReasons.length > 0}
		<div class="mt-3 space-y-2">
			{#each availabilityStatus.unavailabilityReasons as reason}
				<div class="rounded-md p-2 {getUnavailabilityColor(reason.type)}">
					<div class="flex items-start justify-between">
						<div class="flex-1">
							<div class="flex items-center space-x-1">
								<span class="text-xs font-medium">
									{getReasonTypeLabel(reason.type)}
								</span>
								{#if reason.timeRange}
									<span class="text-xs opacity-75">
										• {formatTimeRange(reason.timeRange)}
									</span>
								{/if}
							</div>
							<p class="mt-1 text-xs">{reason.reason}</p>
							{#if reason.status}
								<span
									class="mt-1 inline-flex items-center rounded-full px-1.5 py-0.5 text-xs font-medium {reason.status ===
									'approved'
										? 'bg-green-100 text-green-800'
										: reason.status === 'pending'
											? 'bg-yellow-100 text-yellow-800'
											: 'bg-red-100 text-red-800'}"
								>
									{reason.status}
								</span>
							{/if}
						</div>
					</div>
				</div>
			{/each}
		</div>
	{/if}

	<!-- Leave Requests (if showing details) -->
	{#if showDetails && availabilityStatus.leaveRequests.length > 0}
		<div class="mt-3">
			<h4 class="mb-2 text-xs font-medium text-gray-700">Leave Requests</h4>
			<div class="space-y-1">
				{#each availabilityStatus.leaveRequests as leaveRequest}
					<button
						onclick={() => onLeaveRequestClick?.(leaveRequest)}
						class="w-full rounded-md p-2 text-left {getLeaveTypeColor(
							leaveRequest.requestType
						)} transition-opacity hover:opacity-80"
					>
						<div class="flex items-center justify-between">
							<div>
								<p class="text-xs font-medium capitalize">{leaveRequest.requestType}</p>
								<p class="text-xs opacity-75">
									{leaveRequest.startDate.toLocaleDateString()} - {leaveRequest.endDate.toLocaleDateString()}
								</p>
							</div>
							<span class="text-xs font-medium capitalize">{leaveRequest.status}</span>
						</div>
						{#if leaveRequest.reason}
							<p class="mt-1 text-xs opacity-75">{leaveRequest.reason}</p>
						{/if}
					</button>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Recurring Patterns (if showing details) -->
	{#if showDetails && availabilityStatus.recurringPatterns.length > 0}
		<div class="mt-3">
			<h4 class="mb-2 text-xs font-medium text-gray-700">Recurring Unavailability</h4>
			<div class="space-y-1">
				{#each availabilityStatus.recurringPatterns as pattern}
					<button
						onclick={() => onUnavailabilityClick?.(pattern)}
						class="w-full rounded-md bg-blue-100 p-2 text-left text-blue-800 transition-opacity hover:opacity-80"
					>
						<div class="flex items-center justify-between">
							<div>
								<p class="text-xs font-medium capitalize">
									Every {pattern.dayOfWeek}
								</p>
								{#if pattern.startTime && pattern.endTime}
									<p class="text-xs opacity-75">
										{pattern.startTime} - {pattern.endTime}
									</p>
								{:else}
									<p class="text-xs opacity-75">All day</p>
								{/if}
							</div>
						</div>
						{#if pattern.reason}
							<p class="mt-1 text-xs opacity-75">{pattern.reason}</p>
						{/if}
					</button>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Quick Actions (if available) -->
	{#if availabilityStatus.isAvailable}
		<div class="border-opacity-20 mt-3 border-t border-current pt-2">
			<div class="flex items-center justify-between text-xs">
				<span class="text-gray-600">Ready for scheduling</span>
				<svg class="h-3 w-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
					<path
						fill-rule="evenodd"
						d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
						clip-rule="evenodd"
					/>
				</svg>
			</div>
		</div>
	{:else}
		<div class="border-opacity-20 mt-3 border-t border-current pt-2">
			<div class="flex items-center justify-between text-xs">
				<span class="text-gray-600">
					{availabilityStatus.unavailabilityReasons.length} conflict{availabilityStatus
						.unavailabilityReasons.length !== 1
						? 's'
						: ''}
				</span>
				<svg class="h-3 w-3 text-red-600" fill="currentColor" viewBox="0 0 20 20">
					<path
						fill-rule="evenodd"
						d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
						clip-rule="evenodd"
					/>
				</svg>
			</div>
		</div>
	{/if}
</div>

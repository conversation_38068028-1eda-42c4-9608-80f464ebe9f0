<script lang="ts">
	import type { Employee } from '$lib/types.js';
	import { Button } from '$lib/components/ui/index.js';

	interface Props {
		isOpen: boolean;
		employees: Employee[];
		weekDates: Date[];
		onClose: () => void;
		onDeleteEmployee: (employeeId: string, startDate: string, endDate: string) => Promise<{ deletedCount: number; message: string }>;
		onDeleteDate: (date: string) => Promise<{ deletedCount: number; message: string }>;
		onDeleteWeek: (startDate: string, endDate: string) => Promise<{ deletedCount: number; message: string }>;
		getShiftCountForEmployee: (employeeId: string) => number;
		getShiftCountForDate: (date: Date) => number;
		getTotalShiftCountForWeek: () => number;
	}

	let {
		isOpen,
		employees,
		weekDates,
		onClose,
		onDeleteEmployee,
		onDeleteDate,
		onDeleteWeek,
		getShiftCountForEmployee,
		getShiftCountForDate,
		getTotalShiftCountForWeek
	}: Props = $props();

	// Modal state
	let deleteType = $state<'employee' | 'date' | 'week'>('employee');
	let selectedEmployeeId = $state('');
	let selectedDate = $state('');
	let isDeleting = $state(false);
	let errorMessage = $state('');

	// Get employees with shifts in current week
	let employeesWithShifts = $derived(
		employees.filter(emp => getShiftCountForEmployee(emp.id) > 0)
	);

	// Get dates with shifts
	let datesWithShifts = $derived(
		weekDates.filter(date => getShiftCountForDate(date) > 0)
	);

	// Format date with day name for dropdown display in dd/mm/yyyy format
	function formatDateWithDay(date: Date): string {
		const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

		const dayName = dayNames[date.getDay()];
		const dayNumber = date.getDate().toString().padStart(2, '0');
		const monthNumber = (date.getMonth() + 1).toString().padStart(2, '0');
		const year = date.getFullYear();

		return `${dayName} ${dayNumber}/${monthNumber}/${year}`;
	}

	// Calculate preview information
	let previewInfo = $derived.by(() => {
		if (deleteType === 'employee' && selectedEmployeeId) {
			const employee = employees.find(e => e.id === selectedEmployeeId);
			const shiftCount = getShiftCountForEmployee(selectedEmployeeId);
			return {
				type: 'employee',
				name: employee?.name || 'Unknown Employee',
				count: shiftCount,
				dateRange: `${weekDates[0].toLocaleDateString()} - ${weekDates[weekDates.length - 1].toLocaleDateString()}`
			};
		} else if (deleteType === 'date' && selectedDate) {
			const date = new Date(selectedDate);
			const shiftCount = getShiftCountForDate(date);
			return {
				type: 'date',
				name: date.toLocaleDateString(),
				count: shiftCount,
				dateRange: date.toLocaleDateString()
			};
		} else if (deleteType === 'week') {
			const shiftCount = getTotalShiftCountForWeek();
			return {
				type: 'week',
				name: 'entire week',
				count: shiftCount,
				dateRange: `${weekDates[0].toLocaleDateString()} - ${weekDates[weekDates.length - 1].toLocaleDateString()}`
			};
		}
		return null;
	});

	// Reset form when modal opens/closes
	$effect(() => {
		if (isOpen) {
			deleteType = 'employee';
			selectedEmployeeId = '';
			selectedDate = '';
			errorMessage = '';
		}
	});

	// Handle deletion
	async function handleDelete() {
		if (!previewInfo || previewInfo.count === 0) return;

		isDeleting = true;
		errorMessage = '';

		try {
			let result;
			if (deleteType === 'employee' && selectedEmployeeId) {
				const startDate = weekDates[0].toISOString().split('T')[0];
				const endDate = weekDates[weekDates.length - 1].toISOString().split('T')[0];
				result = await onDeleteEmployee(selectedEmployeeId, startDate, endDate);
			} else if (deleteType === 'date' && selectedDate) {
				result = await onDeleteDate(selectedDate);
			} else if (deleteType === 'week') {
				const startDate = weekDates[0].toISOString().split('T')[0];
				const endDate = weekDates[weekDates.length - 1].toISOString().split('T')[0];
				result = await onDeleteWeek(startDate, endDate);
			}

			// Show success message if available
			if (result && result.message) {
				alert(`Success: ${result.message}`);
			} else {
				alert('Shifts deleted successfully!');
			}

			// Close modal on success
			onClose();
		} catch (error) {
			console.error('❌ BulkDeleteModal: Delete failed:', error);
			errorMessage = error instanceof Error ? error.message : 'Failed to delete shifts';
		} finally {
			isDeleting = false;
		}
	}

	function handleClose() {
		if (!isDeleting) {
			onClose();
		}
	}
</script>

{#if isOpen}
	<!-- Modal backdrop -->
	<div 
		class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
		onclick={handleClose}
	>
		<!-- Modal content -->
		<div 
			class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6"
			onclick={(e) => e.stopPropagation()}
		>
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<h2 class="text-xl font-semibold text-gray-900">Bulk Delete Shifts</h2>
				<button
					onclick={handleClose}
					class="text-gray-400 hover:text-gray-600 transition-colors"
					disabled={isDeleting}
					aria-label="Close modal"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
					</svg>
				</button>
			</div>

			<!-- Delete type selection -->
			<div class="mb-6">
				<label class="block text-sm font-medium text-gray-700 mb-3">
					What would you like to delete?
				</label>
				
				<div class="space-y-3">
					<!-- Employee option -->
					<label class="flex items-center">
						<input
							type="radio"
							bind:group={deleteType}
							value="employee"
							class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
							disabled={isDeleting}
						/>
						<span class="ml-3 text-sm text-gray-900">
							Delete all shifts for an employee
						</span>
					</label>

					<!-- Date option -->
					<label class="flex items-center">
						<input
							type="radio"
							bind:group={deleteType}
							value="date"
							class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
							disabled={isDeleting}
						/>
						<span class="ml-3 text-sm text-gray-900">
							Delete all shifts for a specific date
						</span>
					</label>

					<!-- Week option -->
					<label class="flex items-center">
						<input
							type="radio"
							bind:group={deleteType}
							value="week"
							class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
							disabled={isDeleting}
						/>
						<span class="ml-3 text-sm text-gray-900">
							Delete all shifts for the entire week
						</span>
					</label>
				</div>
			</div>

			<!-- Selection based on type -->
			<div class="mb-6">
				{#if deleteType === 'employee'}
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Select Employee
					</label>
					<select
						bind:value={selectedEmployeeId}
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
						disabled={isDeleting}
					>
						<option value="">Choose an employee...</option>
						{#each employeesWithShifts as employee}
							<option value={employee.id}>
								{employee.name} ({getShiftCountForEmployee(employee.id)} shifts)
							</option>
						{/each}
					</select>
				{:else if deleteType === 'date'}
					<label class="block text-sm font-medium text-gray-700 mb-2">
						Select Date
					</label>
					<select
						bind:value={selectedDate}
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
						disabled={isDeleting}
					>
						<option value="">Choose a date...</option>
						{#each datesWithShifts as date}
							<option value={date.toISOString().split('T')[0]}>
								{formatDateWithDay(date)} ({getShiftCountForDate(date)} shifts)
							</option>
						{/each}
					</select>
				{/if}
			</div>

			<!-- Preview -->
			{#if previewInfo && previewInfo.count > 0}
				<div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
					<div class="flex items-start">
						<svg class="w-5 h-5 text-red-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
						</svg>
						<div>
							<h4 class="text-sm font-medium text-red-800">
								Deletion Preview
							</h4>
							<p class="text-sm text-red-700 mt-1">
								This will delete <strong>{previewInfo.count} shifts</strong> for 
								<strong>{previewInfo.name}</strong>
								{#if previewInfo.type === 'employee'}
									during {previewInfo.dateRange}
								{:else}
									on {previewInfo.dateRange}
								{/if}
							</p>
						</div>
					</div>
				</div>
			{/if}

			<!-- Error message -->
			{#if errorMessage}
				<div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
					<div class="flex items-start">
						<svg class="w-5 h-5 text-red-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
						</svg>
						<div>
							<h4 class="text-sm font-medium text-red-800">Error</h4>
							<p class="text-sm text-red-700 mt-1">{errorMessage}</p>
						</div>
					</div>
				</div>
			{/if}

			<!-- Actions -->
			<div class="flex justify-end space-x-3">
				<Button
					variant="secondary"
					onclick={handleClose}
					disabled={isDeleting}
				>
					Cancel
				</Button>
				
				<Button
					variant="danger"
					onclick={handleDelete}
					disabled={!previewInfo || previewInfo.count === 0 || isDeleting}
				>
					{#if isDeleting}
						<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
						</svg>
						Deleting...
					{:else}
						<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
						</svg>
						Delete {previewInfo?.count || 0} Shifts
					{/if}
				</Button>
			</div>
		</div>
	</div>
{/if}

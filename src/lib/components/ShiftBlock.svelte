<script lang="ts">
	import type { Shift, Venue, UIState } from '$lib/types.js';
	import { getShiftStatus } from '$lib/utils.js';

	interface Props {
		shift: Shift;
		venue?: Venue;
		uiState: UIState;
		onClick: () => void;
		onDelete?: (shiftId: string) => void;
		onDuplicate?: (shift: Shift) => void;
		onDragCreateComplete?: () => void;
		isUnassigned?: boolean;
	}

	let { shift, venue, uiState, onClick, onDelete, onDuplicate, onDragCreateComplete, isUnassigned = false }: Props = $props();

	// State for instant tooltips
	let showEditTooltip = $state(false);
	let showDeleteTooltip = $state(false);
	let showDuplicateTooltip = $state(false);

	// State for drag creation
	let isDragCreating = $state(false);

	// State for tooltip visibility management
	let isAnyTooltipVisible = $derived(showEditTooltip || showDeleteTooltip || showDuplicateTooltip);

	// Tooltip management functions
	function showTooltip(type: 'edit' | 'delete' | 'duplicate') {
		// Hide all tooltips first
		hideAllTooltips();
		// Show the requested tooltip
		switch (type) {
			case 'edit':
				showEditTooltip = true;
				break;
			case 'delete':
				showDeleteTooltip = true;
				break;
			case 'duplicate':
				showDuplicateTooltip = true;
				break;
		}
	}

	function hideAllTooltips() {
		showEditTooltip = false;
		showDeleteTooltip = false;
		showDuplicateTooltip = false;
	}



	// Computed values with safety checks
	let status = $derived(shift ? getShiftStatus(shift) : 'unassigned');

	// Track if we're in a drag operation to prevent click conflicts
	let isDragStarted = $state(false);
	let dragStartTime = $state(0);

	// Drag and drop handlers
	function handleDragStart(event: DragEvent) {
		if (!event.dataTransfer) return;

		isDragStarted = true;
		dragStartTime = Date.now();

		uiState.dragState = {
			isDragging: true,
			draggedShift: shift,
			draggedEmployee: null,
			dropTarget: null
		};

		event.dataTransfer.effectAllowed = 'move';
		event.dataTransfer.setData('text/plain', shift.id);

		// Add some visual feedback
		if (event.target instanceof HTMLElement) {
			event.target.style.opacity = '0.5';
		}


	}

	// Handle click events - prevent if we just finished dragging
	function handleClick(event: MouseEvent) {
		const timeSinceDrag = Date.now() - dragStartTime;

		// If we just finished dragging (within 100ms), ignore the click
		if (isDragStarted && timeSinceDrag < 100) {

			event.preventDefault();
			event.stopPropagation();
			return;
		}


		onClick();
	}

	// Handle delete button click
	function handleDelete(event: MouseEvent) {
		event.preventDefault();
		event.stopPropagation();

		if (onDelete) {
			const confirmDelete = confirm(`Are you sure you want to delete this shift (${formatTimeRange()})?`);
			if (confirmDelete) {

				onDelete(shift.id);
			}
		}
	}

	// Handle duplicate button click
	function handleDuplicate(event: MouseEvent) {
		event.preventDefault();
		event.stopPropagation();

		if (onDuplicate) {

			onDuplicate(shift);
		}
	}

	// Handle drag creation start from duplicate button
	function handleDuplicateDragStart(event: MouseEvent) {
		event.preventDefault();
		event.stopPropagation();

		if (!shift.employeeId) return;

		isDragCreating = true;

		// Initialize drag create state
		uiState.dragCreateState = {
			isActive: true,
			mode: null, // Will be determined by drag direction
			direction: null,
			sourceEmployee: null, // Will be set by parent component
			sourceDate: shift.date,
			sourceShift: shift,
			targetDates: [],
			targetEmployees: [],
			previewCells: []
		};



		// Add global mouse event listeners
		document.addEventListener('mousemove', handleDragCreateMove);
		document.addEventListener('mouseup', handleDragCreateEnd);
		document.body.classList.add('drag-create-active');
	}

	// Handle drag creation mouse move
	function handleDragCreateMove(event: MouseEvent) {
		if (!isDragCreating || !uiState.dragCreateState.isActive) return;

		// Find the cell under the mouse
		const element = document.elementFromPoint(event.clientX, event.clientY);
		const cell = element?.closest('[data-schedule-cell]');

		if (cell) {
			const employeeId = cell.getAttribute('data-employee-id');
			const dateStr = cell.getAttribute('data-date');

			if (employeeId && dateStr) {
				const targetDate = new Date(dateStr);
				const sourceDate = uiState.dragCreateState.sourceDate;

				if (!sourceDate) return;

				// Determine drag direction and mode
				const isSameEmployee = employeeId === shift.employeeId;
				const isSameDate = targetDate.toDateString() === sourceDate.toDateString();

				if (isSameEmployee && !isSameDate) {
					// Horizontal drag (same employee, different dates)
					uiState.dragCreateState.direction = 'horizontal';
					uiState.dragCreateState.mode = 'duplicate-shift-horizontal';

					const startDate = new Date(Math.min(sourceDate.getTime(), targetDate.getTime()));
					const endDate = new Date(Math.max(sourceDate.getTime(), targetDate.getTime()));

					const targetDates: Date[] = [];
					const previewCells: { employeeId: string; date: Date }[] = [];

					for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
						const currentDate = new Date(d);
						if (currentDate.getTime() !== sourceDate.getTime()) { // Exclude source date
							targetDates.push(currentDate);
							previewCells.push({ employeeId, date: currentDate });
						}
					}

					uiState.dragCreateState.targetDates = targetDates;
					uiState.dragCreateState.previewCells = previewCells;

				} else if (!isSameEmployee && isSameDate) {
					// Vertical drag (different employees, same date)
					uiState.dragCreateState.direction = 'vertical';
					uiState.dragCreateState.mode = 'duplicate-shift-vertical';

					// This would need access to all employees from the parent component
					// For now, we'll handle this in the ScheduleGrid component

				}
			}
		}
	}

	// Handle drag creation end
	function handleDragCreateEnd(_event: MouseEvent) {
		if (!isDragCreating || !uiState.dragCreateState.isActive) return;



		// Clean up event listeners
		document.removeEventListener('mousemove', handleDragCreateMove);
		document.removeEventListener('mouseup', handleDragCreateEnd);
		document.body.classList.remove('drag-create-active');

		// Trigger creation if we have valid targets
		const hasTargets = uiState.dragCreateState.targetDates.length > 0 || uiState.dragCreateState.targetEmployees.length > 0;

		if (hasTargets) {

			onDragCreateComplete?.();
		} else {
			// Reset state if no valid drag occurred
			isDragCreating = false;
			uiState.dragCreateState = {
				isActive: false,
				mode: null,
				direction: null,
				sourceEmployee: null,
				sourceDate: null,
				sourceShift: null,
				targetDates: [],
				targetEmployees: [],
				previewCells: []
			};
		}
	}

	function handleDragEnd(event: DragEvent) {

		// Reset visual feedback
		if (event.target instanceof HTMLElement) {
			event.target.style.opacity = '1';
		}

		// Reset drag state if not dropped successfully
		if (uiState.dragState.isDragging && !uiState.dragState.dropTarget) {
			uiState.dragState = {
				isDragging: false,
				draggedShift: null,
				draggedEmployee: null,
				dropTarget: null
			};
		}

		// Reset drag tracking after a short delay
		setTimeout(() => {
			isDragStarted = false;
		}, 150);
	}

	// Format time range with safety checks - HH:MM format only
	function formatTimeRange(): string {
		if (!shift?.startTime || !shift?.endTime) {
			console.warn('⚠️ ShiftBlock: Missing time data for shift:', shift?.id);
			return 'No time set';
		}

		// Convert HH:MM:SS to HH:MM format if needed
		const formatTime = (time: string): string => {
			if (time.includes(':')) {
				const parts = time.split(':');
				return `${parts[0]}:${parts[1]}`;
			}
			return time;
		};

		return `${formatTime(shift.startTime)} - ${formatTime(shift.endTime)}`;
	}

	// Get appropriate text color based on background
	function getTextColor(): string {
		if (status === 'paid') return 'text-green-800';
		if (status === 'scheduled') return 'text-blue-800';
		return 'text-gray-700';
	}

	// Get vertical status indicator color
	function getStatusIndicatorColor(): string {
		if (isUnassigned) return 'bg-red-500';
		if (status === 'paid') return 'bg-green-500';
		if (status === 'scheduled') return 'bg-blue-500';
		return 'bg-gray-400';
	}

	// Get border and background colors based on status - Modern design
	function getBorderColor(): string {
		if (status === 'paid') return 'border-green-400';
		if (status === 'scheduled') return 'border-blue-400';
		if (isUnassigned) return 'border-red-400';
		return 'border-gray-300';
	}

	function getBackgroundColor(): string {
		if (status === 'paid') return 'bg-green-50';
		if (status === 'scheduled') return 'bg-blue-50';
		if (isUnassigned) return 'bg-red-50';
		return 'bg-gray-50';
	}
</script>

{#if shift && shift.id}
	<div
		class="shift-block relative group cursor-pointer transition-all duration-200 hover:shadow-md
			{getBackgroundColor()} border {getBorderColor()} rounded p-1.5 text-xs
			{uiState.dragState.isDragging && uiState.dragState.draggedShift?.id === shift.id ? 'opacity-50' : ''}
			{isAnyTooltipVisible ? 'tooltip-active' : ''}
			w-full h-[38px] min-h-[38px] max-h-[38px] shadow-sm box-border"
		draggable="true"
		ondragstart={handleDragStart}
		ondragend={handleDragEnd}
		onclick={handleClick}
		onmouseleave={hideAllTooltips}
		role="button"
		tabindex="0"
		onkeydown={(e) => e.key === 'Enter' && handleClick(e as any)}
	>


		<!-- Vertical status indicator -->
		<div class="absolute left-0 top-0 bottom-0 w-1 rounded-l-lg {getStatusIndicatorColor()}"></div>

		<!-- Shift content - Compact layout optimized for 38px height -->
		<div class="flex flex-col justify-center h-full ml-1 px-0.5 overflow-hidden">
			<!-- Top row: Venue/location name prominently displayed -->
			<div class="flex items-center justify-between mb-0.5 min-h-0">
				<span class="font-semibold {getTextColor()} text-[10px] leading-tight truncate mr-0.5" title={venue?.name || 'Unknown Venue'}>
					{venue?.name || 'Unknown Venue'}
				</span>
				{#if shift.isPaid}
					<div class="flex-shrink-0">
						<svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
						</svg>
					</div>
				{/if}
			</div>

			<!-- Bottom row: Time range only -->
			<div class="flex items-center min-h-0">
				<span class="text-gray-600 text-[10px] leading-tight font-medium">
					{formatTimeRange()}
				</span>
			</div>
		</div>

		<!-- Action Icons on Hover - Compact layout for smaller container -->
		<div class="absolute inset-0 bg-white bg-opacity-95 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center space-x-1 z-20">
			<!-- Edit Action -->
			<div class="relative tooltip-container">
				<button
					onclick={handleClick}
					onmouseenter={() => showTooltip('edit')}
					class="w-6 h-6 bg-blue-100 hover:bg-blue-200 rounded border border-blue-300 flex items-center justify-center transition-colors duration-150"
					aria-label="Edit shift"
					title="Edit shift"
				>
					<svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
					</svg>
				</button>
				{#if showEditTooltip}
					<div class="shift-action-tooltip show">Edit shift</div>
				{/if}
			</div>

			<!-- Delete Action -->
			{#if onDelete}
				<div class="relative tooltip-container">
					<button
						onclick={handleDelete}
						onmouseenter={() => showTooltip('delete')}
						class="w-6 h-6 bg-red-100 hover:bg-red-200 rounded border border-red-300 flex items-center justify-center transition-colors duration-150"
						aria-label="Delete shift"
						title="Delete shift"
					>
						<svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
						</svg>
					</button>
					{#if showDeleteTooltip}
						<div class="shift-action-tooltip show">Delete shift</div>
					{/if}
				</div>
			{/if}

			<!-- Duplicate Action with Drag Creation -->
			{#if onDuplicate}
				<div class="relative tooltip-container">
					<button
						onclick={handleDuplicate}
						onmousedown={handleDuplicateDragStart}
						onmouseenter={() => showTooltip('duplicate')}
						class="w-6 h-6 bg-green-100 hover:bg-green-200 rounded border border-green-300 flex items-center justify-center transition-colors duration-150 {isDragCreating ? 'drag-create-cursor' : ''}"
						aria-label="Duplicate shift or drag to create multiple"
						title={isDragCreating ? 'Drag to create multiple' : 'Duplicate shift'}
					>
						<svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
						</svg>
					</button>
					{#if showDuplicateTooltip}
						<div class="shift-action-tooltip show">
							{isDragCreating ? 'Drag to create multiple' : 'Duplicate shift'}
						</div>
					{/if}
				</div>
			{/if}
		</div>


	</div>
{:else}
	<!-- Fallback for invalid shift data -->
	<div class="w-full h-[38px] min-h-[38px] max-h-[38px] bg-red-100 border border-red-300 rounded p-1.5 flex items-center justify-center box-border">
		<span class="text-red-600 text-[10px] font-medium">Invalid shift data</span>
	</div>
{/if}

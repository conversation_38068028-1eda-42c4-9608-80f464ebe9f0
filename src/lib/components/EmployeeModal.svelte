<script lang="ts">
	import type { Employee, EmployeeFormData } from '$lib/types.js';
	import { validateEmployee, generateId } from '$lib/utils.js';

	interface Props {
		employee: Employee | null;
		mode: 'create' | 'edit';
		onSave: (employee: Employee) => void;
		onDelete: (employeeId: string) => void;
		onClose: () => void;
	}

	let { employee, mode, onSave, onDelete, onClose }: Props = $props();

	// Form data
	let formData: EmployeeFormData = $state({
		name: employee?.name || '',
		defaultDailyRate: employee?.defaultDailyRate || 100,
		role: employee?.role || '',
		isActive: employee?.isActive ?? true
	});

	// Validation
	let errors = $state<Record<string, string>>({});
	let isSubmitting = $state(false);

	// Common roles for quick selection
	const commonRoles = [
		'Server',
		'Manager',
		'Kitchen Staff',
		'Bartender',
		'Host/Hostess',
		'Cashier',
		'Cook',
		'Dishwasher',
		'Supervisor',
		'Assistant Manager'
	];

	function handleSubmit(event?: SubmitEvent | MouseEvent) {
		event?.preventDefault();
		// Validate form - convert EmployeeFormData to partial Employee for validation
		const employeeForValidation = {
			name: formData.name,
			defaultDailyRate: formData.defaultDailyRate,
			role: formData.role as any // Allow any role for validation, will be mapped later
		};
		const validation = validateEmployee(employeeForValidation);

		if (!validation.isValid) {
			errors = validation.errors.reduce(
				(acc, error) => {
					acc[error.field] = error.message;
					return acc;
				},
				{} as Record<string, string>
			);
			return;
		}

		errors = {};
		isSubmitting = true;

		try {
			const employeeData: Employee = {
				id: employee?.id || generateId(),
				organizationId: employee?.organizationId || '',
				fullName: formData.name.trim(),
				name: formData.name.trim(),
				defaultDailyRate: formData.defaultDailyRate,
				defaultDailyWorkingHours: employee?.defaultDailyWorkingHours || 8,
				defaultHourlyRate: employee?.defaultHourlyRate || formData.defaultDailyRate / 8,
				role: formData.role.trim() as 'FOH' | 'BOH' | 'DELIVERY' | 'OTHER',
				active: formData.isActive,
				isActive: formData.isActive,
				createdAt: employee?.createdAt || new Date(),
				updatedAt: new Date()
			};

			onSave(employeeData);
		} finally {
			isSubmitting = false;
		}
	}

	function handleDelete() {
		if (
			employee &&
			confirm(
				`Are you sure you want to delete ${employee.name}? This will unassign all their shifts.`
			)
		) {
			onDelete(employee.id);
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			onClose();
		}
	}

	function selectRole(role: string) {
		formData.role = role;
	}
</script>

<svelte:window onkeydown={handleKeydown} />

<!-- Modal Backdrop -->
<div class="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black p-4">
	<div class="max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg bg-white shadow-xl">
		<!-- Modal Header -->
		<div class="flex items-center justify-between border-b border-gray-200 p-6">
			<h2 class="text-lg font-semibold text-gray-900">
				{mode === 'create' ? 'Add Employee' : 'Edit Employee'}
			</h2>
			<button
				onclick={onClose}
				class="text-gray-400 transition-colors hover:text-gray-600"
				aria-label="Close modal"
			>
				<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M6 18L18 6M6 6l12 12"
					/>
				</svg>
			</button>
		</div>

		<!-- Modal Body -->
		<form onsubmit={handleSubmit} class="space-y-4 p-6">
			<!-- Name -->
			<div>
				<label for="employee-name" class="mb-1 block text-sm font-medium text-gray-700"
					>Full Name *</label
				>
				<input
					id="employee-name"
					type="text"
					bind:value={formData.name}
					placeholder="Enter employee's full name"
					class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none
						{errors.name ? 'border-red-300' : ''}"
				/>
				{#if errors.name}
					<p class="mt-1 text-sm text-red-600">{errors.name}</p>
				{/if}
			</div>

			<!-- Role -->
			<div>
				<label for="employee-role" class="mb-1 block text-sm font-medium text-gray-700"
					>Role *</label
				>
				<input
					id="employee-role"
					type="text"
					bind:value={formData.role}
					placeholder="Enter job role"
					class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none
						{errors.role ? 'border-red-300' : ''}"
				/>
				{#if errors.role}
					<p class="mt-1 text-sm text-red-600">{errors.role}</p>
				{/if}

				<!-- Quick Role Selection -->
				<div class="mt-2">
					<p class="mb-2 text-xs text-gray-500">Quick select:</p>
					<div class="flex flex-wrap gap-1">
						{#each commonRoles as role}
							<button
								type="button"
								onclick={() => selectRole(role)}
								class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-700 transition-colors hover:bg-gray-200
									{formData.role === role ? 'bg-blue-100 text-blue-700' : ''}"
							>
								{role}
							</button>
						{/each}
					</div>
				</div>
			</div>

			<!-- Default Daily Rate -->
			<div>
				<label for="employee-rate" class="mb-1 block text-sm font-medium text-gray-700"
					>Default Daily Rate *</label
				>
				<div class="relative">
					<span class="absolute top-2 left-3 text-gray-500">лв</span>
					<input
						id="employee-rate"
						type="number"
						step="0.01"
						min="0"
						bind:value={formData.defaultDailyRate}
						class="w-full rounded-md border border-gray-300 py-2 pr-3 pl-8 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none
							{errors.defaultDailyRate ? 'border-red-300' : ''}"
					/>
				</div>
				{#if errors.defaultDailyRate}
					<p class="mt-1 text-sm text-red-600">{errors.defaultDailyRate}</p>
				{/if}
				<p class="mt-1 text-xs text-gray-500">
					This rate will be used as default when creating new shifts
				</p>
			</div>

			<!-- Active Status -->
			<div class="flex items-center">
				<input
					type="checkbox"
					id="isActive"
					bind:checked={formData.isActive}
					class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
				/>
				<label for="isActive" class="ml-2 block text-sm text-gray-700"> Active employee </label>
			</div>
			<p class="text-xs text-gray-500">
				Inactive employees won't appear in the main schedule but can still be edited
			</p>

			{#if mode === 'edit' && employee}
				<!-- Employee Stats -->
				<div class="rounded-md bg-gray-50 p-4">
					<h3 class="mb-2 text-sm font-medium text-gray-900">Employee Information</h3>
					<div class="space-y-1 text-sm">
						<div class="flex justify-between">
							<span class="text-gray-600">Created:</span>
							<span>{employee.createdAt.toLocaleDateString()}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-600">Last Updated:</span>
							<span>{employee.updatedAt.toLocaleDateString()}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-600">Status:</span>
							<span class={employee.isActive ? 'text-green-600' : 'text-red-600'}>
								{employee.isActive ? 'Active' : 'Inactive'}
							</span>
						</div>
					</div>
				</div>
			{/if}
		</form>

		<!-- Modal Footer -->
		<div class="flex items-center justify-between border-t border-gray-200 p-6">
			<div>
				{#if mode === 'edit' && employee}
					<button
						onclick={handleDelete}
						class="rounded-md px-4 py-2 text-red-600 transition-colors hover:bg-red-50"
					>
						Delete Employee
					</button>
				{/if}
			</div>

			<div class="flex items-center space-x-3">
				<button
					onclick={onClose}
					class="rounded-md px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50"
				>
					Cancel
				</button>
				<button
					onclick={handleSubmit}
					disabled={isSubmitting}
					class="rounded-md bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700 disabled:opacity-50"
				>
					{isSubmitting ? 'Saving...' : mode === 'create' ? 'Add Employee' : 'Save Changes'}
				</button>
			</div>
		</div>
	</div>
</div>

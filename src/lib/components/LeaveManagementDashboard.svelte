<script lang="ts">
	/**
	 * Leave Management Dashboard Component
	 * Comprehensive leave request management with filtering and approval workflows
	 * Following Code Complete principles: Clear data organization, efficient workflows
	 */

	import { createEventDispatcher, onMount } from 'svelte';
	import { leaveService } from '$lib/services/leaveService.js';
	import LeaveRequestModal from './LeaveRequestModal.svelte';
	import type { 
		LeaveRequest, 
		LeaveRequestStatus, 
		LeaveRequestType,
		LeaveManagementFilters,
		Employee 
	} from '$lib/types.js';

	interface Props {
		organizationId: string;
		employees: Employee[];
		currentUserId: string;
		userRole: 'viewer' | 'editor' | 'admin' | 'superadmin';
	}

	let { organizationId, employees, currentUserId, userRole }: Props = $props();

	const dispatch = createEventDispatcher<{
		refresh: void;
		conflictDetected: { leaveRequest: LeaveRequest };
	}>();

	// Data state
	let leaveRequests: LeaveRequest[] = $state([]);
	let filteredRequests: LeaveRequest[] = $state([]);

	// UI state
	let isLoading = $state(false);
	let selectedRequest: LeaveRequest | null = $state(null);
	let showModal = $state(false);
	let modalMode: 'create' | 'edit' | 'approve' = $state('create');

	// Filter state
	let filters: LeaveManagementFilters = $state({
		status: undefined,
		requestType: undefined,
		employeeId: undefined,
		dateRange: undefined
	});

	let statusFilter = $state<LeaveRequestStatus | 'all'>('all');
	let typeFilter = $state<LeaveRequestType | 'all'>('all');
	let employeeFilter = $state<string | 'all'>('all');

	// Computed
	let canManageRequests = $derived(userRole === 'editor' || userRole === 'admin' || userRole === 'superadmin');
	let pendingRequestsCount = $derived(leaveRequests.filter(lr => lr.status === 'pending').length);
	let myRequestsCount = $derived(leaveRequests.filter(lr => lr.requestedBy === currentUserId).length);

	let statusOptions: { value: LeaveRequestStatus | 'all'; label: string; color: string }[] = [
		{ value: 'all', label: 'All Status', color: 'bg-gray-100 text-gray-800' },
		{ value: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
		{ value: 'approved', label: 'Approved', color: 'bg-green-100 text-green-800' },
		{ value: 'denied', label: 'Denied', color: 'bg-red-100 text-red-800' },
		{ value: 'cancelled', label: 'Cancelled', color: 'bg-gray-100 text-gray-800' }
	];

	let typeOptions: { value: LeaveRequestType | 'all'; label: string; color: string }[] = [
		{ value: 'all', label: 'All Types', color: 'bg-gray-100 text-gray-800' },
		{ value: 'vacation', label: 'Vacation', color: 'bg-yellow-100 text-yellow-800' },
		{ value: 'sick', label: 'Sick Leave', color: 'bg-red-100 text-red-800' },
		{ value: 'personal', label: 'Personal', color: 'bg-blue-100 text-blue-800' },
		{ value: 'emergency', label: 'Emergency', color: 'bg-purple-100 text-purple-800' },
		{ value: 'other', label: 'Other', color: 'bg-gray-100 text-gray-800' }
	];

	/**
	 * Load leave requests on component mount
	 * Following Code Complete: Clear initialization
	 */
	onMount(() => {
		loadLeaveRequests();
	});

	/**
	 * Load leave requests from service
	 * Following Code Complete: Clear data loading with error handling
	 */
	async function loadLeaveRequests() {
		isLoading = true;

		try {
			leaveRequests = await leaveService.fetchLeaveRequests(
				organizationId,
				filters.employeeId,
				filters.status?.[0], // Take first status if array
				filters.dateRange?.start,
				filters.dateRange?.end
			);

			applyFilters();

		} catch (error) {
			console.error('❌ LeaveManagementDashboard: Failed to load leave requests:', error);
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Apply client-side filters
	 * Following Code Complete: Clear filtering logic
	 */
	function applyFilters() {
		filteredRequests = leaveRequests.filter(request => {
			// Status filter
			if (statusFilter !== 'all' && request.status !== statusFilter) {
				return false;
			}

			// Type filter
			if (typeFilter !== 'all' && request.requestType !== typeFilter) {
				return false;
			}

			// Employee filter
			if (employeeFilter !== 'all' && request.employeeId !== employeeFilter) {
				return false;
			}

			return true;
		});
	}

	/**
	 * Handle filter changes
	 * Following Code Complete: Reactive filter updates
	 */
	function handleFilterChange() {
		applyFilters();
	}

	/**
	 * Open modal for creating new leave request
	 * Following Code Complete: Clear modal state management
	 */
	function openCreateModal() {
		selectedRequest = null;
		modalMode = 'create';
		showModal = true;
	}

	/**
	 * Open modal for editing leave request
	 * Following Code Complete: Clear modal state management
	 */
	function openEditModal(request: LeaveRequest) {
		selectedRequest = request;
		modalMode = 'edit';
		showModal = true;
	}

	/**
	 * Open modal for approving leave request
	 * Following Code Complete: Clear modal state management
	 */
	function openApprovalModal(request: LeaveRequest) {
		selectedRequest = request;
		modalMode = 'approve';
		showModal = true;
	}

	/**
	 * Handle modal close
	 * Following Code Complete: Clear state cleanup
	 */
	function handleModalClose() {
		showModal = false;
		selectedRequest = null;
	}

	/**
	 * Handle leave request save
	 * Following Code Complete: Clear success handling
	 */
	function handleLeaveRequestSave(newRequest: LeaveRequest) {
		// Add to list if new, update if existing
		const existingIndex = leaveRequests.findIndex(lr => lr.id === newRequest.id);
		if (existingIndex >= 0) {
			leaveRequests[existingIndex] = newRequest;
		} else {
			leaveRequests = [newRequest, ...leaveRequests];
		}

		applyFilters();
		dispatch('refresh');
	}

	/**
	 * Handle leave request approval
	 * Following Code Complete: Clear approval handling
	 */
	function handleLeaveRequestApproval(data: { requestId: string; status: 'approved' | 'denied'; notes?: string }) {
		const { requestId, status } = data;

		// Update the request in the list
		const requestIndex = leaveRequests.findIndex(lr => lr.id === requestId);
		if (requestIndex >= 0) {
			leaveRequests[requestIndex] = {
				...leaveRequests[requestIndex],
				status,
				approvedAt: new Date(),
				approvedBy: currentUserId
			};
		}

		applyFilters();
		dispatch('refresh');

		// Check for scheduling conflicts if approved
		if (status === 'approved') {
			const request = leaveRequests[requestIndex];
			if (request) {
				dispatch('conflictDetected', { leaveRequest: request });
			}
		}
	}

	/**
	 * Handle leave request deletion
	 * Following Code Complete: Clear deletion handling
	 */
	function handleLeaveRequestDelete(requestId: string) {
		leaveRequests = leaveRequests.filter(lr => lr.id !== requestId);
		applyFilters();
		dispatch('refresh');
	}

	/**
	 * Calculate leave duration in days
	 * Following Code Complete: Pure function, clear calculation
	 */
	function calculateLeaveDays(request: LeaveRequest): number {
		const diffTime = Math.abs(request.endDate.getTime() - request.startDate.getTime());
		return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
	}

	/**
	 * Get employee name by ID
	 * Following Code Complete: Helper function with fallback
	 */
	function getEmployeeName(employeeId: string): string {
		const employee = employees.find(emp => emp.id === employeeId);
		return employee?.name || 'Unknown Employee';
	}

	/**
	 * Get status color class
	 * Following Code Complete: Clear color mapping
	 */
	function getStatusColor(status: LeaveRequestStatus): string {
		const option = statusOptions.find(opt => opt.value === status);
		return option?.color || 'bg-gray-100 text-gray-800';
	}

	/**
	 * Get type color class
	 * Following Code Complete: Clear color mapping
	 */
	function getTypeColor(type: LeaveRequestType): string {
		const option = typeOptions.find(opt => opt.value === type);
		return option?.color || 'bg-gray-100 text-gray-800';
	}

	// Watch filter changes
	$effect(() => {
		handleFilterChange();
	});
</script>

<!-- Dashboard Header -->
<div class="mb-6">
	<div class="flex items-center justify-between">
		<div>
			<h2 class="text-2xl font-bold text-gray-900">Leave Management</h2>
			<p class="mt-1 text-sm text-gray-500">
				Manage employee leave requests and unavailability
			</p>
		</div>

		<div class="flex space-x-3">
			<button
				onclick={loadLeaveRequests}
				disabled={isLoading}
				class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
			>
				<svg class="mr-2 h-4 w-4 {isLoading ? 'animate-spin' : ''}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
				</svg>
				Refresh
			</button>

			<button
				onclick={openCreateModal}
				class="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
			>
				<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
				</svg>
				Request Leave
			</button>
		</div>
	</div>

	<!-- Summary Cards -->
	<div class="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
		<div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
			<div class="flex items-center">
				<div class="flex-shrink-0">
					<svg class="h-8 w-8 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-5 w-0 flex-1">
					<dl>
						<dt class="text-sm font-medium text-gray-500 truncate">Pending Requests</dt>
						<dd class="text-lg font-medium text-gray-900">{pendingRequestsCount}</dd>
					</dl>
				</div>
			</div>
		</div>

		<div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
			<div class="flex items-center">
				<div class="flex-shrink-0">
					<svg class="h-8 w-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-5 w-0 flex-1">
					<dl>
						<dt class="text-sm font-medium text-gray-500 truncate">My Requests</dt>
						<dd class="text-lg font-medium text-gray-900">{myRequestsCount}</dd>
					</dl>
				</div>
			</div>
		</div>

		<div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
			<div class="flex items-center">
				<div class="flex-shrink-0">
					<svg class="h-8 w-8 text-green-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-5 w-0 flex-1">
					<dl>
						<dt class="text-sm font-medium text-gray-500 truncate">Total Requests</dt>
						<dd class="text-lg font-medium text-gray-900">{leaveRequests.length}</dd>
					</dl>
				</div>
			</div>
		</div>

		<div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
			<div class="flex items-center">
				<div class="flex-shrink-0">
					<svg class="h-8 w-8 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-5 w-0 flex-1">
					<dl>
						<dt class="text-sm font-medium text-gray-500 truncate">Filtered Results</dt>
						<dd class="text-lg font-medium text-gray-900">{filteredRequests.length}</dd>
					</dl>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Filters -->
<div class="mb-6 rounded-lg bg-white p-4 shadow">
	<h3 class="text-lg font-medium text-gray-900 mb-4">Filters</h3>
	
	<div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
		<!-- Status Filter -->
		<div>
			<label for="statusFilter" class="block text-sm font-medium text-gray-700">Status</label>
			<select
				id="statusFilter"
				bind:value={statusFilter}
				class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
			>
				{#each statusOptions as option}
					<option value={option.value}>{option.label}</option>
				{/each}
			</select>
		</div>

		<!-- Type Filter -->
		<div>
			<label for="typeFilter" class="block text-sm font-medium text-gray-700">Type</label>
			<select
				id="typeFilter"
				bind:value={typeFilter}
				class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
			>
				{#each typeOptions as option}
					<option value={option.value}>{option.label}</option>
				{/each}
			</select>
		</div>

		<!-- Employee Filter -->
		<div>
			<label for="employeeFilter" class="block text-sm font-medium text-gray-700">Employee</label>
			<select
				id="employeeFilter"
				bind:value={employeeFilter}
				class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
			>
				<option value="all">All Employees</option>
				{#each employees as employee}
					<option value={employee.id}>{employee.name}</option>
				{/each}
			</select>
		</div>

		<!-- Clear Filters -->
		<div class="flex items-end">
			<button
				onclick={() => {
					statusFilter = 'all';
					typeFilter = 'all';
					employeeFilter = 'all';
				}}
				class="w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
			>
				Clear Filters
			</button>
		</div>
	</div>
</div>

<!-- Leave Requests Table -->
<div class="overflow-hidden bg-white shadow sm:rounded-md">
	<div class="px-4 py-5 sm:p-6">
		<h3 class="text-lg font-medium text-gray-900 mb-4">
			Leave Requests ({filteredRequests.length})
		</h3>

		{#if isLoading}
			<div class="flex justify-center py-8">
				<svg class="h-8 w-8 animate-spin text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
				</svg>
			</div>
		{:else if filteredRequests.length === 0}
			<div class="text-center py-8">
				<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
				</svg>
				<h3 class="mt-2 text-sm font-medium text-gray-900">No leave requests</h3>
				<p class="mt-1 text-sm text-gray-500">Get started by creating a new leave request.</p>
				<div class="mt-6">
					<button
						onclick={openCreateModal}
						class="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
					>
						<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
						</svg>
						Request Leave
					</button>
				</div>
			</div>
		{:else}
			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Employee
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Type
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Dates
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Duration
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Status
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Requested
							</th>
							<th class="relative px-6 py-3">
								<span class="sr-only">Actions</span>
							</th>
						</tr>
					</thead>
					<tbody class="bg-white divide-y divide-gray-200">
						{#each filteredRequests as request}
							<tr class="hover:bg-gray-50">
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="flex items-center">
										<div class="flex-shrink-0 h-8 w-8">
											<div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
												<span class="text-sm font-medium text-blue-700">
													{getEmployeeName(request.employeeId).split(' ').map(n => n[0]).join('')}
												</span>
											</div>
										</div>
										<div class="ml-4">
											<div class="text-sm font-medium text-gray-900">
												{getEmployeeName(request.employeeId)}
											</div>
										</div>
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {getTypeColor(request.requestType)}">
										{request.requestType}
									</span>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									<div>
										{request.startDate.toLocaleDateString()} - {request.endDate.toLocaleDateString()}
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									{calculateLeaveDays(request)} day{calculateLeaveDays(request) !== 1 ? 's' : ''}
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {getStatusColor(request.status)}">
										{request.status}
									</span>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
									{request.createdAt.toLocaleDateString()}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
									<div class="flex space-x-2">
										{#if request.status === 'pending' && canManageRequests}
											<button
												onclick={() => openApprovalModal(request)}
												class="text-blue-600 hover:text-blue-900"
											>
												Review
											</button>
										{/if}
										
										{#if request.status === 'pending' && (request.requestedBy === currentUserId || canManageRequests)}
											<button
												onclick={() => openEditModal(request)}
												class="text-gray-600 hover:text-gray-900"
											>
												Edit
											</button>
										{/if}
									</div>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
		{/if}
	</div>
</div>

<!-- Leave Request Modal -->
{#if showModal}
	<LeaveRequestModal
		leaveRequest={selectedRequest}
		{employees}
		{currentUserId}
		{organizationId}
		mode={modalMode}
		onClose={handleModalClose}
		onSave={handleLeaveRequestSave}
		onApprove={handleLeaveRequestApproval}
		onDelete={handleLeaveRequestDelete}
	/>
{/if}

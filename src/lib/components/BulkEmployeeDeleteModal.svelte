<script lang="ts">
	import type { Employee } from '$lib/types.js';
	import { Button } from '$lib/components/ui/index.js';

	interface Props {
		isOpen: boolean;
		employees: Employee[];
		onClose: () => void;
		onBulkDelete: (employeeIds: string[]) => Promise<{ deletedCount: number; message: string }>;
	}

	let { isOpen, employees, onClose, onBulkDelete }: Props = $props();

	// State
	let selectedEmployeeIds = $state<string[]>([]);
	let isDeleting = $state(false);
	let showConfirmation = $state(false);

	// Computed
	let selectedEmployees = $derived(
		employees.filter(emp => selectedEmployeeIds.includes(emp.id))
	);

	// Functions
	function toggleEmployeeSelection(employeeId: string) {
		if (selectedEmployeeIds.includes(employeeId)) {
			selectedEmployeeIds = selectedEmployeeIds.filter(id => id !== employeeId);
		} else {
			selectedEmployeeIds = [...selectedEmployeeIds, employeeId];
		}
	}

	function selectAllEmployees() {
		selectedEmployeeIds = employees.map(emp => emp.id);
	}

	function clearSelection() {
		selectedEmployeeIds = [];
	}

	function proceedToConfirmation() {
		if (selectedEmployeeIds.length === 0) {
			alert('Please select at least one employee to delete.');
			return;
		}
		showConfirmation = true;
	}

	function cancelConfirmation() {
		showConfirmation = false;
	}

	async function confirmBulkDelete() {
		if (selectedEmployeeIds.length === 0) return;

		try {
			isDeleting = true;
			const result = await onBulkDelete(selectedEmployeeIds);
			
			// Show success message
			alert(`Success: ${result.message}`);
			
			// Reset state and close modal
			selectedEmployeeIds = [];
			showConfirmation = false;
			onClose();
		} catch (error) {
			console.error('❌ BulkEmployeeDeleteModal: Failed to delete employees:', error);
			alert(`Failed to delete employees: ${error instanceof Error ? error.message : 'Unknown error'}`);
		} finally {
			isDeleting = false;
		}
	}

	function handleClose() {
		if (!isDeleting) {
			selectedEmployeeIds = [];
			showConfirmation = false;
			onClose();
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && !isDeleting) {
			handleClose();
		}
	}
</script>

<svelte:window onkeydown={handleKeydown} />

{#if isOpen}
	<!-- Modal Backdrop -->
	<div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
		<div class="max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-white shadow-xl">
			<!-- Modal Header -->
			<div class="flex items-center justify-between border-b border-gray-200 p-6">
				<h2 class="text-lg font-semibold text-gray-900">
					{showConfirmation ? 'Confirm Bulk Delete' : 'Bulk Delete Employees'}
				</h2>
				<button
					onclick={handleClose}
					class="text-gray-400 transition-colors hover:text-gray-600"
					aria-label="Close modal"
					disabled={isDeleting}
				>
					<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M6 18L18 6M6 6l12 12"
						/>
					</svg>
				</button>
			</div>

			<!-- Modal Content -->
			<div class="p-6">
				{#if !showConfirmation}
					<!-- Employee Selection Phase -->
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<p class="text-sm text-gray-600">
								Select employees to delete. This action will also remove all their shifts.
							</p>
							<div class="flex space-x-2">
								<Button
									variant="secondary"
									size="sm"
									onclick={selectAllEmployees}
									disabled={employees.length === 0}
								>
									Select All
								</Button>
								<Button
									variant="secondary"
									size="sm"
									onclick={clearSelection}
									disabled={selectedEmployeeIds.length === 0}
								>
									Clear
								</Button>
							</div>
						</div>

						<!-- Employee List -->
						<div class="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
							{#if employees.length === 0}
								<div class="p-4 text-center text-gray-500">
									No employees available
								</div>
							{:else}
								<div class="divide-y divide-gray-200">
									{#each employees as employee}
										<label class="flex items-center p-4 hover:bg-gray-50 cursor-pointer">
											<input
												type="checkbox"
												checked={selectedEmployeeIds.includes(employee.id)}
												onchange={() => toggleEmployeeSelection(employee.id)}
												class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
											/>
											<div class="ml-3 flex-1">
												<div class="flex items-center space-x-3">
													<div class="flex-shrink-0">
														<div class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
															<span class="text-xs font-medium text-blue-700">
																{(employee.name || employee.fullName || '')
																	.split(' ')
																	.map((n) => n[0])
																	.join('')}
															</span>
														</div>
													</div>
													<div>
														<p class="text-sm font-medium text-gray-900">
															{employee.name}
														</p>
														<p class="text-xs text-gray-500">
															{employee.role} • {employee.isActive ? 'Active' : 'Inactive'}
														</p>
													</div>
												</div>
											</div>
										</label>
									{/each}
								</div>
							{/if}
						</div>

						<!-- Selection Summary -->
						{#if selectedEmployeeIds.length > 0}
							<div class="bg-red-50 border border-red-200 rounded-lg p-4">
								<p class="text-sm text-red-800">
									<strong>{selectedEmployeeIds.length}</strong> employee{selectedEmployeeIds.length === 1 ? '' : 's'} selected for deletion
								</p>
							</div>
						{/if}
					</div>
				{:else}
					<!-- Confirmation Phase -->
					<div class="space-y-4">
						<div class="bg-red-50 border border-red-200 rounded-lg p-4">
							<div class="flex">
								<div class="flex-shrink-0">
									<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
									</svg>
								</div>
								<div class="ml-3">
									<h3 class="text-sm font-medium text-red-800">
										Confirm Permanent Deletion
									</h3>
									<div class="mt-2 text-sm text-red-700">
										<p>
											You are about to permanently delete <strong>{selectedEmployees.length}</strong> 
											employee{selectedEmployees.length === 1 ? '' : 's'} and all their associated shifts:
										</p>
										<ul class="mt-2 list-disc list-inside space-y-1">
											{#each selectedEmployees as employee}
												<li>{employee.name} ({employee.role})</li>
											{/each}
										</ul>
										<p class="mt-2 font-medium">
											This action cannot be undone.
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				{/if}
			</div>

			<!-- Modal Footer -->
			<div class="flex items-center justify-end space-x-3 border-t border-gray-200 bg-gray-50 px-6 py-4">
				{#if !showConfirmation}
					<Button
						variant="secondary"
						onclick={handleClose}
						disabled={isDeleting}
					>
						Cancel
					</Button>
					<Button
						variant="danger"
						onclick={proceedToConfirmation}
						disabled={selectedEmployeeIds.length === 0 || isDeleting}
					>
						Delete Selected ({selectedEmployeeIds.length})
					</Button>
				{:else}
					<Button
						variant="secondary"
						onclick={cancelConfirmation}
						disabled={isDeleting}
					>
						Back
					</Button>
					<Button
						variant="danger"
						onclick={confirmBulkDelete}
						disabled={isDeleting}
						loading={isDeleting}
					>
						{isDeleting ? 'Deleting...' : `Confirm Delete (${selectedEmployees.length})`}
					</Button>
				{/if}
			</div>
		</div>
	</div>
{/if}

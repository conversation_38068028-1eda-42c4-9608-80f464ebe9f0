<script lang="ts">
	/**
	 * Tallyca Main Layout Component
	 * Replicates the Tallyca design system with sidebar navigation
	 * Following Code Complete principles: Clear structure, reusable design
	 */

	import { page } from '$app/stores';
	import { signOut, authState } from '$lib/stores/auth.js';
	import OrganizationSwitcher from './OrganizationSwitcher.svelte';

	interface Props {
		children?: any;
	}

	let { children }: Props = $props();

	// Navigation state
	let isSidebarCollapsed = $state(false);
	let activeSchedulerSubmenu = $state(false);

	// Auto-open scheduler submenu when on scheduler pages
	$effect(() => {
		if (isSchedulerActive) {
			activeSchedulerSubmenu = true;
		}
	});

	// Navigation items - Dashboard removed, scheduler as primary interface
	const navigationItems = [
		{
			id: 'scheduler',
			label: 'Work Scheduler',
			icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z',
			submenu: [
				{ label: 'Overview', href: '/scheduler' },
				{ label: 'Employees', href: '/scheduler/employees' },
				{ label: 'Schedule', href: '/scheduler/schedule' }
			]
		}
	];

	// Computed active states - Dashboard removed
	let isSchedulerActive = $derived($page.url.pathname.startsWith('/scheduler'));

	function toggleSidebar() {
		isSidebarCollapsed = !isSidebarCollapsed;
	}

	function toggleSchedulerSubmenu() {
		activeSchedulerSubmenu = !activeSchedulerSubmenu;
	}

	async function handleSignOut() {
		await signOut();
	}
</script>

<!-- Tallyca Layout Structure -->
<div class="flex h-screen bg-bg-tertiary">
	<!-- Sidebar -->
	<div class="flex-shrink-0 {isSidebarCollapsed ? 'w-16' : 'w-sidebar'} transition-all duration-300">
		<div class="layout-sidebar flex h-full flex-col shadow-sm">
			<!-- Logo/Brand -->
			<div class="flex items-center justify-between p-md border-b border-border-standard">
				{#if !isSidebarCollapsed}
					<div class="flex items-center space-x-3">
						<div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
							<span class="text-white font-bold text-sm">T</span>
						</div>
						<span class="text-xl font-bold text-text-primary">Tallyca</span>
					</div>
				{:else}
					<div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mx-auto">
						<span class="text-white font-bold text-sm">T</span>
					</div>
				{/if}
			</div>

			<!-- Navigation -->
			<nav class="flex-1 p-md space-y-2">
				{#each navigationItems as item}
					<div>
						{#if item.submenu}
							<!-- Expandable Menu Item -->
							<button
								onclick={toggleSchedulerSubmenu}
								class="w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors
									{isSchedulerActive ? 'bg-primary-light text-primary' : 'text-text-primary hover:bg-bg-tertiary'}"
							>
								<div class="flex items-center space-x-3">
									<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={item.icon} />
									</svg>
									{#if !isSidebarCollapsed}
										<span>{item.label}</span>
									{/if}
								</div>
								{#if !isSidebarCollapsed}
									<svg
										class="w-4 h-4 transition-transform {activeSchedulerSubmenu ? 'rotate-90' : ''}"
										fill="none" stroke="currentColor" viewBox="0 0 24 24"
									>
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
									</svg>
								{/if}
							</button>

							<!-- Submenu -->
							{#if activeSchedulerSubmenu && !isSidebarCollapsed}
								<div class="ml-8 mt-2 space-y-1">
									{#each item.submenu as subitem}
										<a
											href={subitem.href}
											class="block px-3 py-2 text-sm rounded-lg transition-colors
												{($page.url.pathname === subitem.href) ? 'bg-primary-light text-primary font-medium' : 'text-text-secondary hover:bg-bg-tertiary'}"
										>
											{subitem.label}
										</a>
									{/each}
								</div>
							{/if}
						{/if}
					</div>
				{/each}
			</nav>

			<!-- User Section -->
			<div class="border-t border-border-standard p-md">
				{#if !isSidebarCollapsed}
					<div class="space-y-3">
						<OrganizationSwitcher />
						<button
							onclick={handleSignOut}
							class="w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium text-danger rounded-lg hover:bg-danger-light transition-colors"
						>
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
							</svg>
							<span>Sign Out</span>
						</button>
					</div>
				{:else}
					<button
						onclick={handleSignOut}
						class="w-full flex items-center justify-center p-2 text-danger rounded-lg hover:bg-danger-light transition-colors"
						title="Sign Out"
						aria-label="Sign Out"
					>
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1" />
						</svg>
					</button>
				{/if}
			</div>
		</div>
	</div>

	<!-- Main Content Area -->
	<div class="flex-1 flex flex-col overflow-hidden">
		<!-- Top Header -->
		<header class="layout-header px-lg py-md">
			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-4">
					<button
						onclick={toggleSidebar}
						class="p-2 text-text-muted hover:text-text-primary hover:bg-bg-tertiary rounded-lg transition-colors"
						aria-label="Toggle sidebar"
					>
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
						</svg>
					</button>
					<h1 class="text-page-title text-text-primary">
						{#if $page.url.pathname.startsWith('/scheduler')}
							Work Scheduler
						{:else}
							Tallyca
						{/if}
					</h1>
				</div>

				<div class="flex items-center space-x-4">
					<div class="text-sm text-text-secondary">
						{$authState.user?.email}
					</div>
				</div>
			</div>
		</header>

		<!-- Page Content -->
		<main class="flex-1 overflow-auto bg-bg-tertiary">
			{@render children()}
		</main>
	</div>
</div>

<!--
	Badge Component
	Following Code Complete principles: Single responsibility, clear interface, accessibility
	Uses Design System for consistent styling
-->

<script lang="ts">
	import { getBadgeClasses, combineClasses } from '$lib/design-system/index.js';
	import type { BadgeProps } from '$lib/design-system/index.js';

	/**
	 * Badge component props using Design System types
	 * Following Code Complete: Clear parameter definitions, type safety
	 */
	let {
		variant = 'primary',
		children,
		className = ''
	}: BadgeProps = $props();

	/**
	 * Generate badge classes using design system
	 * Following Code Complete: Pure function, clear logic separation
	 */
	const badgeClasses = $derived(
		combineClasses(
			getBadgeClasses(variant),
			className
		)
	);
</script>

<span class={badgeClasses}>
	{@render children?.()}
</span>

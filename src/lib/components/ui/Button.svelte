<!--
	Reusable Button Component
	Following Code Complete principles: Single responsibility, clear interface, consistent behavior
	Updated to use Design System
-->

<script lang="ts">
	import { getButtonClasses, combineClasses } from '$lib/design-system/index.js';
	import type { ButtonProps } from '$lib/design-system/index.js';

	/**
	 * Button component props using Design System types
	 * Following Code Complete: Clear parameter definitions, type safety
	 */
	let {
		variant = 'primary',
		size = 'md',
		disabled = false,
		loading = false,
		fullWidth = false,
		type = 'button',
		onclick,
		children,
		className = ''
	}: ButtonProps = $props();

	/**
	 * Generate button classes using design system
	 * Following Code Complete: Pure function, clear logic separation
	 */
	const buttonClasses = $derived(
		combineClasses(
			getButtonClasses(variant, size, fullWidth),
			loading && 'opacity-75 cursor-wait',
			className
		)
	);

	/**
	 * Handle button click
	 * Following Code Complete: Clear event handling, proper delegation
	 */
	function handleClick(event: MouseEvent): void {
		const buttonText = (event.target as HTMLElement)?.textContent?.trim() || 'Unknown';
		console.log('🔄 Button.handleClick: Click detected on button:', buttonText, { disabled, loading, onclick: !!onclick });

		if (disabled || loading) {
			console.log('❌ Button.handleClick: Click prevented (disabled or loading)');
			event.preventDefault();
			return;
		}

		console.log('✅ Button.handleClick: Calling onclick handler for button:', buttonText);
		onclick?.(event);
		console.log('✅ Button.handleClick: onclick handler completed for button:', buttonText);
	}
</script>

<button
	{type}
	class={buttonClasses}
	{disabled}
	onclick={handleClick}
>
	{#if loading}
		<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
			<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
			<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
		</svg>
		Loading...
	{:else}
		{@render children?.()}
	{/if}
</button>

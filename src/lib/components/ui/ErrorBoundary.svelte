<!--
	Error Boundary Component
	Following Code Complete principles: Graceful error handling, clear user feedback, proper logging
	Provides comprehensive error handling for the restaurant scheduling application
-->

<script lang="ts">
	import { onMount } from 'svelte';
	import { combineClasses } from '$lib/design-system/index.js';
	import Button from './Button.svelte';
	import type { Snippet } from 'svelte';

	/**
	 * Error boundary component props
	 * Following Code Complete: Clear parameter definitions, type safety
	 */
	interface Props {
		/** Child content to render when no error */
		children: Snippet;
		/** Optional fallback content when error occurs */
		fallback?: Snippet<[Error]>;
		/** Custom error handler */
		onError?: (error: Error, errorInfo?: any) => void;
		/** Show detailed error info in development */
		showDetails?: boolean;
		/** Custom CSS classes */
		className?: string;
	}

	let {
		children,
		fallback,
		onError,
		showDetails = false,
		className = ''
	}: Props = $props();

	// Error state management
	let hasError = $state(false);
	let error = $state<Error | null>(null);
	let errorInfo = $state<any>(null);

	/**
	 * Handle errors caught by the boundary
	 * Following Code Complete: Comprehensive error handling with logging
	 */
	function handleError(err: Error, info?: any): void {
		console.error('🚨 ErrorBoundary: Error caught:', err);
		console.error('🚨 ErrorBoundary: Error info:', info);
		
		hasError = true;
		error = err;
		errorInfo = info;

		// Call custom error handler if provided
		onError?.(err, info);

		// In production, you might want to send this to an error reporting service
		if (typeof window !== 'undefined' && window.location.hostname !== 'localhost') {
			// Example: Send to error reporting service
			// errorReportingService.captureException(err, { extra: info });
		}
	}

	/**
	 * Reset error boundary state
	 * Following Code Complete: Clear state management
	 */
	function resetError(): void {
		console.log('🔄 ErrorBoundary: Resetting error state');
		hasError = false;
		error = null;
		errorInfo = null;
	}

	/**
	 * Set up global error handlers
	 * Following Code Complete: Comprehensive error catching
	 */
	onMount(() => {
		// Handle unhandled promise rejections
		const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
			console.error('🚨 ErrorBoundary: Unhandled promise rejection:', event.reason);
			handleError(new Error(`Unhandled promise rejection: ${event.reason}`), {
				type: 'unhandledRejection',
				reason: event.reason
			});
		};

		// Handle global errors
		const handleGlobalError = (event: ErrorEvent) => {
			console.error('🚨 ErrorBoundary: Global error:', event.error);
			handleError(event.error || new Error(event.message), {
				type: 'globalError',
				filename: event.filename,
				lineno: event.lineno,
				colno: event.colno
			});
		};

		window.addEventListener('unhandledrejection', handleUnhandledRejection);
		window.addEventListener('error', handleGlobalError);

		return () => {
			window.removeEventListener('unhandledrejection', handleUnhandledRejection);
			window.removeEventListener('error', handleGlobalError);
		};
	});

	/**
	 * Get error boundary container classes
	 * Following Code Complete: Pure function, clear logic separation
	 */
	const containerClasses = $derived(
		combineClasses(
			'error-boundary',
			className
		)
	);

	/**
	 * Format error message for display
	 * Following Code Complete: Pure function, clear data transformation
	 */
	function formatErrorMessage(err: Error): string {
		if (!err) return 'An unknown error occurred';
		
		// In development, show full error details
		if (showDetails || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
			return `${err.name}: ${err.message}`;
		}
		
		// In production, show user-friendly messages
		const userFriendlyMessages: Record<string, string> = {
			'NetworkError': 'Network connection error. Please check your internet connection.',
			'TypeError': 'A technical error occurred. Please try again.',
			'ReferenceError': 'A technical error occurred. Please try again.',
			'SyntaxError': 'A technical error occurred. Please try again.',
			'RangeError': 'Invalid data encountered. Please try again.',
		};

		return userFriendlyMessages[err.name] || 'Something went wrong. Please try again.';
	}
</script>

{#if hasError && error}
	<div class={containerClasses}>
		{#if fallback}
			{@render fallback(error)}
		{:else}
			<!-- Default error UI -->
			<div class="min-h-[400px] flex items-center justify-center p-8">
				<div class="max-w-md w-full text-center">
					<!-- Error Icon -->
					<div class="mx-auto w-16 h-16 mb-6 text-red-500">
						<svg fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-full h-full">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
								d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
						</svg>
					</div>

					<!-- Error Message -->
					<h2 class="text-xl font-semibold text-gray-900 mb-4">
						Oops! Something went wrong
					</h2>
					
					<p class="text-gray-600 mb-6">
						{formatErrorMessage(error)}
					</p>

					<!-- Error Details (Development only) -->
					{#if showDetails && errorInfo}
						<details class="mb-6 text-left">
							<summary class="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
								Technical Details
							</summary>
							<pre class="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto max-h-40">
								{JSON.stringify(errorInfo, null, 2)}
							</pre>
						</details>
					{/if}

					<!-- Action Buttons -->
					<div class="space-y-3">
						<Button
							variant="primary"
							size="md"
							fullWidth={true}
							onclick={resetError}
							className="w-full"
						>
							Try Again
						</Button>
						
						<Button
							variant="secondary"
							size="md"
							fullWidth={true}
							onclick={() => window.location.reload()}
							className="w-full"
						>
							Reload Page
						</Button>
					</div>

					<!-- Support Information -->
					<p class="mt-6 text-sm text-gray-500">
						If this problem persists, please contact support with the error details above.
					</p>
				</div>
			</div>
		{/if}
	</div>
{:else}
	<!-- Render children when no error -->
	<div class={containerClasses}>
		{@render children()}
	</div>
{/if}

<style>
	.error-boundary {
		/* Ensure error boundary takes full space */
		min-height: inherit;
		width: 100%;
	}
</style>

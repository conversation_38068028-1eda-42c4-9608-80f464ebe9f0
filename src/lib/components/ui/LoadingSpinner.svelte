<!--
	Reusable Loading Spinner Component
	Following Code Complete principles: Single responsibility, clear interface, consistent behavior
	Updated to use Design System
-->

<script lang="ts">
	import { combineClasses } from '$lib/design-system/index.js';

	/**
	 * Loading spinner component props
	 * Following Code Complete: Clear parameter definitions, type safety
	 */
	interface Props {
		size?: 'sm' | 'md' | 'lg';
		color?: 'primary' | 'secondary' | 'white';
		message?: string;
		fullScreen?: boolean;
		class?: string;
	}

	let {
		size = 'md',
		color = 'primary',
		message = 'Loading...',
		fullScreen = false,
		class: className = ''
	}: Props = $props();

	/**
	 * Get spinner size classes using design system
	 * Following Code Complete: Pure function, clear logic separation
	 */
	function getSpinnerSizeClasses(): string {
		const sizeClasses = {
			sm: 'w-4 h-4',
			md: 'w-8 h-8',
			lg: 'w-12 h-12'
		};

		return sizeClasses[size];
	}

	/**
	 * Get spinner color classes using design system
	 * Following Code Complete: Pure function, clear logic separation
	 */
	function getSpinnerColorClasses(): string {
		const colorClasses = {
			primary: 'border-primary',
			secondary: 'border-text-secondary',
			white: 'border-white'
		};

		return colorClasses[color];
	}

	/**
	 * Get container classes using design system
	 * Following Code Complete: Pure function, clear logic separation
	 */
	function getContainerClasses(): string {
		const baseClasses = fullScreen
			? 'fixed inset-0 bg-bg-primary bg-opacity-75 flex items-center justify-center z-50'
			: 'flex items-center justify-center';

		return combineClasses(baseClasses, className);
	}
</script>

<div class={getContainerClasses()}>
	<div class="text-center">
		<!-- Spinner -->
		<div
			class="animate-spin rounded-full border-2 border-t-transparent {getSpinnerSizeClasses()} {getSpinnerColorClasses()} mx-auto"
			role="status"
			aria-label="Loading"
		></div>

		<!-- Message -->
		{#if message}
			<p class="text-text-secondary mt-2 text-sm">
				{message}
			</p>
		{/if}
	</div>
</div>

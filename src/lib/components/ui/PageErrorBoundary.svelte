<!--
	Page-Level Error Boundary Component
	Following Code Complete principles: Simplified error handling for page-level errors
	Provides lightweight error handling for individual pages
-->

<script lang="ts">
	import { combineClasses } from '$lib/design-system/index.js';
	import Button from './Button.svelte';
	import type { Snippet } from 'svelte';

	/**
	 * Page error boundary component props
	 * Following Code Complete: Clear parameter definitions, type safety
	 */
	interface Props {
		/** Child content to render when no error */
		children: Snippet;
		/** Page title for error display */
		pageTitle?: string;
		/** Custom error handler */
		onError?: (error: Error) => void;
		/** Custom CSS classes */
		className?: string;
	}

	let {
		children,
		pageTitle = 'Page',
		onError,
		className = ''
	}: Props = $props();

	// Error state management
	let hasError = $state(false);
	let error = $state<Error | null>(null);

	/**
	 * Handle errors caught by the boundary
	 * Following Code Complete: Simple error handling with logging
	 */
	function handleError(err: Error): void {
		console.error(`🚨 PageErrorBoundary (${pageTitle}): Error caught:`, err);
		
		hasError = true;
		error = err;

		// Call custom error handler if provided
		onError?.(err);
	}

	/**
	 * Reset error boundary state
	 * Following Code Complete: Clear state management
	 */
	function resetError(): void {
		console.log(`🔄 PageErrorBoundary (${pageTitle}): Resetting error state`);
		hasError = false;
		error = null;
	}

	/**
	 * Navigate back to dashboard
	 * Following Code Complete: Clear navigation logic
	 */
	function goToDashboard(): void {
		window.location.href = '/dashboard';
	}

	/**
	 * Get error boundary container classes
	 * Following Code Complete: Pure function, clear logic separation
	 */
	const containerClasses = $derived(
		combineClasses(
			'page-error-boundary min-h-screen',
			className
		)
	);

	/**
	 * Get user-friendly error message
	 * Following Code Complete: Pure function, clear data transformation
	 */
	function getUserFriendlyMessage(err: Error): string {
		if (!err) return 'An unexpected error occurred';

		// Common error patterns and their user-friendly messages
		const errorPatterns = [
			{ pattern: /network|fetch|connection/i, message: 'Network connection error. Please check your internet connection.' },
			{ pattern: /unauthorized|401/i, message: 'You need to sign in to access this page.' },
			{ pattern: /forbidden|403/i, message: 'You don\'t have permission to access this page.' },
			{ pattern: /not found|404/i, message: 'The requested page could not be found.' },
			{ pattern: /server|500/i, message: 'Server error. Please try again later.' },
			{ pattern: /timeout/i, message: 'Request timed out. Please try again.' }
		];

		const errorMessage = err.message.toLowerCase();
		
		for (const { pattern, message } of errorPatterns) {
			if (pattern.test(errorMessage)) {
				return message;
			}
		}

		return 'Something went wrong while loading this page.';
	}
</script>

{#if hasError && error}
	<div class={containerClasses}>
		<div class="flex items-center justify-center min-h-screen p-4">
			<div class="max-w-md w-full text-center">
				<!-- Error Icon -->
				<div class="mx-auto w-12 h-12 mb-4 text-red-500">
					<svg fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-full h-full">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
							d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
					</svg>
				</div>

				<!-- Error Message -->
				<h1 class="text-2xl font-bold text-gray-900 mb-2">
					{pageTitle} Error
				</h1>
				
				<p class="text-gray-600 mb-6">
					{getUserFriendlyMessage(error)}
				</p>

				<!-- Action Buttons -->
				<div class="space-y-3">
					<Button
						variant="primary"
						size="md"
						fullWidth={true}
						onclick={resetError}
						className="w-full"
					>
						Try Again
					</Button>
					
					<Button
						variant="secondary"
						size="md"
						fullWidth={true}
						onclick={goToDashboard}
						className="w-full"
					>
						Go to Dashboard
					</Button>
				</div>

				<!-- Support Information -->
				<p class="mt-6 text-sm text-gray-500">
					If this problem continues, please refresh the page or contact support.
				</p>
			</div>
		</div>
	</div>
{:else}
	<!-- Render children when no error -->
	<div class={containerClasses}>
		{@render children()}
	</div>
{/if}

<style>
	.page-error-boundary {
		/* Ensure error boundary takes full viewport */
		width: 100%;
	}
</style>

<!--
	Reusable Form Field Component
	Following Code Complete principles: Single responsibility, accessibility, clear interface
-->

<script lang="ts">
	/**
	 * Form field component props
	 * Following Code Complete: Clear parameter definitions, type safety
	 */
	interface Props {
		label: string;
		id: string;
		type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'date' | 'time' | 'select' | 'textarea';
		value?: string | number;
		placeholder?: string;
		required?: boolean;
		disabled?: boolean;
		error?: string;
		helpText?: string;
		options?: Array<{ value: string | number; label: string }>;
		rows?: number;
		min?: number;
		max?: number;
		step?: number;
		children?: any;
	}

	let {
		label,
		id,
		type = 'text',
		value = $bindable(),
		placeholder,
		required = false,
		disabled = false,
		error,
		helpText,
		options = [],
		rows = 3,
		min,
		max,
		step,
		children
	}: Props = $props();

	/**
	 * Get input classes based on state
	 * Following Code Complete: Pure function, clear logic separation
	 */
	function getInputClasses(): string {
		const baseClasses = 'w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:border-transparent transition-colors';
		const errorClasses = error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500';
		const disabledClasses = disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white';
		
		return `${baseClasses} ${errorClasses} ${disabledClasses}`;
	}
</script>

<div class="space-y-1">
	<!-- Label -->
	<label for={id} class="block text-sm font-medium text-gray-700">
		{label}
		{#if required}
			<span class="text-red-500">*</span>
		{/if}
	</label>

	<!-- Input Field -->
	{#if type === 'select'}
		<select
			{id}
			bind:value
			{disabled}
			{required}
			class={getInputClasses()}
			aria-describedby={error ? `${id}-error` : helpText ? `${id}-help` : undefined}
		>
			{#if placeholder}
				<option value="" disabled>{placeholder}</option>
			{/if}
			{#each options as option}
				<option value={option.value}>{option.label}</option>
			{/each}
			{@render children?.()}
		</select>
	{:else if type === 'textarea'}
		<textarea
			{id}
			bind:value
			{placeholder}
			{disabled}
			{required}
			{rows}
			class={getInputClasses()}
			aria-describedby={error ? `${id}-error` : helpText ? `${id}-help` : undefined}
		></textarea>
	{:else}
		<input
			{id}
			{type}
			bind:value
			{placeholder}
			{disabled}
			{required}
			{min}
			{max}
			{step}
			class={getInputClasses()}
			aria-describedby={error ? `${id}-error` : helpText ? `${id}-help` : undefined}
		/>
	{/if}

	<!-- Error Message -->
	{#if error}
		<p id="{id}-error" class="text-sm text-red-600" role="alert">
			{error}
		</p>
	{/if}

	<!-- Help Text -->
	{#if helpText && !error}
		<p id="{id}-help" class="text-sm text-gray-500">
			{helpText}
		</p>
	{/if}
</div>

<!--
	Input Component
	Following Code Complete principles: Reusable, consistent, accessible input component
	Handles validation states, error display, and proper accessibility
	Updated to use Design System
-->
<script lang="ts">
	import { getInputClasses, combineClasses } from '$lib/design-system/index.js';

	interface Props {
		id: string;
		type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
		placeholder?: string;
		value?: string;
		required?: boolean;
		disabled?: boolean;
		error?: string;
		label?: string;
		autocomplete?: string;
		class?: string;
		size?: 'sm' | 'md' | 'lg';
		variant?: 'default' | 'error' | 'success';
	}

	let {
		id,
		type = 'text',
		placeholder = '',
		value = $bindable(''),
		required = false,
		disabled = false,
		error = '',
		label = '',
		autocomplete = '',
		class: className = '',
		size = 'md',
		variant = 'default'
	}: Props = $props();

	// Generate input classes using design system
	const inputClasses = $derived(combineClasses(getInputClasses(variant, size, !!error), className));
</script>

<!-- Label (if provided) -->
{#if label}
	<label for={id} class="text-text-primary mb-1 block text-sm font-medium">
		{label}
		{#if required}
			<span class="text-danger">*</span>
		{/if}
	</label>
{/if}

<!-- Input Field -->
<input
	{id}
	{type}
	{placeholder}
	{required}
	{disabled}
	autocomplete={autocomplete as any}
	bind:value
	class={inputClasses}
	aria-invalid={error ? 'true' : 'false'}
	aria-describedby={error ? `${id}-error` : undefined}
/>

<!-- Error Message -->
{#if error}
	<p id="{id}-error" class="text-danger mt-1 text-sm" role="alert">
		{error}
	</p>
{/if}

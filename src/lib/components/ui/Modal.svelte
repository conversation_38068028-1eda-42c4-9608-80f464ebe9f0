<!--
	Reusable Modal Component
	Following Code Complete principles: Single responsibility, clear interface, accessibility
	Updated to use Design System
-->

<script lang="ts">
	import { getModalSizeClasses } from '$lib/design-system/index.js';
	import type { ModalProps } from '$lib/design-system/index.js';

	/**
	 * Modal component props using Design System types
	 * Following Code Complete: Clear parameter definitions, type safety
	 */
	let {
		isOpen = false,
		title,
		size = 'md',
		closeOnBackdrop = true,
		onclose,
		onClose,
		children,
		className = ''
	}: ModalProps = $props();

	// Handle both onclose and onClose for backward compatibility
	const handleClose = () => {
		onclose?.();
		onClose?.();
	};

	/**
	 * Handle backdrop click
	 * Following Code Complete: Clear event handling, proper validation
	 */
	function handleBackdropClick(event: MouseEvent): void {
		if (closeOnBackdrop && event.target === event.currentTarget) {
			handleClose();
		}
	}

	/**
	 * Handle backdrop keyboard interaction
	 * Following Code Complete: Accessibility requirement, clear purpose
	 */
	function handleBackdropKeydown(event: KeyboardEvent): void {
		if (event.key === 'Enter' && closeOnBackdrop && event.target === event.currentTarget) {
			onclose?.();
		}
	}

	/**
	 * Handle escape key press
	 * Following Code Complete: Accessibility requirement, clear purpose
	 */
	function handleKeydown(event: KeyboardEvent): void {
		if (event.key === 'Escape' && isOpen) {
			onclose?.();
		}
	}

	/**
	 * Close modal
	 * Following Code Complete: Clear action, single responsibility
	 */
	function closeModal(): void {
		onclose?.();
	}
</script>

<svelte:window onkeydown={handleKeydown} />

{#if isOpen}
	<!-- Modal Backdrop -->
	<div
		class="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black p-4"
		onclick={handleBackdropClick}
		onkeydown={handleBackdropKeydown}
		role="dialog"
		aria-modal="true"
		aria-labelledby="modal-title"
		tabindex="0"
	>
		<!-- Modal Container -->
		<div
			class="card-base w-full {getModalSizeClasses(size)} max-h-[90vh] overflow-y-auto {className}"
		>
			<!-- Modal Header -->
			<div class="p-lg border-border-standard flex items-center justify-between border-b">
				<h2 id="modal-title" class="text-heading text-text-primary text-lg">
					{title}
				</h2>
				<button
					onclick={closeModal}
					class="text-text-muted hover:text-text-secondary focus-ring transition-colors"
					aria-label="Close modal"
				>
					<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M6 18L18 6M6 6l12 12"
						/>
					</svg>
				</button>
			</div>

			<!-- Modal Content -->
			<div class="p-lg">
				{@render children?.()}
			</div>
		</div>
	</div>
{/if}

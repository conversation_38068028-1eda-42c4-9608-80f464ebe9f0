<!--
	Reusable Error Display Component
	Following Code Complete principles: Single responsibility, clear interface, user-friendly error handling
-->

<script lang="ts">
	import Button from './Button.svelte';

	/**
	 * Error display component props
	 * Following Code Complete: Clear parameter definitions, type safety
	 */
	interface Props {
		title?: string;
		message: string;
		showRetry?: boolean;
		retryLabel?: string;
		fullScreen?: boolean;
		onRetry?: () => void;
	}

	let {
		title = 'Something went wrong',
		message,
		showRetry = false,
		retryLabel = 'Try Again',
		fullScreen = false,
		onRetry
	}: Props = $props();

	/**
	 * Get container classes
	 * Following Code Complete: Pure function, clear logic separation
	 */
	function getContainerClasses(): string {
		if (fullScreen) {
			return 'fixed inset-0 bg-white flex items-center justify-center z-50';
		}
		return 'flex items-center justify-center p-8';
	}

	/**
	 * Handle retry action
	 * Following Code Complete: Clear event handling, proper delegation
	 */
	function handleRetry(): void {
		onRetry?.();
	}
</script>

<div class={getContainerClasses()}>
	<div class="text-center max-w-md">
		<!-- Error Icon -->
		<div class="text-red-600 mb-4">
			<svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path 
					stroke-linecap="round" 
					stroke-linejoin="round" 
					stroke-width="2" 
					d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
				/>
			</svg>
		</div>

		<!-- Error Title -->
		<h2 class="text-xl font-semibold text-gray-900 mb-2">
			{title}
		</h2>

		<!-- Error Message -->
		<p class="text-gray-600 mb-4">
			{message}
		</p>

		<!-- Retry Button -->
		{#if showRetry && onRetry}
			<Button variant="primary" onclick={handleRetry}>
				{retryLabel}
			</Button>
		{/if}
	</div>
</div>

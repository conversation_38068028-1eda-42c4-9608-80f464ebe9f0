<script lang="ts">
	/**
	 * Bulk Employee Upload Component
	 * Allows CSV-style bulk employee creation with live preview
	 * Following Code Complete principles: Clear validation, user feedback
	 */
	
	import { employeeService } from '$lib/services/index.js';
	import { Button } from '$lib/components/ui/index.js';
	import { formatCurrency } from '$lib/utils.js';
	import Modal from './Modal.svelte';
	import type { EmployeeFormData } from '$lib/types.js';
	
	interface Props {
		onComplete: () => void;
		onClose: () => void;
	}
	
	let { onComplete, onClose }: Props = $props();
	
	// State
	let csvText = $state('');
	let isProcessing = $state(false);
	let validationErrors: string[] = $state([]);
	let previewEmployees: EmployeeFormData[] = $state([]);
	
	// Sample data for user guidance
	const sampleData = `<PERSON>, <PERSON>, 150
<PERSON>, <PERSON>, 120
<PERSON>, <PERSON>, 140
<PERSON>, Host, 100`;
	
	// Parse CSV text into employee data
	function parseCSV(text: string): { employees: EmployeeFormData[], errors: string[] } {
		const lines = text.trim().split('\n').filter(line => line.trim());
		const employees: EmployeeFormData[] = [];
		const errors: string[] = [];
		
		lines.forEach((line, index) => {
			const lineNumber = index + 1;
			const parts = line.split(',').map(part => part.trim());
			
			if (parts.length < 3) {
				errors.push(`Line ${lineNumber}: Missing required fields (name, role, rate)`);
				return;
			}
			
			const [name, role, rateStr] = parts;
			
			// Validate name
			if (!name || name.length < 2) {
				errors.push(`Line ${lineNumber}: Invalid name "${name}"`);
				return;
			}
			
			// Validate role - check against known role mappings
			if (!role || role.length < 2) {
				errors.push(`Line ${lineNumber}: Invalid role "${role}"`);
				return;
			}

			// Check if role can be mapped to a valid enum value
			const roleEnumMap: Record<string, string> = {
				'Front of House': 'FOH',
				'Back of House': 'BOH',
				'Delivery': 'DELIVERY',
				'Other': 'OTHER',
				'Server': 'FOH',
				'Cook': 'BOH',
				'Kitchen': 'BOH',
				'Manager': 'FOH',
				'Host': 'FOH',
				'Bartender': 'FOH',
				'Chef': 'BOH',
				'Prep': 'BOH',
				'Dishwasher': 'BOH',
				'Driver': 'DELIVERY',
				'FOH': 'FOH',
				'BOH': 'BOH',
				'DELIVERY': 'DELIVERY',
				'OTHER': 'OTHER'
			};

			const normalizedRole = role.trim();
			const mappedRole = roleEnumMap[normalizedRole] || roleEnumMap[normalizedRole.toUpperCase()] || null;

			if (!mappedRole) {
				errors.push(`Line ${lineNumber}: Unknown role "${role}". Use: Server, Cook, Manager, Host, Bartender, Chef, Prep, Dishwasher, Driver, or Other`);
				return;
			}
			
			// Validate rate
			const rate = parseFloat(rateStr);
			if (isNaN(rate) || rate <= 0) {
				errors.push(`Line ${lineNumber}: Invalid daily rate "${rateStr}"`);
				return;
			}
			
			// Check for duplicates within this batch
			const duplicate = employees.find(emp => 
				emp.name.toLowerCase() === name.toLowerCase()
			);
			if (duplicate) {
				errors.push(`Line ${lineNumber}: Duplicate employee name "${name}"`);
				return;
			}
			
			employees.push({
				name: name,
				role: role,
				defaultDailyRate: rate,
				isActive: true
			});
		});
		
		return { employees, errors };
	}
	
	// Update preview when CSV text changes
	$effect(() => {
		if (csvText.trim()) {
			const { employees, errors } = parseCSV(csvText);
			previewEmployees = employees;
			validationErrors = errors;
		} else {
			previewEmployees = [];
			validationErrors = [];
		}
	});
	
	// Load sample data
	function loadSample() {
		csvText = sampleData;
	}
	
	// Clear all data
	function clearData() {
		csvText = '';
		previewEmployees = [];
		validationErrors = [];
	}
	
	// Submit bulk upload
	async function handleSubmit() {
		if (previewEmployees.length === 0 || validationErrors.length > 0) {
			return;
		}
		
		try {
			isProcessing = true;
			
			// Create employees one by one (could be optimized with batch RPC)
			for (const employeeData of previewEmployees) {
				await employeeService.createEmployee(employeeData);
			}
			
			console.log(`✅ BulkEmployeeUpload: Created ${previewEmployees.length} employees`);
			onComplete();
			
		} catch (error) {
			console.error('❌ BulkEmployeeUpload: Failed to create employees:', error);
			alert(`Failed to create employees: ${error instanceof Error ? error.message : 'Unknown error'}`);
		} finally {
			isProcessing = false;
		}
	}
</script>

<Modal isOpen={true} onClose={onClose} size="lg">
	<div class="p-6">
		<!-- Header -->
		<div class="mb-6">
			<h2 class="text-xl font-semibold text-gray-900 mb-2">Bulk Add Employees</h2>
			<p class="text-gray-600">
				Paste employee data in CSV format: Name, Role, Daily Rate (one per line)
			</p>
		</div>
		
		<!-- Instructions -->
		<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
			<h3 class="text-sm font-medium text-blue-900 mb-2">Format Instructions:</h3>
			<ul class="text-sm text-blue-800 space-y-1">
				<li>• Each line should contain: Name, Role, Daily Rate</li>
				<li>• Separate values with commas</li>
				<li>• Daily rate should be a number (e.g., 150 for 150 лв/day)</li>
				<li>• Names must be unique</li>
			</ul>
			<div class="mt-3">
				<button
					onclick={loadSample}
					class="text-sm text-blue-700 hover:text-blue-900 underline"
				>
					Load sample data
				</button>
			</div>
		</div>
		
		<!-- CSV Input -->
		<div class="mb-6">
			<label for="csv-input" class="block text-sm font-medium text-gray-700 mb-2">
				Employee Data
			</label>
			<textarea
				id="csv-input"
				bind:value={csvText}
				placeholder="John Smith, Manager, 150&#10;Jane Doe, Server, 120&#10;Mike Johnson, Cook, 140"
				rows="8"
				class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
			></textarea>
			<div class="flex justify-between items-center mt-2">
				<p class="text-xs text-gray-500">
					{csvText.trim().split('\n').filter(line => line.trim()).length} lines entered
				</p>
				{#if csvText.trim()}
					<button
						onclick={clearData}
						class="text-xs text-red-600 hover:text-red-800"
					>
						Clear all
					</button>
				{/if}
			</div>
		</div>
		
		<!-- Validation Errors -->
		{#if validationErrors.length > 0}
			<div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
				<h3 class="text-sm font-medium text-red-900 mb-2">Validation Errors:</h3>
				<ul class="text-sm text-red-800 space-y-1">
					{#each validationErrors as error}
						<li>• {error}</li>
					{/each}
				</ul>
			</div>
		{/if}
		
		<!-- Preview -->
		{#if previewEmployees.length > 0}
			<div class="mb-6">
				<h3 class="text-sm font-medium text-gray-900 mb-3">
					Preview ({previewEmployees.length} employees)
				</h3>
				<div class="bg-gray-50 border border-gray-200 rounded-lg overflow-hidden">
					<table class="min-w-full">
						<thead class="bg-gray-100">
							<tr>
								<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
								<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Role</th>
								<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Daily Rate</th>
							</tr>
						</thead>
						<tbody class="divide-y divide-gray-200">
							{#each previewEmployees as employee}
								<tr>
									<td class="px-4 py-2 text-sm text-gray-900">{employee.name}</td>
									<td class="px-4 py-2 text-sm text-gray-900">{employee.role}</td>
									<td class="px-4 py-2 text-sm text-gray-900">{formatCurrency(employee.defaultDailyRate)}</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			</div>
		{/if}
		
		<!-- Actions -->
		<div class="flex justify-end space-x-3">
			<Button
				variant="outline"
				onclick={onClose}
				disabled={isProcessing}
			>
				Cancel
			</Button>
			
			<Button
				onclick={handleSubmit}
				disabled={previewEmployees.length === 0 || validationErrors.length > 0 || isProcessing}
				loading={isProcessing}
			>
				{isProcessing ? 'Creating...' : `Create ${previewEmployees.length} Employees`}
			</Button>
		</div>
	</div>
</Modal>

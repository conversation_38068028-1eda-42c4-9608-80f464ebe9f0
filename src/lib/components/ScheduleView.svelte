<script lang="ts">
	import { onMount } from 'svelte';
	import type {
		WeeklySchedule,
		EnhancedWeeklySchedule,
		UIState,
		Shift,
		Employee,
		LeaveRequest,
		EmployeeUnavailability,
		ShiftConflict
	} from '$lib/types.js';
	import { getWeekDates, formatDateDisplay } from '$lib/utils.js';
	// Note: Removed server-side calculations from display layer per Code Complete principles
	import { shiftService } from '$lib/services/shiftService.js';
	import { scheduleService } from '$lib/services/scheduleService.js';
	import { enhancedScheduleService } from '$lib/services/enhancedScheduleService.js';
	import { signOut, authState } from '$lib/stores/auth.js';
	import ScheduleGrid from './ScheduleGrid.svelte';
	import EnhancedScheduleGrid from './EnhancedScheduleGrid.svelte';
	import EmployeePanel from './EmployeePanel.svelte';
	import FooterToolbar from './FooterToolbar.svelte';
	import ShiftModal from './ShiftModal.svelte';
	import EmployeeModal from './EmployeeModal.svelte';
	import LeaveRequestModal from './LeaveRequestModal.svelte';
	import LeaveManagementDashboard from './LeaveManagementDashboard.svelte';
	import ShiftReassignmentPanel from './ShiftReassignmentPanel.svelte';
	import LocationManagement from './LocationManagement.svelte';
	import PaymentManagement from './PaymentManagement.svelte';
	import OrganizationSwitcher from './OrganizationSwitcher.svelte';

	interface Props {
		schedule: WeeklySchedule;
	}

	let { schedule = $bindable() }: Props = $props();

	// UI State
	let uiState: UIState = $state({
		selectedWeek: schedule.weekStartDate,
		dragState: {
			isDragging: false,
			draggedShift: null,
			draggedEmployee: null,
			dropTarget: null
		},
		dragCreateState: {
			isActive: false,
			mode: null,
			direction: null,
			sourceEmployee: null,
			sourceDate: null,
			sourceShift: null,
			targetDates: [],
			targetEmployees: [],
			previewCells: []
		},
		shiftModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		employeeModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		leaveModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		unavailabilityModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		reassignmentPanel: {
			isOpen: false,
			affectedShifts: [],
			leaveRequest: undefined,
			suggestedReplacements: []
		},
		isEmployeePanelCollapsed: false,
		isMobileView: false,
		showLocationManagement: false,
		showPaymentManagement: false,
		showLeaveManagement: false,
		showUnavailabilityOverlay: false
	});

	// Enhanced scheduling state
	let enhancedSchedule: EnhancedWeeklySchedule | null = $state(null);
	let isEnhancedMode = $state(false);
	let isLoadingEnhanced = $state(false);
	let activeTab: 'schedule' | 'leave' = $state('schedule');

	// Get organization ID from current restaurant (backward compatibility)
	let organizationId = $derived($authState.currentRestaurant?.id || '');
	let currentUserId = $derived($authState.user?.id || '');
	let userRole = $derived((() => {
		const restaurantRole = $authState.userRestaurants.find(ur => ur.restaurant_id === organizationId)?.role || 'staff';
		// Map restaurant roles to organization roles
		switch (restaurantRole) {
			case 'owner': return 'superadmin' as const;
			case 'manager': return 'admin' as const;
			case 'staff': return 'editor' as const; // Staff can edit schedules
			default: return 'viewer' as const;
		}
	})());

	// Computed values
	let weekDates = $derived(getWeekDates(schedule.weekStartDate));
	// Note: Weekly summary calculations moved to server-side RPC functions
	// UI components are now pure display layers without business logic
	let basicSummary = $derived({
		totalShifts: schedule.shifts.length,
		totalEmployees: schedule.employees.filter(e => e.isActive).length,
		totalVenues: schedule.venues.length
	});

	// Responsive handling
	let screenWidth = $state(0);

	onMount(() => {
		const updateScreenSize = () => {
			screenWidth = window.innerWidth;
			uiState.isMobileView = screenWidth < 768;
		};

		updateScreenSize();
		window.addEventListener('resize', updateScreenSize);

		return () => {
			window.removeEventListener('resize', updateScreenSize);
		};
	});

	// Week navigation
	async function navigateWeek(direction: 'prev' | 'next') {
		const newDate = new Date(schedule.weekStartDate);
		newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
		schedule.weekStartDate = newDate;
		uiState.selectedWeek = newDate;

		// Refresh shifts for the new week
		await refreshShiftsForCurrentWeek();
	}

	async function goToCurrentWeek() {
		const today = new Date();
		const weekStart = new Date(today);
		const day = weekStart.getDay();
		const diff = weekStart.getDate() - day + (day === 0 ? -6 : 1);
		weekStart.setDate(diff);

		schedule.weekStartDate = weekStart;
		uiState.selectedWeek = weekStart;

		// Refresh shifts for the current week
		await refreshShiftsForCurrentWeek();
	}

	// Modal handlers
	function openShiftModal(shift: Shift | null = null, employee: Employee | null = null) {
		uiState.shiftModal = {
			isOpen: true,
			mode: shift ? 'edit' : 'create',
			shift,
			employee
		};
	}

	function closeShiftModal() {
		uiState.shiftModal.isOpen = false;
	}

	function openEmployeeModal(employee: Employee | null = null) {
		uiState.employeeModal = {
			isOpen: true,
			mode: employee ? 'edit' : 'create',
			shift: null,
			employee
		};
	}

	function closeEmployeeModal() {
		uiState.employeeModal.isOpen = false;
	}

	// Data refresh function
	async function refreshShiftsForCurrentWeek() {
		try {
			console.log('🔄 ScheduleView.refreshShiftsForCurrentWeek: Starting refresh for week:', schedule.weekStartDate);
			console.log('🔄 ScheduleView.refreshShiftsForCurrentWeek: Current shifts before refresh:', schedule.shifts.length);

			const freshShifts = await scheduleService.fetchShiftsForWeek(schedule.weekStartDate);

			console.log('🔄 ScheduleView.refreshShiftsForCurrentWeek: Fresh shifts received:', freshShifts.length);
			console.log('🔄 ScheduleView.refreshShiftsForCurrentWeek: Fresh shifts data:', freshShifts);

			schedule.shifts = freshShifts;

			console.log('✅ ScheduleView.refreshShiftsForCurrentWeek: Shifts updated in schedule, new total:', schedule.shifts.length);
			console.log('✅ ScheduleView.refreshShiftsForCurrentWeek: Updated schedule.shifts:', schedule.shifts);
		} catch (error) {
			console.error('❌ ScheduleView.refreshShiftsForCurrentWeek: Failed to refresh shifts:', error);
		}
	}

	// Shift operations
	async function handleShiftSave(shift: Shift) {
		console.log('🔄 ScheduleView: Handling shift save:', shift);

		try {
			let savedShift: Shift;

			if (uiState.shiftModal.mode === 'create') {
				console.log('🔄 ScheduleView: Creating new shift');

				// Create new shift in database
				savedShift = await shiftService.createShift({
					employeeId: shift.employeeId,
					venueId: shift.venueId || shift.locationId, // Use locationId if venueId is undefined
					date: shift.date.toISOString().split('T')[0], // Convert Date to ISO date string
					startTime: shift.startTime,
					endTime: shift.endTime,
					totalHours: shift.totalHours || shift.hoursWorked,
					dailyRate: shift.dailyRate,
					isPaid: shift.isPaid,
					advanceDeduction: shift.advanceDeduction,
					notes: shift.notes
				});

				console.log('✅ ScheduleView: Shift created successfully:', savedShift);
			} else {
				console.log('🔄 ScheduleView: Updating existing shift');

				// Update existing shift in database
				savedShift = await shiftService.updateShift(shift.id, {
					employeeId: shift.employeeId,
					venueId: shift.venueId || shift.locationId, // Use locationId if venueId is undefined
					date: shift.date.toISOString().split('T')[0], // Convert Date to ISO date string
					startTime: shift.startTime,
					endTime: shift.endTime,
					totalHours: shift.totalHours || shift.hoursWorked,
					dailyRate: shift.dailyRate,
					isPaid: shift.isPaid,
					advanceDeduction: shift.advanceDeduction,
					notes: shift.notes
				});

				console.log('✅ ScheduleView: Shift updated successfully:', savedShift);
			}

			// Refresh shifts from database to get current state
			await refreshShiftsForCurrentWeek();

			closeShiftModal();
		} catch (error) {
			console.error('❌ ScheduleView: Failed to save shift:', error);
			// TODO: Show user-friendly error message
			alert(`Failed to save shift: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async function handleShiftDelete(shiftId: string) {
		try {
			console.log('🔄 ScheduleView: Deleting shift:', shiftId);

			// Delete shift from database
			await shiftService.deleteShift(shiftId);

			console.log('✅ ScheduleView: Shift deleted successfully');

			// Refresh shifts from database to get current state
			await refreshShiftsForCurrentWeek();

			closeShiftModal();
		} catch (error) {
			console.error('❌ ScheduleView: Failed to delete shift:', error);
			// TODO: Show user-friendly error message
			alert(`Failed to delete shift: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Employee operations
	function handleEmployeeSave(employee: Employee) {
		if (uiState.employeeModal.mode === 'create') {
			schedule.employees = [...schedule.employees, employee];
		} else {
			const index = schedule.employees.findIndex((e) => e.id === employee.id);
			if (index !== -1) {
				schedule.employees[index] = employee;
			}
		}
		closeEmployeeModal();
	}

	function handleEmployeeDelete(employeeId: string) {
		// Remove employee and unassign their shifts
		schedule.employees = schedule.employees.filter((e) => e.id !== employeeId);
		schedule.shifts = schedule.shifts.map((shift) =>
			shift.employeeId === employeeId ? { ...shift, employeeId: null } : shift
		);
		closeEmployeeModal();
	}

	// Bulk operations
	function markAllAsPaid() {
		schedule.shifts = schedule.shifts.map((shift) => ({ ...shift, isPaid: true }));
	}

	function exportSchedule(format: 'csv' | 'pdf', venueId?: string) {
		// TODO: Implement export functionality
		console.log('Exporting schedule:', { format, venueId, week: schedule.weekStartDate });
	}

	// Enhanced scheduling functions
	async function loadEnhancedSchedule() {
		if (!organizationId) {
			console.warn('⚠️ ScheduleView: No organization ID available for enhanced mode');
			return;
		}

		isLoadingEnhanced = true;
		try {
			console.log('🔄 ScheduleView: Loading enhanced schedule for organization:', organizationId);

			enhancedSchedule = await enhancedScheduleService.loadEnhancedWeeklySchedule(
				organizationId,
				schedule.weekStartDate
			);

			isEnhancedMode = true;
			console.log('✅ ScheduleView: Enhanced schedule loaded successfully');
		} catch (error) {
			console.error('❌ ScheduleView: Failed to load enhanced schedule:', error);
			// Fall back to regular mode
			isEnhancedMode = false;
		} finally {
			isLoadingEnhanced = false;
		}
	}

	function toggleEnhancedMode() {
		if (isEnhancedMode) {
			isEnhancedMode = false;
			enhancedSchedule = null;
		} else {
			loadEnhancedSchedule();
		}
	}

	// Leave management handlers
	function openLeaveModal(leaveRequest: LeaveRequest | null = null) {
		uiState.leaveModal = {
			isOpen: true,
			mode: leaveRequest ? 'edit' : 'create',
			shift: null,
			employee: null
		};
	}

	function closeLeaveModal() {
		uiState.leaveModal.isOpen = false;
	}

	function handleLeaveRequestClick(leaveRequest: LeaveRequest) {
		openLeaveModal(leaveRequest);
	}

	function handleUnavailabilityClick(unavailability: EmployeeUnavailability) {
		// TODO: Open unavailability modal
		console.log('Unavailability clicked:', unavailability);
	}

	function handleConflictDetected(conflicts: ShiftConflict[]) {
		console.log('Conflicts detected:', conflicts);
		// TODO: Show conflict notification or auto-open reassignment panel
	}

	function handleLeaveConflict(event: CustomEvent<{ leaveRequest: LeaveRequest }>) {
		const { leaveRequest } = event.detail;

		// Find affected shifts
		const affectedShifts = schedule.shifts.filter(shift =>
			shift.employeeId === leaveRequest.employeeId &&
			shift.date >= leaveRequest.startDate &&
			shift.date <= leaveRequest.endDate
		);

		if (affectedShifts.length > 0) {
			// Open reassignment panel
			uiState.reassignmentPanel = {
				isOpen: true,
				affectedShifts,
				leaveRequest,
				suggestedReplacements: [] // Will be loaded by the panel
			};
		}
	}

	function closeReassignmentPanel() {
		uiState.reassignmentPanel.isOpen = false;
	}

	function handleDataRefresh() {
		// Refresh both regular and enhanced schedule data
		refreshShiftsForCurrentWeek();
		if (isEnhancedMode) {
			loadEnhancedSchedule();
		}
	}

	// Authentication
	async function handleSignOut() {
		try {
			console.log('🔄 ScheduleView: Signing out...');
			await signOut();
			console.log('✅ ScheduleView: Sign out successful');
		} catch (error) {
			console.error('❌ ScheduleView: Sign out failed:', error);
		}
	}
</script>

<div class="flex h-screen bg-gray-50">
	<!-- Employee Panel -->
	<div
		class="flex-shrink-0 {uiState.isEmployeePanelCollapsed
			? 'w-16'
			: 'w-80'} transition-all duration-300 {uiState.isMobileView ? 'hidden' : 'block'}"
	>
		<EmployeePanel
			employees={schedule.employees}
			venues={schedule.venues}
			isCollapsed={uiState.isEmployeePanelCollapsed}
			onToggleCollapse={() =>
				(uiState.isEmployeePanelCollapsed = !uiState.isEmployeePanelCollapsed)}
			onEmployeeEdit={openEmployeeModal}
			onEmployeeAdd={() => openEmployeeModal()}
		/>
	</div>

	<!-- Main Content -->
	<div class="flex flex-1 flex-col overflow-hidden">
		<!-- Header -->
		<header class="border-b border-gray-200 bg-white px-6 py-4">
			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-4">
					<h1 class="text-2xl font-bold text-gray-900">Work Scheduler</h1>
					<OrganizationSwitcher />

					<!-- Tab Navigation -->
					<div class="flex rounded-lg bg-gray-100 p-1">
						<button
							onclick={() => activeTab = 'schedule'}
							class="rounded-md px-3 py-1 text-sm font-medium transition-colors {activeTab === 'schedule'
								? 'bg-white text-gray-900 shadow-sm'
								: 'text-gray-500 hover:text-gray-700'}"
						>
							Schedule
						</button>
						<button
							onclick={() => activeTab = 'leave'}
							class="rounded-md px-3 py-1 text-sm font-medium transition-colors {activeTab === 'leave'
								? 'bg-white text-gray-900 shadow-sm'
								: 'text-gray-500 hover:text-gray-700'}"
						>
							Leave Management
							{#if organizationId}
								<!-- TODO: Add pending leave count badge -->
							{/if}
						</button>
					</div>

					{#if activeTab === 'schedule'}
						<button
							onclick={goToCurrentWeek}
							class="rounded-md bg-blue-100 px-3 py-1 text-sm text-blue-700 transition-colors hover:bg-blue-200"
						>
							Today
						</button>

						<!-- Enhanced Mode Toggle -->
						<button
							onclick={toggleEnhancedMode}
							disabled={isLoadingEnhanced}
							class="rounded-md px-3 py-1 text-sm font-medium transition-colors {isEnhancedMode
								? 'bg-green-100 text-green-700 hover:bg-green-200'
								: 'bg-gray-100 text-gray-700 hover:bg-gray-200'} disabled:opacity-50"
							title={isEnhancedMode ? 'Switch to basic mode' : 'Enable enhanced features (leave integration, conflict detection)'}
						>
							{#if isLoadingEnhanced}
								<svg class="mr-1 h-3 w-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
								</svg>
							{:else}
								<svg class="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
								</svg>
							{/if}
							{isEnhancedMode ? 'Enhanced' : 'Basic'}
						</button>

						{#if isEnhancedMode}
							<!-- Availability Overlay Toggle -->
							<button
								onclick={() => uiState.showUnavailabilityOverlay = !uiState.showUnavailabilityOverlay}
								class="rounded-md px-3 py-1 text-sm font-medium transition-colors {uiState.showUnavailabilityOverlay
									? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
									: 'bg-gray-100 text-gray-700 hover:bg-gray-200'}"
								title="Toggle availability overlay"
							>
								<svg class="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
								</svg>
								Availability
							</button>
						{/if}
					{/if}
				</div>

				<div class="flex items-center space-x-3">
					<!-- Management Buttons -->
					<button
						onclick={() => uiState.showLocationManagement = true}
						class="rounded-md bg-green-100 px-3 py-1 text-sm text-green-700 transition-colors hover:bg-green-200"
						title="Manage Locations"
					>
						Locations
					</button>
					<button
						onclick={() => uiState.showPaymentManagement = true}
						class="rounded-md bg-purple-100 px-3 py-1 text-sm text-purple-700 transition-colors hover:bg-purple-200"
						title="Manage Payments"
					>
						Payments
					</button>
					<button
						onclick={handleSignOut}
						class="rounded-md bg-red-100 px-3 py-1 text-sm text-red-700 transition-colors hover:bg-red-200"
						title="Sign out"
					>
						Sign Out
					</button>
				</div>

				<div class="flex items-center space-x-4">
					<!-- Week Navigation -->
					<div class="flex items-center space-x-2">
						<button
							onclick={() => navigateWeek('prev')}
							class="rounded-md p-2 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-700"
							aria-label="Previous week"
						>
							<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M15 19l-7-7 7-7"
								/>
							</svg>
						</button>

						<div class="min-w-[200px] text-center">
							<div class="text-lg font-semibold text-gray-900">
								{formatDateDisplay(weekDates[0])} - {formatDateDisplay(weekDates[6])}
							</div>
							<div class="text-sm text-gray-500">
								Week of {schedule.weekStartDate.toLocaleDateString()}
							</div>
						</div>

						<button
							onclick={() => navigateWeek('next')}
							class="rounded-md p-2 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-700"
							aria-label="Next week"
						>
							<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M9 5l7 7-7 7"
								/>
							</svg>
						</button>
					</div>

					<!-- Quick Stats -->
					<div class="hidden items-center space-x-4 text-sm md:flex">
						<div class="text-center">
							<div class="font-semibold text-gray-900">
								{basicSummary.totalEmployees}
							</div>
							<div class="text-gray-500">Employees</div>
						</div>
						<div class="text-center">
							<div class="font-semibold text-gray-900">{basicSummary.totalShifts}</div>
							<div class="text-gray-500">Shifts</div>
						</div>
						<div class="text-center">
							<div class="font-semibold text-red-600">{schedule.shifts.filter(s => !s.employeeId).length}</div>
							<div class="text-gray-500">Unassigned</div>
						</div>
					</div>
				</div>
			</div>
		</header>

		<!-- Main Content Area -->
		<div class="flex-1 overflow-auto">
			{#if activeTab === 'schedule'}
				<!-- Schedule Grid -->
				{#if isEnhancedMode && enhancedSchedule}
					<EnhancedScheduleGrid
						schedule={enhancedSchedule}
						{weekDates}
						{uiState}
						{organizationId}
						onShiftClick={openShiftModal}
						onShiftCreate={openShiftModal}
						onLeaveRequestClick={handleLeaveRequestClick}
						onUnavailabilityClick={handleUnavailabilityClick}
						onConflictDetected={handleConflictDetected}
					/>
				{:else}
					<ScheduleGrid
						{schedule}
						{weekDates}
						{uiState}
						isToday={(date) => {
							const today = new Date();
							return date.toDateString() === today.toDateString();
						}}
						onShiftClick={openShiftModal}
						onShiftCreate={openShiftModal}
					/>
				{/if}
			{:else if activeTab === 'leave'}
				<!-- Leave Management Dashboard -->
				{#if organizationId}
					<LeaveManagementDashboard
						{organizationId}
						employees={schedule.employees}
						{currentUserId}
						{userRole}
						on:refresh={handleDataRefresh}
						on:conflictDetected={handleLeaveConflict}
					/>
				{:else}
					<div class="flex items-center justify-center h-full">
						<div class="text-center">
							<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
							</svg>
							<h3 class="mt-2 text-sm font-medium text-gray-900">Organization Required</h3>
							<p class="mt-1 text-sm text-gray-500">
								Please select an organization to access leave management features.
							</p>
						</div>
					</div>
				{/if}
			{/if}
		</div>

		<!-- Footer Toolbar -->
		<!-- Note: FooterToolbar should be updated to use server-side calculations -->
		<!-- For now, passing basic summary to maintain functionality -->
		<FooterToolbar
			weeklySummary={{
				totalHours: 0, // Will be calculated server-side
				totalPay: 0, // Will be calculated server-side
				paidAmount: 0, // Will be calculated server-side
				unpaidAmount: 0, // Will be calculated server-side
				advanceDeductions: 0, // Will be calculated server-side
				unassignedShifts: schedule.shifts.filter(s => !s.employeeId).length,
				employeeSummaries: [],
				venueSummaries: []
			}}
			venues={schedule.venues}
			onMarkAllPaid={markAllAsPaid}
			onExport={exportSchedule}
		/>
	</div>
</div>

<!-- Modals -->
{#if uiState.shiftModal.isOpen}
	<ShiftModal
		shift={uiState.shiftModal.shift}
		employees={schedule.employees}
		venues={schedule.venues}
		mode={uiState.shiftModal.mode}
		employee={uiState.shiftModal.employee}
		date={uiState.shiftModal.shift?.date}
		onSave={handleShiftSave}
		onDelete={handleShiftDelete}
		onClose={closeShiftModal}
	/>
{/if}

{#if uiState.employeeModal.isOpen}
	<EmployeeModal
		employee={uiState.employeeModal.employee}
		mode={uiState.employeeModal.mode}
		onSave={handleEmployeeSave}
		onDelete={handleEmployeeDelete}
		onClose={closeEmployeeModal}
	/>
{/if}

<!-- Location Management Modal -->
<LocationManagement
	isOpen={uiState.showLocationManagement}
	onClose={() => uiState.showLocationManagement = false}
/>

<!-- Payment Management Modal -->
<PaymentManagement
	isOpen={uiState.showPaymentManagement}
	onClose={() => uiState.showPaymentManagement = false}
/>

<!-- Leave Request Modal -->
{#if uiState.leaveModal.isOpen}
	<LeaveRequestModal
		leaveRequest={null}
		employees={schedule.employees}
		{currentUserId}
		{organizationId}
		mode="create"
		onClose={closeLeaveModal}
	/>
{/if}

<!-- Shift Reassignment Panel -->
{#if uiState.reassignmentPanel.isOpen}
	<ShiftReassignmentPanel
		panelState={uiState.reassignmentPanel}
		{organizationId}
		onClose={closeReassignmentPanel}
	/>
{/if}

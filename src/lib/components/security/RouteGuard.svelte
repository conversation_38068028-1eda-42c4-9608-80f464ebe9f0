<!--
	Route Guard Component
	Client-side route protection with comprehensive security validation
	Following Code Complete principles: Security-first, clear user feedback
-->

<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { validateSecurity, type SecurityValidationResult } from '$lib/security/TokenValidationService.js';
	import { sessionManager, isSessionActive } from '$lib/security/SessionManager.js';
	import { LoadingSpinner } from '$lib/components/ui/index.js';

	// ============================================================================
	// PROPS
	// ============================================================================

	interface RouteGuardProps {
		requireAuth?: boolean;
		requireOrganization?: boolean;
		requiredRole?: 'viewer' | 'editor' | 'admin' | 'superadmin';
		redirectTo?: string;
		children?: any;
	}

	let {
		requireAuth = true,
		requireOrganization = false,
		requiredRole = 'viewer',
		redirectTo = '/',
		children
	}: RouteGuardProps = $props();

	// ============================================================================
	// STATE
	// ============================================================================

	let isValidating = $state(true);
	let validationResult: SecurityValidationResult | null = $state(null);
	let error = $state<string | null>(null);
	let retryCount = $state(0);
	let maxRetries = 3;

	// ============================================================================
	// REACTIVE LOGIC
	// ============================================================================

	/**
	 * Check if current validation meets requirements
	 * Following Code Complete: Clear business rule validation
	 */
	const isAccessAllowed = $derived(() => {
		if (!validationResult) return false;

		// Check authentication requirement
		if (requireAuth && !validationResult.isValid) {
			return false;
		}

		// Check organization requirement
		if (requireOrganization && !validationResult.organizationContext.organizationId) {
			return false;
		}

		// Check role requirement
		if (requireOrganization && requiredRole) {
			const hasRole = checkRole(validationResult.organizationContext, requiredRole);
			if (!hasRole) {
				return false;
			}
		}

		return true;
	});

	/**
	 * Get user-friendly error message
	 * Following Code Complete: Clear user communication
	 */
	const errorMessage = $derived(() => {
		if (!validationResult) {
			return 'Checking access permissions...';
		}

		if (requireAuth && !validationResult.isValid) {
			return 'Please sign in to access this page.';
		}

		if (requireOrganization && !validationResult.organizationContext.organizationId) {
			return 'Organization access required. Please contact your administrator.';
		}

		if (requireOrganization && requiredRole) {
			const hasRole = checkRole(validationResult.organizationContext, requiredRole);
			if (!hasRole) {
				return `Insufficient permissions. ${requiredRole} role required.`;
			}
		}

		return error || 'Access denied.';
	});

	// ============================================================================
	// LIFECYCLE
	// ============================================================================

	onMount(async () => {
		console.log('🔒 RouteGuard: Mounting for route:', $page.url.pathname);
		await validateAccess();

		// Listen for session changes
		sessionManager.addEventListener(handleSessionEvent);
	});

	onDestroy(() => {
		sessionManager.removeEventListener(handleSessionEvent);
	});

	// ============================================================================
	// VALIDATION LOGIC
	// ============================================================================

	/**
	 * Validate access permissions
	 * Following Code Complete: Comprehensive validation, error handling
	 */
	async function validateAccess(): Promise<void> {
		isValidating = true;
		error = null;

		try {
			console.log('🔒 RouteGuard: Validating access...');
			
			const result = await validateSecurity();
			validationResult = result;

			if (!isAccessAllowed) {
				await handleAccessDenied();
			} else {
				console.log('✅ RouteGuard: Access granted');
			}

		} catch (err) {
			const errorMsg = err instanceof Error ? err.message : 'Validation failed';
			console.error('🔒 RouteGuard: Validation error:', errorMsg);
			error = errorMsg;

			// Retry logic for transient errors
			if (retryCount < maxRetries) {
				retryCount++;
				console.log(`🔒 RouteGuard: Retrying validation (${retryCount}/${maxRetries})`);
				setTimeout(() => validateAccess(), 1000 * retryCount);
				return;
			}

			await handleAccessDenied();
		} finally {
			isValidating = false;
		}
	}

	/**
	 * Handle access denied scenarios
	 * Following Code Complete: Clear error handling, user guidance
	 */
	async function handleAccessDenied(): Promise<void> {
		console.log('🔒 RouteGuard: Access denied for route:', $page.url.pathname);

		// Determine appropriate redirect
		let redirectUrl = redirectTo;

		if (requireAuth && (!validationResult || !validationResult.isValid)) {
			// Need authentication - redirect to login with return URL
			const returnUrl = encodeURIComponent($page.url.pathname + $page.url.search);
			redirectUrl = `/?returnUrl=${returnUrl}`;
		} else if (requireOrganization && validationResult && !validationResult.organizationContext.organizationId) {
			// Need organization setup
			redirectUrl = '/setup/organization';
		}

		// Add delay to show error message briefly
		setTimeout(() => {
			goto(redirectUrl, { replaceState: true });
		}, 2000);
	}

	/**
	 * Handle session events
	 * Following Code Complete: Event-driven architecture, reactive updates
	 */
	function handleSessionEvent(event: any): void {
		console.log('🔒 RouteGuard: Session event:', event.type);

		switch (event.type) {
			case 'session_end':
			case 'session_expired':
				if (requireAuth) {
					validateAccess();
				}
				break;

			case 'session_start':
			case 'session_refresh':
				validateAccess();
				break;
		}
	}

	/**
	 * Check if user has required role
	 * Following Code Complete: Business rule validation, role hierarchy
	 */
	function checkRole(
		context: { organizationId: string | null; userRole: string | null; membershipActive: boolean },
		required: string
	): boolean {
		if (!context.organizationId || !context.membershipActive) {
			return false;
		}

		const roleHierarchy = ['viewer', 'editor', 'admin', 'superadmin'];
		const userRoleIndex = roleHierarchy.indexOf(context.userRole || '');
		const requiredRoleIndex = roleHierarchy.indexOf(required);

		return userRoleIndex >= requiredRoleIndex;
	}

	/**
	 * Retry validation manually
	 * Following Code Complete: User control, error recovery
	 */
	function retryValidation(): void {
		retryCount = 0;
		validateAccess();
	}
</script>

<!-- Route Guard UI -->
{#if isValidating}
	<!-- Loading State -->
	<div class="min-h-screen bg-gray-50 flex items-center justify-center">
		<div class="text-center">
			<LoadingSpinner size="lg" />
			<p class="mt-4 text-gray-600">Verifying access permissions...</p>
		</div>
	</div>
{:else if !isAccessAllowed}
	<!-- Access Denied State -->
	<div class="min-h-screen bg-gray-50 flex items-center justify-center">
		<div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
			<!-- Error Icon -->
			<div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
				<svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
				</svg>
			</div>

			<!-- Error Message -->
			<h3 class="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
			<p class="text-sm text-gray-600 mb-6">{errorMessage}</p>

			<!-- Action Buttons -->
			<div class="space-y-3">
				{#if error && retryCount < maxRetries}
					<button
						onclick={retryValidation}
						class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
					>
						Try Again
					</button>
				{/if}

				<button
					onclick={() => goto(redirectTo)}
					class="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
				>
					Go to Home
				</button>
			</div>

			<!-- Additional Info -->
			{#if validationResult}
				<div class="mt-6 pt-6 border-t border-gray-200">
					<details class="text-left">
						<summary class="text-sm text-gray-500 cursor-pointer hover:text-gray-700">
							Technical Details
						</summary>
						<div class="mt-2 text-xs text-gray-400 space-y-1">
							<p>Route: {$page.url.pathname}</p>
							<p>Authenticated: {validationResult.isValid ? 'Yes' : 'No'}</p>
							{#if validationResult.organizationContext.organizationId}
								<p>Organization: {validationResult.organizationContext.organizationId}</p>
								<p>Role: {validationResult.organizationContext.userRole || 'None'}</p>
							{/if}
							<p>Required Role: {requiredRole}</p>
						</div>
					</details>
				</div>
			{/if}
		</div>
	</div>
{:else}
	<!-- Access Granted - Render Children -->
	{@render children?.()}
{/if}

<style>
	/* Ensure full height coverage */
	:global(html, body) {
		height: 100%;
	}
</style>

<script lang="ts">
	/**
	 * Test Page for Week Calendar Picker and Duplicate Week Bug Fix
	 * This page demonstrates both enhancements:
	 * 1. Interactive calendar widget for week selection
	 * 2. Fixed duplicate week functionality with corrected date calculations
	 */
	
	import { onMount } from 'svelte';
	import { formatWeekRange, getWeekStart, getWeekDates } from '$lib/utils.js';
	import { Button } from '$lib/components/ui/index.js';
	import WeekCalendarPicker from '$lib/components/WeekCalendarPicker.svelte';
	import DuplicateWeekModal from '$lib/components/DuplicateWeekModal.svelte';
	import type { WeeklySchedule, DuplicateWeekRequest, DuplicateWeekResult, Shift, Employee, Venue } from '$lib/types.js';

	// Test state
	let selectedWeek = $state(getWeekStart(new Date()));
	let isCalendarPickerOpen = $state(false);
	let isDuplicateModalOpen = $state(false);
	let testResults = $state<DuplicateWeekResult | null>(null);

	// Test schedule data
	let testSchedule: WeeklySchedule = $state({
		weekStartDate: selectedWeek,
		employees: [
			{
				id: 'emp-1',
				name: '<PERSON>',
				fullName: '<PERSON>e',
				role: 'FOH',
				defaultDailyRate: 120,
				defaultDailyWorkingHours: 8,
				defaultHourlyRate: 15,
				isActive: true,
				active: true,
				organizationId: 'test-org',
				createdAt: new Date(),
				updatedAt: new Date()
			},
			{
				id: 'emp-2',
				name: 'Jane Smith',
				fullName: 'Jane Smith',
				role: 'BOH',
				defaultDailyRate: 140,
				defaultDailyWorkingHours: 8,
				defaultHourlyRate: 17.5,
				isActive: true,
				active: true,
				organizationId: 'test-org',
				createdAt: new Date(),
				updatedAt: new Date()
			}
		] as Employee[],
		venues: [
			{
				id: 'venue-1',
				name: 'Main Restaurant',
				color: '#3B82F6',
				address: '123 Main St'
			},
			{
				id: 'venue-2',
				name: 'Bar Area',
				color: '#10B981',
				address: '123 Main St'
			}
		] as Venue[],
		shifts: []
	});

	// Calendar picker functions
	function openCalendarPicker() {
		isCalendarPickerOpen = true;
	}

	function closeCalendarPicker() {
		isCalendarPickerOpen = false;
	}

	function handleWeekSelect(weekStart: Date) {
		selectedWeek = weekStart;
		testSchedule.weekStartDate = weekStart;
		generateTestShifts();
		closeCalendarPicker();
	}

	// Week navigation
	function navigateWeek(direction: 'prev' | 'next') {
		const newDate = new Date(selectedWeek);
		newDate.setDate(selectedWeek.getDate() + (direction === 'next' ? 7 : -7));
		selectedWeek = newDate;
		testSchedule.weekStartDate = newDate;
		generateTestShifts();
	}

	function goToCurrentWeek() {
		const currentWeek = getWeekStart(new Date());
		selectedWeek = currentWeek;
		testSchedule.weekStartDate = currentWeek;
		generateTestShifts();
	}

	// Generate test shifts for the selected week
	function generateTestShifts() {
		const weekDates = getWeekDates(selectedWeek);
		const shifts: Shift[] = [];

		// Create test shifts with specific patterns to test the bug fix
		weekDates.forEach((date, dayIndex) => {
			// Skip weekends for this test
			if (dayIndex === 5 || dayIndex === 6) return;

			const normalizedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

			// John Doe - Morning shifts (Monday, Wednesday, Friday)
			if (dayIndex % 2 === 0) {
				shifts.push({
					id: `shift-john-${dayIndex}`,
					employeeId: 'emp-1',
					locationId: 'venue-1',
					venueId: 'venue-1',
					date: normalizedDate,
					startTime: '09:00',
					endTime: '17:00',
					hoursWorked: 8,
					totalHours: 8,
					dailyRate: 120,
					isPaid: false,
					advanceDeduction: 0,
					notes: `Morning shift for ${normalizedDate.toDateString()}`,
					createdAt: new Date(),
					updatedAt: new Date()
				});
			}

			// Jane Smith - Evening shifts (Tuesday, Thursday)
			if (dayIndex % 2 === 1) {
				shifts.push({
					id: `shift-jane-${dayIndex}`,
					employeeId: 'emp-2',
					locationId: 'venue-2',
					venueId: 'venue-2',
					date: normalizedDate,
					startTime: '17:00',
					endTime: '23:00',
					hoursWorked: 6,
					totalHours: 6,
					dailyRate: 105,
					isPaid: false,
					advanceDeduction: 0,
					notes: `Evening shift for ${normalizedDate.toDateString()}`,
					createdAt: new Date(),
					updatedAt: new Date()
				});
			}
		});

		testSchedule.shifts = shifts;
		console.log('✅ Test: Generated test shifts for week:', formatWeekRange(selectedWeek), shifts);
	}

	// Duplicate week modal functions
	function openDuplicateModal() {
		isDuplicateModalOpen = true;
		testResults = null;
	}

	function closeDuplicateModal() {
		isDuplicateModalOpen = false;
	}

	async function handleDuplicateWeek(request: DuplicateWeekRequest): Promise<DuplicateWeekResult> {
		try {
			console.log('🔄 Test: Simulating duplicate week with FIXED date calculation:', request);

			// Simulate the FIXED duplication logic
			const sourceWeekShifts = testSchedule.shifts;
			const totalShifts = sourceWeekShifts.length * request.targetWeekStarts.length;

			// Simulate the bug fix: proper date calculation
			const duplicatedShifts: any[] = [];
			request.targetWeekStarts.forEach(targetWeekStart => {
				sourceWeekShifts.forEach(sourceShift => {
					// FIXED: Calculate day offset correctly
					const sourceDate = sourceShift.date instanceof Date ? sourceShift.date : new Date(sourceShift.date);
					const sourceWeekStart = request.sourceWeekStart;
					
					// Calculate the number of days from source week start to shift date
					const dayOffset = Math.floor((sourceDate.getTime() - sourceWeekStart.getTime()) / (24 * 60 * 60 * 1000));
					
					// Add the same offset to target week start
					const targetDate = new Date(targetWeekStart);
					targetDate.setDate(targetWeekStart.getDate() + dayOffset);

					duplicatedShifts.push({
						...sourceShift,
						id: `duplicated-${sourceShift.id}-${targetWeekStart.getTime()}`,
						date: targetDate,
						notes: `${sourceShift.notes} (Duplicated with FIXED calculation)`
					});
				});
			});

			const mockResult: DuplicateWeekResult = {
				success: true,
				totalShiftsDuplicated: totalShifts,
				conflictsDetected: 0,
				conflictsResolved: 0,
				targetWeeksProcessed: request.targetWeekStarts.length,
				message: `Successfully duplicated ${sourceWeekShifts.length} shifts to ${request.targetWeekStarts.length} week(s) using FIXED date calculation`,
				details: request.targetWeekStarts.map(targetWeek => ({
					targetWeekStart: targetWeek.toISOString().split('T')[0],
					shiftsDuplicated: sourceWeekShifts.length,
					conflictsFound: [],
					conflictsResolved: 0
				}))
			};

			// Simulate network delay
			await new Promise(resolve => setTimeout(resolve, 1500));

			testResults = mockResult;
			console.log('✅ Test: Mock duplication completed with FIXED logic:', mockResult);
			console.log('🔍 Test: Duplicated shifts with correct dates:', duplicatedShifts);

			return mockResult;
		} catch (error) {
			console.error('❌ Test: Duplication failed:', error);
			throw error;
		}
	}

	// Computed values
	let weekDates = $derived(getWeekDates(selectedWeek));
	let shiftsThisWeek = $derived(testSchedule.shifts.length);

	onMount(() => {
		generateTestShifts();
	});
</script>

<div class="container mx-auto p-6 max-w-6xl">
	<div class="mb-8">
		<h1 class="text-3xl font-bold text-gray-900 mb-4">Calendar Picker & Duplicate Week Bug Fix Test</h1>
		<p class="text-gray-600 mb-6">
			This page demonstrates two critical enhancements to the restaurant scheduling system:
		</p>
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
				<h3 class="text-lg font-semibold text-blue-900 mb-2">1. Interactive Calendar Picker</h3>
				<p class="text-sm text-blue-800">
					Click on the week display below to open an interactive calendar widget that allows you to select any week (past or future) with visual week boundaries and navigation.
				</p>
			</div>
			<div class="bg-green-50 border border-green-200 rounded-lg p-4">
				<h3 class="text-lg font-semibold text-green-900 mb-2">2. Fixed Duplicate Week Bug</h3>
				<p class="text-sm text-green-800">
					The duplicate week functionality now uses corrected date calculation logic that properly handles day offsets within weeks, preventing incorrect shift duplication.
				</p>
			</div>
		</div>
	</div>

	<!-- Week Navigation with Calendar Picker -->
	<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
		<div class="flex items-center justify-between mb-4">
			<h2 class="text-xl font-semibold text-gray-900">Week Selection Test</h2>
			<div class="flex items-center space-x-2 relative">
				<button
					onclick={() => navigateWeek('prev')}
					class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
					aria-label="Previous week"
				>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
					</svg>
				</button>

				<!-- Week Display with Calendar Picker -->
				<div class="relative">
					<button
						onclick={openCalendarPicker}
						class="text-center px-4 py-3 hover:bg-gray-100 rounded-lg transition-colors group border-2 border-dashed border-gray-300 hover:border-blue-400"
						aria-label="Select week from calendar"
					>
						<div class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
							{formatWeekRange(selectedWeek)}
						</div>
						<div class="text-xs text-gray-500 group-hover:text-blue-500 transition-colors">
							📅 Click to open calendar picker
						</div>
					</button>

					<!-- Calendar Picker -->
					<WeekCalendarPicker
						selectedWeek={selectedWeek}
						isOpen={isCalendarPickerOpen}
						onWeekSelect={handleWeekSelect}
						onClose={closeCalendarPicker}
						position="bottom"
					/>
				</div>

				<button
					onclick={() => navigateWeek('next')}
					class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
					aria-label="Next week"
				>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
					</svg>
				</button>
			</div>
		</div>

		<!-- Week Overview -->
		<div class="grid grid-cols-7 gap-2 mb-4">
			{#each weekDates as date}
				{@const dayShifts = testSchedule.shifts.filter(s => {
					const shiftDate = s.date instanceof Date ? s.date : new Date(s.date);
					return shiftDate.toDateString() === date.toDateString();
				})}
				<div class="text-center p-3 bg-gray-50 rounded-lg">
					<div class="font-medium text-gray-900 mb-1">
						{date.toLocaleDateString('en-US', { weekday: 'short' })}
					</div>
					<div class="text-sm text-gray-600 mb-2">
						{date.getDate()}
					</div>
					<div class="text-xs text-blue-600">
						{dayShifts.length} shifts
					</div>
				</div>
			{/each}
		</div>

		<!-- Test Actions -->
		<div class="flex items-center justify-between">
			<div class="flex space-x-3">
				<Button onclick={goToCurrentWeek} variant="secondary" size="sm">
					Go to Current Week
				</Button>
				<Button onclick={generateTestShifts} variant="secondary" size="sm">
					Regenerate Test Shifts
				</Button>
			</div>
			<Button onclick={openDuplicateModal}>
				<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
				</svg>
				Test Fixed Duplicate Week
			</Button>
		</div>
	</div>

	<!-- Test Results -->
	{#if testResults}
		<div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
			<h3 class="text-lg font-semibold text-green-900 mb-4">✅ Bug Fix Test Results</h3>
			<div class="grid grid-cols-2 gap-4 text-sm mb-4">
				<div>
					<span class="text-green-700">Shifts Duplicated:</span>
					<div class="font-medium text-green-900">{testResults.totalShiftsDuplicated}</div>
				</div>
				<div>
					<span class="text-green-700">Weeks Processed:</span>
					<div class="font-medium text-green-900">{testResults.targetWeeksProcessed}</div>
				</div>
			</div>
			<p class="text-green-800 text-sm">{testResults.message}</p>
		</div>
	{/if}

	<!-- Feature Documentation -->
	<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
		<div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
			<h3 class="text-lg font-semibold text-blue-900 mb-4">Calendar Picker Features</h3>
			<ul class="text-sm text-blue-800 space-y-2">
				<li>• Interactive calendar with visual week boundaries</li>
				<li>• Month and year navigation</li>
				<li>• Current week highlighting (teal)</li>
				<li>• Selected week highlighting (blue)</li>
				<li>• Week range display on hover</li>
				<li>• Quick "Go to Current Week" action</li>
				<li>• Mobile responsive design</li>
			</ul>
		</div>
		<div class="bg-green-50 border border-green-200 rounded-lg p-6">
			<h3 class="text-lg font-semibold text-green-900 mb-4">Bug Fix Details</h3>
			<ul class="text-sm text-green-800 space-y-2">
				<li>• Fixed date calculation in RPC function</li>
				<li>• Corrected day offset handling</li>
				<li>• Proper week boundary calculations</li>
				<li>• Eliminated negative offset issues</li>
				<li>• Consistent shift duplication across dates</li>
				<li>• Maintains original shift timing</li>
			</ul>
		</div>
	</div>
</div>

<!-- Duplicate Week Modal -->
{#if isDuplicateModalOpen}
	<DuplicateWeekModal
		isOpen={isDuplicateModalOpen}
		currentWeekStart={selectedWeek}
		schedule={testSchedule}
		organizationId="test-org-123"
		onClose={closeDuplicateModal}
		onDuplicate={handleDuplicateWeek}
	/>
{/if}

/**
 * Root Layout Server Load Function
 * Provides server-side authentication context and security validation
 * Following Code Complete principles: Server-side data loading, security-first
 */

import type { LayoutServerLoad } from './$types';

/**
 * Server-side layout load function
 * Provides authentication context to all pages
 * Following Code Complete: Centralized security context, clear data flow
 */
export const load: LayoutServerLoad = async ({ locals, url }) => {
	const { security } = locals;
	
	// Return security context for client-side use
	return {
		security: {
			isAuthenticated: security.isAuthenticated,
			user: security.user ? {
				id: security.user.id,
				email: security.user.email,
				created_at: security.user.created_at,
				updated_at: security.user.updated_at
			} : null,
			organizationId: security.organizationId,
			userRole: security.userRole,
			sessionExpiry: security.sessionExpiry,
			needsRefresh: security.needsRefresh
		},
		currentPath: url.pathname
	};
};

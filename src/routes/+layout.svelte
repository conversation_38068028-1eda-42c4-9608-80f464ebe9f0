<script lang="ts">
	/**
	 * Root Layout Component
	 * Enhanced authentication with comprehensive security
	 * Following Code Complete principles: Security-first, clear separation of concerns
	 */

	import '../app.css';
	import { onMount } from 'svelte';
	import {
		isAuthenticated,
		isLoading,
		enhancedAuthState,
		initializeSecurity
	} from '$lib/security/index.js';
	import { AuthPage } from '$lib/components/auth/index.js';
	import { LoadingSpinner } from '$lib/components/ui/index.js';
	import RestaurantSetup from '$lib/components/auth/RestaurantSetup.svelte';
	import TallycaLayout from '$lib/components/TallycaLayout.svelte';

	let { children } = $props();

	// Initialize enhanced security system on app start
	onMount(async () => {
		try {
			await initializeSecurity();
		} catch (error) {
			console.error('Failed to initialize security:', error);
		}
	});
</script>

<!-- Authentication Guard -->
{#if $isLoading}
	<!-- Loading State -->
	<div class="min-h-screen bg-gray-50 flex items-center justify-center">
		<LoadingSpinner
			size="lg"
			message="Loading your account..."
		/>
	</div>
{:else if !$isAuthenticated}
	<!-- Authentication Required -->
	<AuthPage />
{:else if $enhancedAuthState.needsRestaurantSetup}
	<!-- Restaurant Setup Required -->
	<RestaurantSetup
		onComplete={async (_restaurantId) => {
			// Refresh auth state after restaurant creation
			await initializeSecurity();
		}}
	/>
{:else}
	<!-- Authenticated App with Tallyca Layout -->
	<TallycaLayout>
		{@render children()}
	</TallycaLayout>
{/if}

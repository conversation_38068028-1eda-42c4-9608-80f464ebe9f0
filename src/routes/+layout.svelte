<script lang="ts">
	/**
	 * Root Layout Component
	 * Handles authentication state and routing
	 * Following Code Complete principles: Clear separation of concerns
	 */

	import '../app.css';
	import { onMount } from 'svelte';
	import { initializeAuth, isAuthenticated, isLoading, authState } from '$lib/stores/auth.js';
	import { AuthPage } from '$lib/components/auth/index.js';
	import { LoadingSpinner } from '$lib/components/ui/index.js';
	import RestaurantSetup from '$lib/components/auth/RestaurantSetup.svelte';
	import TallycaLayout from '$lib/components/TallycaLayout.svelte';

	let { children } = $props();

	// Initialize authentication on app start
	onMount(() => {
		initializeAuth();
	});
</script>

<!-- Authentication Guard -->
{#if $isLoading}
	<!-- Loading State -->
	<div class="min-h-screen bg-gray-50 flex items-center justify-center">
		<LoadingSpinner
			size="lg"
			message="Loading your account..."
		/>
	</div>
{:else if !$isAuthenticated}
	<!-- Authentication Required -->
	<AuthPage />
{:else if $authState.needsRestaurantSetup}
	<!-- Restaurant Setup Required -->
	<RestaurantSetup
		onComplete={(restaurantId) => {
			// Refresh auth state after restaurant creation
			initializeAuth();
		}}
	/>
{:else}
	<!-- Authenticated App with Tallyca Layout -->
	<TallycaLayout>
		{@render children()}
	</TallycaLayout>
{/if}

<script lang="ts">
	import { onMount } from 'svelte';
	import { supabase } from '$lib/supabaseClient.js';

	let debugResults: Array<{message: string, type: string, timestamp: string}> = [];
	let loading = true;

	async function runDiagnostics() {
		debugResults = [];
		loading = true;

		try {
			// Test 1: Authentication
			addResult('1. Testing authentication...');
			const { data: { user }, error: authError } = await supabase.auth.getUser();
			if (authError) {
				addResult(`   ❌ Auth error: ${authError.message}`, 'error');
			} else if (user) {
				addResult(`   ✅ User authenticated: ${user.email}`, 'success');
			} else {
				addResult('   ⚠️  No user session', 'warning');
			}

			// Test 2: Organizations
			addResult('2. Checking organizations table...');
			const { data: orgs, error: orgError } = await supabase
				.from('organizations')
				.select('*');
			
			if (orgError) {
				addResult(`   ❌ Organizations error: ${orgError.message}`, 'error');
			} else {
				addResult(`   ✅ Found ${orgs.length} organizations`, 'success');
				if (orgs.length > 0) {
					addResult(`   📋 First org: ${orgs[0].name} (${orgs[0].id})`, 'info');
				}
			}

			// Test 3: Organization memberships
			addResult('3. Checking organization memberships...');
			const { data: memberships, error: memError } = await supabase
				.from('organization_memberships')
				.select('*');
			
			if (memError) {
				addResult(`   ❌ Memberships error: ${memError.message}`, 'error');
			} else {
				addResult(`   ✅ Found ${memberships.length} memberships`, 'success');
				if (memberships.length > 0 && user) {
					const userMemberships = memberships.filter(m => m.user_id === user.id);
					addResult(`   📋 User has ${userMemberships.length} memberships`, 'info');
					if (userMemberships.length > 0) {
						addResult(`   📋 First membership: ${userMemberships[0].role} in org ${userMemberships[0].organization_id}`, 'info');
					}
				}
			}

			// Test 4: Employees table
			addResult('4. Checking employees table directly...');
			const { data: employees, error: empError } = await supabase
				.from('employees')
				.select('*');
			
			if (empError) {
				addResult(`   ❌ Employees error: ${empError.message}`, 'error');
			} else {
				addResult(`   ✅ Found ${employees.length} employees in table`, 'success');
				if (employees.length > 0) {
					addResult(`   📋 First employee: ${employees[0].name} (org: ${employees[0].organization_id})`, 'info');
				}
			}

			// Test 5: RPC function
			addResult('5. Testing get_all_employees RPC function...');
			const { data: rpcEmployees, error: rpcError } = await supabase
				.rpc('get_all_employees');
			
			if (rpcError) {
				addResult(`   ❌ RPC error: ${rpcError.message}`, 'error');
				addResult(`   📋 Error code: ${rpcError.code}`, 'error');
			} else {
				addResult(`   ✅ RPC returned ${rpcEmployees.length} employees`, 'success');
				if (rpcEmployees.length > 0) {
					addResult(`   📋 First RPC employee: ${rpcEmployees[0].name}`, 'info');
				}
			}

			// Test 6: Test create_employee RPC function
			addResult('6. Testing create_employee RPC function...');
			const testEmployeeName = `Test Employee ${Date.now()}`;
			const { data: newEmployee, error: createError } = await supabase
				.rpc('create_employee', {
					p_name: testEmployeeName,
					p_role: 'FOH',
					p_default_daily_rate: 120.00,
					p_restaurant_id: null,
					p_is_active: true
				});

			if (createError) {
				addResult(`   ❌ Create employee RPC error: ${createError.message}`, 'error');
				addResult(`   📋 Error code: ${createError.code}`, 'error');
			} else {
				addResult(`   ✅ Create employee RPC succeeded`, 'success');
				addResult(`   📋 Created employee: ${newEmployee?.name || 'Unknown'}`, 'info');
			}

			// Test 7: Test create_shift RPC function
			addResult('7. Testing create_shift RPC function...');
			if (orgs && orgs.length > 0) {
				const { data: locations } = await supabase.from('locations').select('*').limit(1);
				if (locations && locations.length > 0) {
					const { data: newShift, error: shiftError } = await supabase
						.rpc('create_shift', {
							p_employee_id: null,
							p_location_id: locations[0].id,
							p_shift_date: '2024-01-15',
							p_start_time: '09:00',
							p_end_time: '17:00',
							p_daily_rate: 25.00,
							p_hours_worked: null,
							p_is_paid: false,
							p_advance_deduction: 0,
							p_notes: 'Test shift'
						});

					if (shiftError) {
						addResult(`   ❌ Create shift RPC error: ${shiftError.message}`, 'error');
						addResult(`   📋 Error code: ${shiftError.code}`, 'error');
					} else {
						addResult(`   ✅ Create shift RPC succeeded`, 'success');
					}
				} else {
					addResult(`   ⚠️  No locations found for shift test`, 'warning');
				}
			}

			// Analysis
			addResult('📊 ANALYSIS:', 'header');
			
			if (employees && employees.length > 0 && (!rpcEmployees || rpcEmployees.length === 0)) {
				addResult('❌ ISSUE: Employees exist but RPC returns empty', 'error');
				addResult('🔧 LIKELY CAUSE: Authentication/organization context issue', 'warning');
			} else if (rpcEmployees && rpcEmployees.length > 0) {
				addResult('✅ RPC function working correctly!', 'success');
			} else {
				addResult('⚠️  No employees in database', 'warning');
			}

		} catch (error) {
			addResult(`❌ Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
		}

		loading = false;
	}

	function addResult(message: string, type: string = 'normal') {
		debugResults = [...debugResults, { message, type, timestamp: new Date().toLocaleTimeString() }];
	}

	onMount(() => {
		runDiagnostics();
	});
</script>

<div class="container mx-auto p-6">
	<h1 class="text-2xl font-bold mb-6">🔍 RPC Function Debug Page</h1>
	
	<div class="mb-4">
		<button 
			on:click={runDiagnostics}
			disabled={loading}
			class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
		>
			{loading ? 'Running Diagnostics...' : 'Run Diagnostics'}
		</button>
	</div>

	<div class="bg-gray-100 p-4 rounded-lg font-mono text-sm">
		{#each debugResults as result}
			<div class="mb-1 {
				result.type === 'error' ? 'text-red-600' :
				result.type === 'success' ? 'text-green-600' :
				result.type === 'warning' ? 'text-yellow-600' :
				result.type === 'info' ? 'text-blue-600' :
				result.type === 'header' ? 'text-purple-600 font-bold' :
				'text-gray-800'
			}">
				<span class="text-gray-400 text-xs">[{result.timestamp}]</span>
				{result.message}
			</div>
		{/each}
		
		{#if loading}
			<div class="text-blue-600">Running diagnostics...</div>
		{/if}
	</div>
</div>

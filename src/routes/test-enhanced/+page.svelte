<script lang="ts">
	/**
	 * Enhanced Scheduling Test Page
	 * Demonstrates the new leave management and enhanced scheduling features
	 * Following Code Complete principles: Clear testing interface, comprehensive examples
	 */

	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { authState } from '$lib/stores/auth.js';
	import { enhancedScheduleService, leaveService } from '$lib/services';
	import EnhancedScheduleGrid from '$lib/components/EnhancedScheduleGrid.svelte';
	import LeaveManagementDashboard from '$lib/components/LeaveManagementDashboard.svelte';
	import EmployeeAvailabilityCard from '$lib/components/EmployeeAvailabilityCard.svelte';
	import type {
		EnhancedWeeklySchedule,
		UIState,
		LeaveRequest,
		EmployeeUnavailability,
		ShiftConflict
	} from '$lib/types.js';
	import { getWeekDates } from '$lib/utils.js';

	// Test data state
	let enhancedSchedule: EnhancedWeeklySchedule | null = $state(null);
	let isLoading = $state(false);
	let error = $state<string | null>(null);
	let activeDemo: 'schedule' | 'leave' | 'availability' = $state('schedule');

	// UI state for testing
	let uiState: UIState = $state({
		selectedWeek: new Date(),
		dragState: {
			isDragging: false,
			draggedShift: null,
			draggedEmployee: null,
			dropTarget: null
		},
		dragCreateState: {
			isActive: false,
			mode: null,
			direction: null,
			sourceEmployee: null,
			sourceDate: null,
			sourceShift: null,
			targetDates: [],
			targetEmployees: [],
			previewCells: []
		},
		shiftModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		employeeModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		leaveModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		unavailabilityModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		reassignmentPanel: {
			isOpen: false,
			affectedShifts: [],
			leaveRequest: undefined,
			suggestedReplacements: []
		},
		isEmployeePanelCollapsed: false,
		isMobileView: false,
		showLocationManagement: false,
		showPaymentManagement: false,
		showLeaveManagement: false,
		showUnavailabilityOverlay: true
	});

	// Get current organization and user info
	let organizationId = $derived($authState.currentRestaurant?.id || '');
	let currentUserId = $derived($authState.user?.id || '');
	let userRole = $derived('admin' as const);

	// Calculate week dates
	let weekDates = $derived(getWeekDates(uiState.selectedWeek));

	/**
	 * Load test data on mount
	 * Following Code Complete: Clear initialization with error handling
	 */
	onMount(async () => {
		// Check if user is authenticated
		if (!$authState.user) {
			goto('/auth');
			return;
		}

		if (!organizationId) {
			error = 'No organization selected. Please select a restaurant first.';
			return;
		}

		await loadTestData();
	});

	/**
	 * Load enhanced schedule test data
	 * Following Code Complete: Comprehensive data loading with fallbacks
	 */
	async function loadTestData() {
		isLoading = true;
		error = null;

		try {
			console.log('🔄 TestEnhanced: Loading test data for organization:', organizationId);

			// Load enhanced schedule
			enhancedSchedule = await enhancedScheduleService.loadEnhancedWeeklySchedule(
				organizationId,
				uiState.selectedWeek
			);

			console.log('✅ TestEnhanced: Enhanced schedule loaded:', enhancedSchedule);

		} catch (err) {
			console.error('❌ TestEnhanced: Failed to load test data:', err);
			error = err instanceof Error ? err.message : 'Failed to load test data';
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Create sample leave request for testing
	 * Following Code Complete: Clear test data generation
	 */
	async function createSampleLeaveRequest() {
		if (!enhancedSchedule || enhancedSchedule.employees.length === 0) {
			alert('No employees available for testing');
			return;
		}

		try {
			const employee = enhancedSchedule.employees[0];
			const startDate = new Date();
			startDate.setDate(startDate.getDate() + 7); // Next week
			const endDate = new Date(startDate);
			endDate.setDate(endDate.getDate() + 2); // 3 days

			await leaveService.createLeaveRequest(organizationId, {
				employeeId: employee.id,
				requestType: 'vacation',
				startDate: startDate.toISOString().split('T')[0],
				endDate: endDate.toISOString().split('T')[0],
				reason: 'Sample vacation request for testing'
			});

			// Reload data to show the new request
			await loadTestData();
			alert('Sample leave request created successfully!');

		} catch (err) {
			console.error('❌ TestEnhanced: Failed to create sample leave request:', err);
			alert('Failed to create sample leave request: ' + (err instanceof Error ? err.message : 'Unknown error'));
		}
	}

	/**
	 * Handle leave request click
	 * Following Code Complete: Clear event handling
	 */
	function handleLeaveRequestClick(leaveRequest: LeaveRequest) {
		console.log('Leave request clicked:', leaveRequest);
		alert(`Leave Request: ${leaveRequest.requestType} from ${leaveRequest.startDate.toLocaleDateString()} to ${leaveRequest.endDate.toLocaleDateString()}`);
	}

	/**
	 * Handle unavailability click
	 * Following Code Complete: Clear event handling
	 */
	function handleUnavailabilityClick(unavailability: EmployeeUnavailability) {
		console.log('Unavailability clicked:', unavailability);
		alert(`Unavailability: ${unavailability.unavailabilityType} - ${unavailability.reason || 'No reason provided'}`);
	}

	/**
	 * Handle conflict detection
	 * Following Code Complete: Clear conflict handling
	 */
	function handleConflictDetected(conflicts: ShiftConflict[]) {
		console.log('Conflicts detected:', conflicts);
		const conflictMessages = conflicts.map(c => `${c.conflictType}: ${c.conflictDetails}`).join('\n');
		alert(`Scheduling Conflicts Detected:\n\n${conflictMessages}`);
	}

	/**
	 * Handle shift click (placeholder)
	 * Following Code Complete: Clear event handling
	 */
	function handleShiftClick(shift: any) {
		console.log('Shift clicked:', shift);
	}

	/**
	 * Handle data refresh
	 * Following Code Complete: Clear refresh handling
	 */
	function handleDataRefresh() {
		loadTestData();
	}

	/**
	 * Handle leave conflict
	 * Following Code Complete: Clear conflict handling
	 */
	function handleLeaveConflict(event: CustomEvent<{ leaveRequest: LeaveRequest }>) {
		const { leaveRequest } = event.detail;
		console.log('Leave conflict detected:', leaveRequest);
		alert(`Leave conflict detected for ${leaveRequest.employeeName || 'employee'}`);
	}
</script>

<svelte:head>
	<title>Enhanced Scheduling Test - Tallyca</title>
</svelte:head>

<div class="min-h-screen bg-gray-50">
	<!-- Header -->
	<header class="bg-white shadow">
		<div class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
			<div class="flex items-center justify-between">
				<div>
					<h1 class="text-3xl font-bold tracking-tight text-gray-900">Enhanced Scheduling Test</h1>
					<p class="mt-1 text-sm text-gray-500">
						Testing leave management integration and enhanced scheduling features
					</p>
				</div>

				<div class="flex space-x-3">
					<button
						onclick={() => goto('/')}
						class="rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
					>
						← Back to Main
					</button>

					<button
						onclick={loadTestData}
						disabled={isLoading}
						class="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
					>
						{#if isLoading}
							<svg class="mr-2 h-4 w-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
							</svg>
						{/if}
						Refresh Data
					</button>

					<button
						onclick={createSampleLeaveRequest}
						disabled={isLoading || !enhancedSchedule}
						class="rounded-md bg-green-600 px-4 py-2 text-sm font-medium text-white hover:bg-green-700 disabled:opacity-50"
					>
						Create Sample Leave
					</button>
				</div>
			</div>

			<!-- Demo Navigation -->
			<div class="mt-6 flex space-x-1 rounded-lg bg-gray-100 p-1">
				<button
					onclick={() => activeDemo = 'schedule'}
					class="rounded-md px-3 py-2 text-sm font-medium transition-colors {activeDemo === 'schedule' 
						? 'bg-white text-gray-900 shadow-sm' 
						: 'text-gray-500 hover:text-gray-700'}"
				>
					Enhanced Schedule Grid
				</button>
				<button
					onclick={() => activeDemo = 'leave'}
					class="rounded-md px-3 py-2 text-sm font-medium transition-colors {activeDemo === 'leave' 
						? 'bg-white text-gray-900 shadow-sm' 
						: 'text-gray-500 hover:text-gray-700'}"
				>
					Leave Management
				</button>
				<button
					onclick={() => activeDemo = 'availability'}
					class="rounded-md px-3 py-2 text-sm font-medium transition-colors {activeDemo === 'availability' 
						? 'bg-white text-gray-900 shadow-sm' 
						: 'text-gray-500 hover:text-gray-700'}"
				>
					Availability Cards
				</button>
			</div>
		</div>
	</header>

	<!-- Main Content -->
	<main class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
		{#if error}
			<!-- Error State -->
			<div class="rounded-md bg-red-50 p-4">
				<div class="flex">
					<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
					</svg>
					<div class="ml-3">
						<h3 class="text-sm font-medium text-red-800">Error Loading Test Data</h3>
						<p class="mt-1 text-sm text-red-700">{error}</p>
					</div>
				</div>
			</div>
		{:else if isLoading}
			<!-- Loading State -->
			<div class="flex items-center justify-center py-12">
				<div class="text-center">
					<svg class="mx-auto h-12 w-12 animate-spin text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
					</svg>
					<p class="mt-2 text-sm text-gray-500">Loading enhanced scheduling data...</p>
				</div>
			</div>
		{:else if !enhancedSchedule}
			<!-- No Data State -->
			<div class="text-center py-12">
				<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
				</svg>
				<h3 class="mt-2 text-sm font-medium text-gray-900">No Schedule Data</h3>
				<p class="mt-1 text-sm text-gray-500">
					No enhanced schedule data available. Make sure you have selected an organization.
				</p>
			</div>
		{:else}
			<!-- Demo Content -->
			{#if activeDemo === 'schedule'}
				<!-- Enhanced Schedule Grid Demo -->
				<div class="rounded-lg bg-white shadow">
					<div class="border-b border-gray-200 px-6 py-4">
						<h2 class="text-lg font-medium text-gray-900">Enhanced Schedule Grid</h2>
						<p class="mt-1 text-sm text-gray-500">
							Schedule grid with leave integration, conflict detection, and availability overlays
						</p>
						
						<!-- Controls -->
						<div class="mt-4 flex items-center space-x-4">
							<label class="flex items-center">
								<input
									type="checkbox"
									bind:checked={uiState.showUnavailabilityOverlay}
									class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
								/>
								<span class="ml-2 text-sm text-gray-700">Show Availability Overlay</span>
							</label>
						</div>
					</div>

					<div class="p-6">
						<EnhancedScheduleGrid
							schedule={enhancedSchedule}
							{weekDates}
							{uiState}
							{organizationId}
							onShiftClick={handleShiftClick}
							onShiftCreate={handleShiftClick}
							onLeaveRequestClick={handleLeaveRequestClick}
							onUnavailabilityClick={handleUnavailabilityClick}
							onConflictDetected={handleConflictDetected}
						/>
					</div>
				</div>

			{:else if activeDemo === 'leave'}
				<!-- Leave Management Demo -->
				<div class="rounded-lg bg-white shadow">
					<div class="border-b border-gray-200 px-6 py-4">
						<h2 class="text-lg font-medium text-gray-900">Leave Management Dashboard</h2>
						<p class="mt-1 text-sm text-gray-500">
							Comprehensive leave request management with approval workflows
						</p>
					</div>

					<div class="p-6">
						<LeaveManagementDashboard
							{organizationId}
							employees={enhancedSchedule.employees}
							{currentUserId}
							{userRole}
							on:refresh={handleDataRefresh}
							on:conflictDetected={handleLeaveConflict}
						/>
					</div>
				</div>

			{:else if activeDemo === 'availability'}
				<!-- Availability Cards Demo -->
				<div class="rounded-lg bg-white shadow">
					<div class="border-b border-gray-200 px-6 py-4">
						<h2 class="text-lg font-medium text-gray-900">Employee Availability Cards</h2>
						<p class="mt-1 text-sm text-gray-500">
							Individual employee availability status with leave and unavailability details
						</p>
					</div>

					<div class="p-6">
						{#if enhancedSchedule.employeeAvailability.size > 0}
							<div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
								{#each enhancedSchedule.employees.slice(0, 6) as employee}
									{@const availability = enhancedSchedule.employeeAvailability.get(employee.id)?.[0]}
									{#if availability}
										<EmployeeAvailabilityCard
											{employee}
											availabilityStatus={availability}
											onLeaveRequestClick={handleLeaveRequestClick}
											onUnavailabilityClick={handleUnavailabilityClick}
											showDetails={true}
										/>
									{/if}
								{/each}
							</div>
						{:else}
							<div class="text-center py-8">
								<p class="text-sm text-gray-500">No availability data available</p>
							</div>
						{/if}
					</div>
				</div>
			{/if}
		{/if}
	</main>
</div>

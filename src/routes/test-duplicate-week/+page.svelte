<script lang="ts">
	import { onMount } from 'svelte';
	import type { WeeklySchedule, DuplicateWeekRequest, DuplicateWeekResult, Shift, Employee, Venue } from '$lib/types.js';
	import { getWeekStart, getWeekDates, formatWeekRange } from '$lib/utils.js';
	// import { duplicateWeekShifts } from '$lib/services/shiftService.js'; // Not used in test page
	import DuplicateWeekModal from '$lib/components/DuplicateWeekModal.svelte';
	import { Button } from '$lib/components/ui/index.js';

	// Test data
	let testSchedule: WeeklySchedule = $state({
		weekStartDate: getWeekStart(new Date()),
		employees: [
			{
				id: 'emp-1',
				name: '<PERSON>',
				fullName: '<PERSON>',
				role: 'FOH',
				defaultDailyRate: 120,
				defaultDailyWorkingHours: 8,
				defaultHourlyRate: 15,
				isActive: true,
				active: true,
				createdAt: new Date(),
				updatedAt: new Date()
			},
			{
				id: 'emp-2',
				name: '<PERSON>',
				fullName: '<PERSON>',
				role: 'BOH',
				defaultDailyRate: 140,
				defaultDailyWorkingHours: 8,
				defaultHourlyRate: 17.5,
				isActive: true,
				active: true,
				createdAt: new Date(),
				updatedAt: new Date()
			}
		] as Employee[],
		venues: [
			{
				id: 'venue-1',
				name: 'Main Restaurant',
				color: '#3B82F6',
				address: '123 Main St'
			},
			{
				id: 'venue-2',
				name: 'Bar Area',
				color: '#10B981',
				address: '123 Main St'
			}
		] as Venue[],
		shifts: []
	});

	// Modal state
	let isModalOpen = $state(false);
	let testResults = $state<DuplicateWeekResult | null>(null);
	let errorMessage = $state('');

	// Generate test shifts for the current week
	onMount(() => {
		generateTestShifts();
	});

	function generateTestShifts() {
		const weekDates = getWeekDates(testSchedule.weekStartDate);
		const shifts: Shift[] = [];

		// Create some sample shifts for the week
		weekDates.forEach((date, dayIndex) => {
			// Skip weekends for this test
			if (dayIndex === 5 || dayIndex === 6) return;

			// John Doe - Morning shifts
			// Normalize date to midnight to match database format
			const normalizedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
			shifts.push({
				id: `shift-john-${dayIndex}`,
				employeeId: 'emp-1',
				locationId: 'venue-1',
				venueId: 'venue-1',
				date: normalizedDate,
				startTime: '09:00',
				endTime: '17:00',
				hoursWorked: 8,
				totalHours: 8,
				dailyRate: 120,
				isPaid: false,
				advanceDeduction: 0,
				notes: `Test shift for ${normalizedDate.toDateString()}`,
				createdAt: new Date(),
				updatedAt: new Date()
			});

			// Jane Smith - Evening shifts (alternate days)
			if (dayIndex % 2 === 0) {
				shifts.push({
					id: `shift-jane-${dayIndex}`,
					employeeId: 'emp-2',
					locationId: 'venue-2',
					venueId: 'venue-2',
					date: normalizedDate,
					startTime: '17:00',
					endTime: '23:00',
					hoursWorked: 6,
					totalHours: 6,
					dailyRate: 105,
					isPaid: false,
					advanceDeduction: 0,
					notes: `Evening shift for ${normalizedDate.toDateString()}`,
					createdAt: new Date(),
					updatedAt: new Date()
				});
			}
		});

		testSchedule.shifts = shifts;
		console.log('✅ Test: Generated test shifts:', shifts);
	}

	function openModal() {
		isModalOpen = true;
		testResults = null;
		errorMessage = '';
	}

	function closeModal() {
		isModalOpen = false;
	}

	async function handleDuplicateWeek(request: DuplicateWeekRequest): Promise<DuplicateWeekResult> {
		try {
			console.log('🔄 Test: Simulating duplicate week request:', request);

			// Simulate the duplication process with mock data
			// In a real implementation, this would call the actual RPC function
			const mockResult: DuplicateWeekResult = {
				success: true,
				totalShiftsDuplicated: testSchedule.shifts.length * request.targetWeekStarts.length,
				conflictsDetected: Math.floor(Math.random() * 3), // Random conflicts for demo
				conflictsResolved: Math.floor(Math.random() * 2),
				targetWeeksProcessed: request.targetWeekStarts.length,
				message: `Successfully duplicated ${testSchedule.shifts.length} shifts to ${request.targetWeekStarts.length} week(s)`,
				details: request.targetWeekStarts.map(targetWeek => ({
					targetWeekStart: targetWeek.toISOString().split('T')[0],
					shiftsDuplicated: testSchedule.shifts.length,
					conflictsFound: [],
					conflictsResolved: 0
				}))
			};

			// Simulate network delay
			await new Promise(resolve => setTimeout(resolve, 2000));

			testResults = mockResult;
			console.log('✅ Test: Mock duplication completed:', mockResult);

			return mockResult;
		} catch (error) {
			console.error('❌ Test: Duplication failed:', error);
			errorMessage = error instanceof Error ? error.message : 'Duplication failed';
			throw error;
		}
	}

	function navigateWeek(direction: 'prev' | 'next') {
		const newDate = new Date(testSchedule.weekStartDate);
		newDate.setDate(testSchedule.weekStartDate.getDate() + (direction === 'next' ? 7 : -7));
		testSchedule.weekStartDate = newDate;
		generateTestShifts();
	}

	// Computed values
	let weekDates = $derived(getWeekDates(testSchedule.weekStartDate));
	let shiftsThisWeek = $derived(testSchedule.shifts.length);
</script>

<div class="container mx-auto p-6 max-w-4xl">
	<div class="mb-8">
		<h1 class="text-3xl font-bold text-gray-900 mb-4">Duplicate Week Feature Test</h1>
		<p class="text-gray-600 mb-6">
			This page demonstrates the duplicate week functionality with test data. 
			The modal provides a complete workflow for duplicating shifts from one week to multiple target weeks.
		</p>
	</div>

	<!-- Current Week Display -->
	<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
		<div class="flex items-center justify-between mb-4">
			<h2 class="text-xl font-semibold text-gray-900">Current Test Week</h2>
			<div class="flex items-center space-x-2">
				<button
					onclick={() => navigateWeek('prev')}
					class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
					aria-label="Previous week"
				>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
					</svg>
				</button>
				<div class="text-center min-w-[200px]">
					<div class="font-semibold text-gray-900">{formatWeekRange(testSchedule.weekStartDate)}</div>
					<div class="text-sm text-gray-500">{shiftsThisWeek} shifts</div>
				</div>
				<button
					onclick={() => navigateWeek('next')}
					class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
					aria-label="Next week"
				>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
					</svg>
				</button>
			</div>
		</div>

		<!-- Week Overview -->
		<div class="grid grid-cols-7 gap-2 mb-4">
			{#each weekDates as date}
				{@const dayShifts = testSchedule.shifts.filter(s => s.date.toDateString() === date.toDateString())}
				<div class="text-center p-3 bg-gray-50 rounded-lg">
					<div class="font-medium text-gray-900 mb-1">
						{date.toLocaleDateString('en-US', { weekday: 'short' })}
					</div>
					<div class="text-sm text-gray-600 mb-2">
						{date.getDate()}
					</div>
					<div class="text-xs text-blue-600">
						{dayShifts.length} shifts
					</div>
				</div>
			{/each}
		</div>

		<!-- Test Actions -->
		<div class="flex items-center justify-between">
			<div class="flex space-x-3">
				<Button onclick={generateTestShifts} variant="secondary" size="sm">
					Regenerate Test Shifts
				</Button>
			</div>
			<Button onclick={openModal}>
				<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
				</svg>
				Test Duplicate Week
			</Button>
		</div>
	</div>

	<!-- Test Results -->
	{#if testResults}
		<div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
			<h3 class="text-lg font-semibold text-green-900 mb-4">Test Results</h3>
			<div class="grid grid-cols-2 gap-4 text-sm">
				<div>
					<span class="text-green-700">Shifts Duplicated:</span>
					<div class="font-medium text-green-900">{testResults.totalShiftsDuplicated}</div>
				</div>
				<div>
					<span class="text-green-700">Weeks Processed:</span>
					<div class="font-medium text-green-900">{testResults.targetWeeksProcessed}</div>
				</div>
				<div>
					<span class="text-green-700">Conflicts Detected:</span>
					<div class="font-medium text-green-900">{testResults.conflictsDetected}</div>
				</div>
				<div>
					<span class="text-green-700">Conflicts Resolved:</span>
					<div class="font-medium text-green-900">{testResults.conflictsResolved}</div>
				</div>
			</div>
			<p class="text-green-800 mt-4">{testResults.message}</p>
		</div>
	{/if}

	{#if errorMessage}
		<div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
			<h3 class="text-lg font-semibold text-red-900 mb-2">Error</h3>
			<p class="text-red-800">{errorMessage}</p>
		</div>
	{/if}

	<!-- Feature Documentation -->
	<div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
		<h3 class="text-lg font-semibold text-blue-900 mb-4">Feature Overview</h3>
		<div class="space-y-3 text-sm text-blue-800">
			<p><strong>Step 1:</strong> Select source week with shifts to duplicate</p>
			<p><strong>Step 2:</strong> Choose one or more target weeks</p>
			<p><strong>Step 3:</strong> Configure conflict resolution (merge or replace)</p>
			<p><strong>Step 4:</strong> Review and confirm duplication</p>
			<p><strong>Step 5:</strong> View detailed results with conflict information</p>
		</div>
		<div class="mt-4 p-3 bg-blue-100 rounded">
			<p class="text-xs text-blue-700">
				<strong>Note:</strong> This test page uses mock data and simulated responses. 
				In the actual application, the RPC function will handle real database operations.
			</p>
		</div>
	</div>
</div>

<!-- Duplicate Week Modal -->
{#if isModalOpen}
	<DuplicateWeekModal
		isOpen={isModalOpen}
		currentWeekStart={testSchedule.weekStartDate}
		schedule={testSchedule}
		organizationId="test-org-123"
		onClose={closeModal}
		onDuplicate={handleDuplicateWeek}
	/>
{/if}

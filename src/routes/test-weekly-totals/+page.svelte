<script lang="ts">
	/**
	 * Test Page for Weekly Total Calculation Synchronization
	 * This page demonstrates and tests the weekly total calculation fixes
	 */
	
	import { onMount } from 'svelte';
	import { formatCurrency, calculateEmployeeWeeklySummary } from '$lib/utils.js';
	import type { Employee, Shift } from '$lib/types.js';

	// Test data
	let testEmployee: Employee = {
		id: 'test-employee-1',
		name: 'Test Employee',
		fullName: 'Test Employee',
		role: 'FOH',
		defaultDailyRate: 120,
		defaultDailyWorkingHours: 8,
		defaultHourlyRate: 15,
		isActive: true,
		active: true, // Added missing active property
		organizationId: 'test-org-123', // Added missing organizationId
		createdAt: new Date(),
		updatedAt: new Date() // Added missing updatedAt
	};

	let testShifts: Shift[] = $state([
		{
			id: 'shift-1',
			employeeId: 'test-employee-1',
			venueId: 'venue-1',
			locationId: 'venue-1',
			date: new Date('2024-01-15'),
			startTime: '09:00',
			endTime: '17:00',
			totalHours: 8,
			hoursWorked: 8,
			dailyRate: 120,
			isPaid: false,
			advanceDeduction: 0,
			notes: 'Test shift 1',
			createdAt: new Date(),
			updatedAt: new Date()
		},
		{
			id: 'shift-2',
			employeeId: 'test-employee-1',
			venueId: 'venue-1',
			locationId: 'venue-1',
			date: new Date('2024-01-16'),
			startTime: '10:00',
			endTime: '18:00',
			totalHours: 8,
			hoursWorked: 8,
			dailyRate: 120,
			isPaid: true,
			advanceDeduction: 10,
			notes: 'Test shift 2',
			createdAt: new Date(),
			updatedAt: new Date()
		}
	]);

	// Reactive calculation
	let weeklySummary = $derived(calculateEmployeeWeeklySummary(testEmployee, testShifts));

	// Test functions
	function modifyShift(shiftId: string) {
		const shiftIndex = testShifts.findIndex(s => s.id === shiftId);
		if (shiftIndex !== -1) {
			// Create new array to trigger reactivity (same pattern as our fix)
			const updatedShifts = [...testShifts];
			const currentShift = updatedShifts[shiftIndex];
			if (currentShift) {
				updatedShifts[shiftIndex] = {
					...currentShift,
					dailyRate: currentShift.dailyRate + 10,
					totalHours: (currentShift.totalHours || 0) + 1,
					updatedAt: new Date()
				};
			}
			
			// Force reactivity by creating new array
			testShifts = updatedShifts;
			
			console.log('🔄 Test: Modified shift, weekly totals should recalculate');
		}
	}

	function togglePaidStatus(shiftId: string) {
		const shiftIndex = testShifts.findIndex(s => s.id === shiftId);
		if (shiftIndex !== -1) {
			// Create new array to trigger reactivity
			const updatedShifts = [...testShifts];
			updatedShifts[shiftIndex] = {
				...updatedShifts[shiftIndex],
				isPaid: !updatedShifts[shiftIndex].isPaid,
				updatedAt: new Date()
			};
			
			testShifts = updatedShifts;
			
			console.log('🔄 Test: Toggled paid status, weekly totals should recalculate');
		}
	}

	function addNewShift() {
		const newShift: Shift = {
			id: `shift-${Date.now()}`,
			employeeId: 'test-employee-1',
			venueId: 'venue-1',
			locationId: 'venue-1',
			date: new Date('2024-01-17'),
			startTime: '11:00',
			endTime: '19:00',
			totalHours: 8,
			hoursWorked: 8,
			dailyRate: 130,
			isPaid: false,
			advanceDeduction: 0,
			notes: 'New test shift',
			createdAt: new Date(),
			updatedAt: new Date()
		};

		// Add to array (same pattern as our fix)
		testShifts = [...testShifts, newShift];
		
		console.log('🔄 Test: Added new shift, weekly totals should recalculate');
	}

	// Debug logging
	$effect(() => {
		console.log('🔄 Test: Weekly summary recalculated:', {
			totalHours: weeklySummary.totalHours,
			totalPay: weeklySummary.totalPay,
			paidAmount: weeklySummary.paidAmount,
			unpaidAmount: weeklySummary.unpaidAmount,
			shiftsCount: weeklySummary.shiftsCount,
			timestamp: new Date().toISOString()
		});
	});

	onMount(() => {
		console.log('🔄 Test: Weekly totals test page mounted');
	});
</script>

<div class="container mx-auto p-6">
	<h1 class="text-3xl font-bold text-gray-900 mb-6">Weekly Total Calculation Test</h1>
	
	<div class="bg-white rounded-lg shadow p-6 mb-6">
		<h2 class="text-xl font-semibold text-gray-800 mb-4">Employee: {testEmployee.name}</h2>
		
		<!-- Weekly Summary Display -->
		<div class="bg-gray-50 rounded-lg p-4 mb-6">
			<h3 class="text-lg font-medium text-gray-700 mb-3">Weekly Summary</h3>
			<div class="grid grid-cols-2 md:grid-cols-5 gap-4">
				<div class="text-center">
					<div class="text-2xl font-bold text-gray-900">{weeklySummary.shiftsCount}</div>
					<div class="text-sm text-gray-500">Shifts</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-gray-900">{weeklySummary.totalHours.toFixed(1)}h</div>
					<div class="text-sm text-gray-500">Total Hours</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-gray-900">{formatCurrency(weeklySummary.totalPay)}</div>
					<div class="text-sm text-gray-500">Total Pay</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-green-600">{formatCurrency(weeklySummary.paidAmount)}</div>
					<div class="text-sm text-gray-500">Paid</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-red-600">{formatCurrency(weeklySummary.unpaidAmount)}</div>
					<div class="text-sm text-gray-500">Unpaid</div>
				</div>
			</div>
		</div>

		<!-- Test Actions -->
		<div class="mb-6">
			<h3 class="text-lg font-medium text-gray-700 mb-3">Test Actions</h3>
			<div class="flex flex-wrap gap-3">
				<button
					onclick={addNewShift}
					class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
				>
					Add New Shift
				</button>
			</div>
		</div>

		<!-- Shifts List -->
		<div>
			<h3 class="text-lg font-medium text-gray-700 mb-3">Shifts ({testShifts.length})</h3>
			<div class="space-y-3">
				{#each testShifts as shift}
					<div class="border border-gray-200 rounded-lg p-4">
						<div class="flex items-center justify-between">
							<div>
								<div class="font-medium text-gray-900">
									{shift.date.toLocaleDateString()} • {shift.startTime} - {shift.endTime}
								</div>
								<div class="text-sm text-gray-500">
									{shift.totalHours}h • {formatCurrency(shift.dailyRate)} • 
									{shift.isPaid ? 'Paid' : 'Unpaid'}
									{#if shift.advanceDeduction > 0}
										• Advance: {formatCurrency(shift.advanceDeduction)}
									{/if}
								</div>
							</div>
							<div class="flex gap-2">
								<button
									onclick={() => modifyShift(shift.id)}
									class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-sm transition-colors"
								>
									Modify (+10 лв, +1h)
								</button>
								<button
									onclick={() => togglePaidStatus(shift.id)}
									class="bg-{shift.isPaid ? 'red' : 'green'}-500 hover:bg-{shift.isPaid ? 'red' : 'green'}-600 text-white px-3 py-1 rounded text-sm transition-colors"
								>
									{shift.isPaid ? 'Mark Unpaid' : 'Mark Paid'}
								</button>
							</div>
						</div>
					</div>
				{/each}
			</div>
		</div>
	</div>

	<!-- Instructions -->
	<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
		<h3 class="text-lg font-medium text-blue-800 mb-2">Test Instructions</h3>
		<ul class="text-sm text-blue-700 space-y-1">
			<li>• Use the buttons above to modify shifts and observe weekly total updates</li>
			<li>• Check the browser console for debug logs showing recalculation events</li>
			<li>• Weekly totals should update immediately when shifts are modified</li>
			<li>• Currency formatting should display Bulgarian Lev (лв) with proper separators</li>
		</ul>
	</div>
</div>

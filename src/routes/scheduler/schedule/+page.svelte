<script lang="ts">
	/**
	 * Schedule Management Page
	 * Drag-and-drop schedule interface following Tallyca design patterns
	 * Following Code Complete principles: Clear state management, modular components
	 */

	import { onMount } from 'svelte';
	import { employeeService, venueService, shiftService } from '$lib/services/index.js';
	import { checkShiftOverlap, bulkDeleteEmployeeShifts, bulkDeleteDateShifts, bulkDeleteWeekShifts, duplicateWeekShifts } from '$lib/services/shiftService.js';
	import { LoadingSpinner, Button } from '$lib/components/ui/index.js';
	import ScheduleGrid from '$lib/components/ScheduleGrid.svelte';
	import ShiftModal from '$lib/components/ShiftModal.svelte';
	import BulkDeleteModal from '$lib/components/BulkDeleteModal.svelte';
	import DuplicateWeekModal from '$lib/components/DuplicateWeekModal.svelte';
	import WeekCalendarPicker from '$lib/components/WeekCalendarPicker.svelte';

	import type { WeeklySchedule, Employee, Shift, UIState } from '$lib/types.js';
	import { getWeekStart, getWeekDates, formatWeekRange } from '$lib/utils.js';

	// State
	let isLoading = $state(true);
	let weeklySchedule: WeeklySchedule = $state({
		weekStartDate: getWeekStart(new Date()),
		employees: [],
		venues: [],
		shifts: []
	});

	let uiState: UIState = $state({
		selectedWeek: new Date(),
		dragState: {
			isDragging: false,
			draggedShift: null,
			draggedEmployee: null,
			dropTarget: null
		},
		dragCreateState: {
			isActive: false,
			mode: null,
			direction: null,
			sourceEmployee: null,
			sourceDate: null,
			sourceShift: null,
			targetDates: [],
			targetEmployees: [],
			previewCells: []
		},
		shiftModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		employeeModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		leaveModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		unavailabilityModal: {
			isOpen: false,
			mode: 'create',
			shift: null,
			employee: null
		},
		reassignmentPanel: {
			isOpen: false,
			affectedShifts: [],
			leaveRequest: undefined,
			suggestedReplacements: []
		},
		isEmployeePanelCollapsed: false,
		isMobileView: false,
		showLocationManagement: false,
		showPaymentManagement: false,
		showLeaveManagement: false,
		showUnavailabilityOverlay: false
	});

	// Bulk delete modal state
	let isBulkDeleteModalOpen = $state(false);

	// Duplicate week modal state
	let isDuplicateWeekModalOpen = $state(false);

	// Calendar picker state
	let isCalendarPickerOpen = $state(false);

	// Optimistic update tracking to prevent race conditions
	let pendingOptimisticUpdates = $state<Set<string>>(new Set());

	// Computed
	let weekDates = $derived(getWeekDates(weeklySchedule.weekStartDate));
	let unassignedShifts = $derived(weeklySchedule.shifts.filter(shift => !shift.employeeId));

	// Functions
	async function loadScheduleData() {
		try {
			isLoading = true;

			const [employees, venues, shifts] = await Promise.all([
				employeeService.fetchActiveEmployees(),
				venueService.fetchAllVenues(),
				shiftService.fetchShiftsForWeek(weeklySchedule.weekStartDate)
			]);

			weeklySchedule.employees = employees;
			weeklySchedule.venues = venues;
			weeklySchedule.shifts = shifts;

		} catch (error) {
			console.error('❌ Failed to load schedule data:', error);
		} finally {
			isLoading = false;
		}
	}

	function navigateWeek(direction: 'prev' | 'next') {
		const currentDate = new Date(weeklySchedule.weekStartDate);
		const newDate = new Date(currentDate);
		newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));

		weeklySchedule.weekStartDate = newDate;
		uiState.selectedWeek = newDate;
		loadScheduleData();
	}

	function goToCurrentWeek() {
		weeklySchedule.weekStartDate = getWeekStart(new Date());
		uiState.selectedWeek = weeklySchedule.weekStartDate;
		loadScheduleData();
	}

	// Calendar picker functions
	function openCalendarPicker() {
		isCalendarPickerOpen = true;
	}

	function closeCalendarPicker() {
		isCalendarPickerOpen = false;
	}

	function handleWeekSelect(weekStart: Date) {
		weeklySchedule.weekStartDate = weekStart;
		uiState.selectedWeek = weekStart;
		loadScheduleData();
		closeCalendarPicker();
	}

	// Enhanced Today button text with day name and date
	function getTodayButtonText(): string {
		const today = new Date();
		const dayName = today.toLocaleDateString('en-US', { weekday: 'short' });
		const day = today.getDate();
		const month = today.toLocaleDateString('en-US', { month: 'short' });
		return `Today ${dayName} ${day} ${month}`;
	}

	// Check if a date is today for highlighting
	function isToday(date: Date): boolean {
		const today = new Date();
		return date.toDateString() === today.toDateString();
	}

	// Employee Pool functionality removed - now using direct employee dragging

	function openShiftModal(shift: Shift | null = null, employee: Employee | null = null) {
		// Determine if this is a real existing shift or a mock shift for creation
		// Real shifts have valid UUIDs, mock shifts have empty string IDs
		const isExistingShift = shift && shift.id && shift.id.length > 0 && shift.id !== '';
		const modalMode = isExistingShift ? 'edit' : 'create';

		uiState.shiftModal = {
			isOpen: true,
			mode: modalMode,
			shift: shift,
			employee: employee
		};
	}

	function closeShiftModal() {
		uiState.shiftModal.isOpen = false;
		uiState.shiftModal.shift = null;
		uiState.shiftModal.employee = null;
	}

	async function handleShiftSave(shift: Shift) {
		try {
			// Double-check mode based on shift ID to prevent UUID errors
			const hasValidId = shift.id && shift.id.length > 0 && shift.id !== '';
			const shouldUpdate = uiState.shiftModal.mode === 'edit' && hasValidId;

			// Check for overlapping shifts before saving (only for assigned shifts)
			const dateStr = shift.date instanceof Date ? shift.date.toISOString().split('T')[0] : shift.date;
			if (!dateStr) {
				throw new Error('Missing required shift date for validation');
			}

			// Only check for overlaps if the shift is assigned to an employee
			if (shift.employeeId) {
				const overlapResult = await checkShiftOverlap(
					shift.employeeId,
					dateStr,
					shift.startTime,
					shift.endTime,
					shouldUpdate ? shift.id : undefined
				);

				if (overlapResult.hasOverlap) {
					const employee = weeklySchedule.employees.find(e => e.id === shift.employeeId);
					const employeeName = employee?.name || 'Employee';
					const conflictDetails = overlapResult.conflictingShifts
						.map(c => `${c.startTime}-${c.endTime}`)
						.join(', ');

					const errorMessage = `${employeeName} already has overlapping shifts on this date: ${conflictDetails}. Please adjust the time range.`;
					alert(errorMessage);
					return; // Don't save the shift
				}
			}

			let savedShift: Shift;

			if (!shouldUpdate) {
				// Create new shift
				savedShift = await shiftService.createShift({
					employeeId: shift.employeeId,
					venueId: shift.venueId || shift.locationId,
					date: dateStr,
					startTime: shift.startTime,
					endTime: shift.endTime,
					totalHours: shift.totalHours || shift.hoursWorked,
					dailyRate: shift.dailyRate,
					isPaid: shift.isPaid,
					advanceDeduction: shift.advanceDeduction,
					notes: shift.notes
				});

				// Optimistic UI update: Add the new shift to the local state immediately
				// Track this optimistic update to prevent race conditions
				pendingOptimisticUpdates.add(savedShift.id);

				const updatedShifts = [...weeklySchedule.shifts, savedShift];
				weeklySchedule = { ...weeklySchedule, shifts: updatedShifts };

				// Force a micro-task to ensure reactivity is processed
				await new Promise(resolve => setTimeout(resolve, 0));
			} else {
				// Update existing shift
				savedShift = await shiftService.updateShift(shift.id, {
					employeeId: shift.employeeId,
					venueId: shift.venueId || shift.locationId,
					date: dateStr,
					startTime: shift.startTime,
					endTime: shift.endTime,
					totalHours: shift.totalHours || shift.hoursWorked,
					dailyRate: shift.dailyRate,
					isPaid: shift.isPaid,
					advanceDeduction: shift.advanceDeduction,
					notes: shift.notes
				});

				// FIXED: Improved optimistic UI update with proper data merging
				const shiftIndex = weeklySchedule.shifts.findIndex(s => s.id === shift.id);
				if (shiftIndex !== -1) {


					// Track this optimistic update to prevent race conditions
					pendingOptimisticUpdates.add(savedShift.id);

					// CRITICAL FIX: Ensure the updated shift maintains all required properties
					// Merge the server response with the original shift to preserve any missing fields
					const originalShift = weeklySchedule.shifts[shiftIndex];
					const mergedShift: Shift = {
						...originalShift,  // Start with original shift data
						...savedShift,     // Override with server response
						// Explicitly ensure critical fields are preserved
						id: savedShift.id || originalShift.id,
						employeeId: savedShift.employeeId,
						locationId: savedShift.locationId || savedShift.venueId || originalShift.locationId,
						venueId: savedShift.venueId || savedShift.locationId || originalShift.venueId,
						dailyRate: savedShift.dailyRate,  // CRITICAL: Ensure daily rate is updated
						date: savedShift.date,
						startTime: savedShift.startTime,
						endTime: savedShift.endTime,
						totalHours: savedShift.totalHours || savedShift.hoursWorked,
						hoursWorked: savedShift.totalHours || savedShift.hoursWorked,
						isPaid: savedShift.isPaid,
						advanceDeduction: savedShift.advanceDeduction || 0,
						notes: savedShift.notes,
						createdAt: originalShift.createdAt,
						updatedAt: new Date()  // Mark as recently updated
					};



					// Create a new array with the updated shift to ensure reactivity
					const updatedShifts = [...weeklySchedule.shifts];
					updatedShifts[shiftIndex] = mergedShift;

					// Force complete reactivity by creating a new weeklySchedule object
					weeklySchedule = {
						...weeklySchedule,
						shifts: updatedShifts
					};



					// Force a micro-task to ensure reactivity is processed
					await new Promise(resolve => setTimeout(resolve, 0));
				} else {
					console.warn('⚠️ SchedulePage: Could not find shift to update in local state:', shift.id);
					// If we can't find the shift locally, force a full refresh
					await loadScheduleData();
				}
			}

			// Close modal immediately for better UX
			closeShiftModal();

			// Add a small delay to ensure UI updates are processed
			await new Promise(resolve => setTimeout(resolve, 50));

			// Refresh schedule data in the background to ensure consistency
			// This will catch any server-side calculations or changes we might have missed
			// Use exponential backoff to handle database consistency timing
			setTimeout(async () => {

				// Retry logic to handle database consistency issues
				let retryCount = 0;
				const maxRetries = 3;

				while (retryCount < maxRetries) {
					try {
						const refreshedData = await Promise.all([
							employeeService.fetchActiveEmployees(),
							venueService.fetchAllVenues(),
							shiftService.fetchShiftsForWeek(weeklySchedule.weekStartDate)
						]);

						// Check if the refreshed data contains our optimistic update
						const [employees, venues, shifts] = refreshedData;
						const updatedShift = shifts.find(s => s.id === savedShift.id);

						// IMPROVED: Check multiple fields for consistency, not just isPaid
						if (updatedShift &&
							updatedShift.isPaid === savedShift.isPaid &&
							updatedShift.dailyRate === savedShift.dailyRate &&
							updatedShift.totalHours === savedShift.totalHours) {
							// Database is consistent, safe to update
							weeklySchedule.employees = employees;
							weeklySchedule.venues = venues;
							weeklySchedule.shifts = shifts;

							// Remove from pending optimistic updates
							pendingOptimisticUpdates.delete(savedShift.id);
							break;
						} else if (retryCount < maxRetries - 1) {
							// Database not consistent yet, retry with exponential backoff
							await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 500));
							retryCount++;
						} else {
							// Max retries reached, keep optimistic update
							break;
						}
					} catch (error) {
						console.error('❌ SchedulePage: Background refresh error:', error);
						break;
					}
				}

			}, 1000); // Increased from 500ms to 1000ms for better database consistency

		} catch (error) {
			console.error('❌ SchedulePage: Failed to save shift:', error);
			// Show user-friendly error message
			alert(`Failed to save shift: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async function handleShiftDelete(shiftId: string) {
		try {
			// Delete shift from database using the service
			await shiftService.deleteShift(shiftId);

			// Refresh schedule data to reflect the deletion
			await loadScheduleData();
			closeShiftModal();
		} catch (error) {
			console.error('❌ SchedulePage: Failed to delete shift:', error);
			// Show user-friendly error message
			alert(`Failed to delete shift: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Bulk delete handlers

	async function handleBulkDeleteEmployee(employeeId: string, startDate: string, endDate: string): Promise<{ deletedCount: number; message: string }> {
		try {
			const result = await bulkDeleteEmployeeShifts(employeeId, startDate, endDate);

			// Refresh schedule data
			await loadScheduleData();

			return result;
		} catch (error) {
			console.error('❌ SchedulePage: Failed to bulk delete employee shifts:', error);
			throw error; // Re-throw for modal error handling
		}
	}

	async function handleBulkDeleteDate(date: string): Promise<{ deletedCount: number; message: string }> {
		try {
			const result = await bulkDeleteDateShifts(date);

			// Refresh schedule data
			await loadScheduleData();

			return result;
		} catch (error) {
			console.error('❌ SchedulePage: Failed to bulk delete date shifts:', error);
			throw error; // Re-throw for modal error handling
		}
	}

	async function handleBulkDeleteWeek(startDate: string, endDate: string): Promise<{ deletedCount: number; message: string }> {
		try {
			const result = await bulkDeleteWeekShifts(startDate, endDate);

			// Refresh schedule data
			await loadScheduleData();

			return result;
		} catch (error) {
			console.error('❌ SchedulePage: Failed to bulk delete week shifts:', error);
			throw error; // Re-throw for modal error handling
		}
	}

	// Bulk delete modal functions
	function openBulkDeleteModal() {
		isBulkDeleteModalOpen = true;
	}

	function closeBulkDeleteModal() {
		isBulkDeleteModalOpen = false;
	}

	// Duplicate week modal functions
	function openDuplicateWeekModal() {
		isDuplicateWeekModalOpen = true;
	}

	function closeDuplicateWeekModal() {
		isDuplicateWeekModalOpen = false;
	}

	async function handleDuplicateWeek(request: import('$lib/types.js').DuplicateWeekRequest): Promise<import('$lib/types.js').DuplicateWeekResult> {
		try {
			const result = await duplicateWeekShifts(
				request.sourceWeekStart,
				request.targetWeekStarts,
				request.conflictResolution,
				request.organizationId
			);

			// Refresh the schedule data to show the new shifts
			await loadScheduleData();

			return result;
		} catch (error) {
			console.error('❌ Schedule: Duplicate week failed:', error);
			throw error;
		}
	}



	// Helper functions for bulk delete modal
	function getShiftCountForEmployee(employeeId: string): number {
		return weeklySchedule.shifts.filter(shift => shift.employeeId === employeeId).length;
	}

	function getShiftCountForDate(date: Date): number {
		return weeklySchedule.shifts.filter(shift =>
			shift.date.toDateString() === date.toDateString()
		).length;
	}

	function getTotalShiftCountForWeek(): number {
		return weeklySchedule.shifts.length;
	}

	// Handle drag creation completion
	async function handleDragCreateComplete() {
		if (!uiState.dragCreateState.isActive) {
			return;
		}

		const { mode, direction, sourceShift, targetDates, targetEmployees, sourceEmployee, sourceDate } = uiState.dragCreateState;

		try {
			// Handle horizontal drag operations (across dates)
			if (direction === 'horizontal' && targetDates.length > 0) {
				if (mode === 'duplicate-shift-horizontal' && sourceShift) {
					// Create duplicate shifts for each target date
					for (const targetDate of targetDates) {
						const shiftData = {
							employeeId: sourceShift.employeeId,
							venueId: sourceShift.locationId || sourceShift.venueId || weeklySchedule.venues[0]?.id || '',
							date: targetDate.toISOString().split('T')[0],
							startTime: sourceShift.startTime,
							endTime: sourceShift.endTime,
							totalHours: sourceShift.hoursWorked || sourceShift.totalHours,
							dailyRate: sourceShift.dailyRate,
							isPaid: false, // New shifts start as unpaid
							advanceDeduction: 0,
							notes: sourceShift.notes || ''
						};

						await shiftService.createShift(shiftData);
					}
				} else if (mode === 'empty-cell-horizontal' && sourceEmployee) {
					// Create new shifts with default parameters for each target date
					const defaultVenue = weeklySchedule.venues[0];
					if (!defaultVenue) {
						throw new Error('No venues available for shift creation');
					}

					for (const targetDate of targetDates) {
						const shiftData = {
							employeeId: sourceEmployee.id,
							venueId: defaultVenue.id,
							date: targetDate.toISOString().split('T')[0],
							startTime: '09:00',
							endTime: '17:00',
							totalHours: 8,
							dailyRate: sourceEmployee.defaultDailyRate || 100,
							isPaid: false,
							advanceDeduction: 0,
							notes: ''
						};

						await shiftService.createShift(shiftData);
					}
				}
			}

			// Handle vertical drag operations (across employees) - OPTIMIZED FOR PERFORMANCE
			else if (direction === 'vertical' && targetEmployees.length > 0 && sourceDate) {

				if (mode === 'duplicate-shift-vertical' && sourceShift) {
					// PERFORMANCE OPTIMIZATION: Batch create duplicate shifts in parallel
					const shiftPromises = targetEmployees.map(targetEmployee => {
						const shiftData = {
							employeeId: targetEmployee.id,
							venueId: sourceShift.locationId || sourceShift.venueId || weeklySchedule.venues[0]?.id || '',
							date: sourceDate.toISOString().split('T')[0],
							startTime: sourceShift.startTime,
							endTime: sourceShift.endTime,
							totalHours: sourceShift.hoursWorked || sourceShift.totalHours,
							dailyRate: targetEmployee.defaultDailyRate || sourceShift.dailyRate,
							isPaid: false, // New shifts start as unpaid
							advanceDeduction: 0,
							notes: sourceShift.notes || ''
						};

						return shiftService.createShift(shiftData);
					});

					// Execute all shift creations in parallel instead of sequentially
					await Promise.all(shiftPromises);

				} else if (mode === 'empty-cell-vertical' && sourceDate) {
					// PERFORMANCE OPTIMIZATION: Batch create new shifts in parallel
					const defaultVenue = weeklySchedule.venues[0];
					if (!defaultVenue) {
						throw new Error('No venues available for shift creation');
					}

					const shiftPromises = targetEmployees.map(targetEmployee => {
						const shiftData = {
							employeeId: targetEmployee.id,
							venueId: defaultVenue.id,
							date: sourceDate.toISOString().split('T')[0],
							startTime: '09:00',
							endTime: '17:00',
							totalHours: 8,
							dailyRate: targetEmployee.defaultDailyRate || 100,
							isPaid: false,
							advanceDeduction: 0,
							notes: ''
						};

						return shiftService.createShift(shiftData);
					});

					// Execute all shift creations in parallel instead of sequentially
					await Promise.all(shiftPromises);
				}
			}

			// Handle unassigned vertical drag operations (across employees)
			else if (direction === 'vertical' && sourceDate && mode === 'unassigned-vertical' && uiState.dragCreateState.previewCells.length > 0) {
				// Create multiple unassigned shifts on the same date (vertical drag)
				const defaultVenue = weeklySchedule.venues[0];
				if (!defaultVenue) {
					throw new Error('No venues available for shift creation');
				}

				// Create multiple unassigned shifts based on the number of preview cells
				const numberOfShifts = uiState.dragCreateState.previewCells.length;

				for (let i = 0; i < numberOfShifts; i++) {
					const shiftData = {
						employeeId: null, // All shifts remain unassigned
						venueId: defaultVenue.id,
						date: sourceDate.toISOString().split('T')[0],
						startTime: '09:00',
						endTime: '17:00',
						totalHours: 8,
						dailyRate: 100, // Default rate for unassigned shifts
						isPaid: false,
						advanceDeduction: 0,
						notes: `Created from unassigned vertical drag (${i + 1} of ${numberOfShifts})`
					};

					await shiftService.createShift(shiftData);
				}
			}

			// Handle unassigned horizontal drag operations (across dates)
			else if (direction === 'horizontal' && targetDates.length > 0 && mode === 'unassigned-horizontal') {
				// Create unassigned shifts for each target date
				const defaultVenue = weeklySchedule.venues[0];
				if (!defaultVenue) {
					throw new Error('No venues available for shift creation');
				}

				for (const targetDate of targetDates) {
					const shiftData = {
						employeeId: null, // Unassigned
						venueId: defaultVenue.id,
						date: targetDate.toISOString().split('T')[0],
						startTime: '09:00',
						endTime: '17:00',
						totalHours: 8,
						dailyRate: 100, // Default rate for unassigned shifts
						isPaid: false,
						advanceDeduction: 0,
						notes: 'Created from unassigned horizontal drag'
					};

					await shiftService.createShift(shiftData);
				}
			}

			// Refresh the schedule after all operations
			await loadScheduleData();

		} catch (error) {
			console.error('❌ SchedulePage: Failed to create shifts:', error);
			// TODO: Show error notification to user
		} finally {
			// Reset drag create state
			uiState.dragCreateState = {
				isActive: false,
				mode: null,
				direction: null,
				sourceEmployee: null,
				sourceDate: null,
				sourceShift: null,
				targetDates: [],
				targetEmployees: [],
				previewCells: []
			};
		}
	}

	onMount(() => {
		loadScheduleData();
	});
</script>

<!-- Schedule Management -->
<div class="h-full flex flex-col">
	{#if isLoading}
		<div class="flex-1 flex items-center justify-center">
			<LoadingSpinner size="lg" message="Loading schedule..." />
		</div>
	{:else}
		<!-- Header -->
		<div class="bg-white border-b border-gray-200 px-6 py-4">
			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-4">
					<h1 class="text-2xl font-bold text-gray-900">Schedule</h1>

					<!-- Week Navigation -->
					<div class="flex items-center space-x-2 relative">
						<button
							onclick={() => navigateWeek('prev')}
							class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
							aria-label="Previous week"
						>
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
							</svg>
						</button>

						<!-- Week Display with Calendar Picker -->
						<div class="relative">
							<button
								onclick={openCalendarPicker}
								class="text-center px-3 py-2 hover:bg-gray-100 rounded-lg transition-colors group"
								aria-label="Select week from calendar"
							>
								<div class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
									{formatWeekRange(weeklySchedule.weekStartDate)}
								</div>
								<div class="text-xs text-gray-500 group-hover:text-blue-500 transition-colors">
									Click to select week
								</div>
							</button>

							<!-- Calendar Picker -->
							<WeekCalendarPicker
								selectedWeek={weeklySchedule.weekStartDate}
								isOpen={isCalendarPickerOpen}
								onWeekSelect={handleWeekSelect}
								onClose={closeCalendarPicker}
								position="bottom"
							/>
						</div>

						<button
							onclick={() => navigateWeek('next')}
							class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
							aria-label="Next week"
						>
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
							</svg>
						</button>
					</div>

					<Button
						variant="secondary"
						onclick={goToCurrentWeek}
						size="sm"
						className="!bg-teal-100 !text-teal-900 hover:!bg-teal-200 !border-teal-300 hover:!border-teal-400"
					>
						{getTodayButtonText()}
					</Button>
				</div>

				<div class="flex items-center space-x-3">
					<!-- Duplicate Week -->
					<Button
						variant="secondary"
						onclick={openDuplicateWeekModal}
					>
						<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
						</svg>
						Duplicate Week
					</Button>

					<!-- Bulk Delete Shifts -->
					<Button
						variant="secondary"
						onclick={openBulkDeleteModal}
					>
						<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
						</svg>
						Bulk Delete Shifts
					</Button>

					<!-- Create Shift -->
					<Button
						onclick={() => openShiftModal()}
					>
						<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
						</svg>
						Create Shift
					</Button>
				</div>
			</div>

			<!-- Quick Stats -->
			<div class="flex items-center space-x-6 mt-4 text-sm">
				<div class="flex items-center space-x-2">
					<div class="w-3 h-3 bg-blue-500 rounded-full"></div>
					<span class="text-gray-600">{weeklySchedule.shifts.length} Total Shifts</span>
				</div>
				<div class="flex items-center space-x-2">
					<div class="w-3 h-3 bg-red-500 rounded-full"></div>
					<span class="text-gray-600">{unassignedShifts.length} Unassigned</span>
				</div>
				<div class="flex items-center space-x-2">
					<div class="w-3 h-3 bg-green-500 rounded-full"></div>
					<span class="text-gray-600">{weeklySchedule.employees.length} Active Employees</span>
				</div>
			</div>
		</div>

		<!-- Main Content -->
		<div class="flex-1 overflow-auto">
			<!-- Schedule Grid -->
			<ScheduleGrid
				schedule={weeklySchedule}
				{weekDates}
				{uiState}
				{isToday}
				onShiftClick={openShiftModal}
				onShiftCreate={openShiftModal}
				onShiftDelete={handleShiftDelete}
				onDragCreateComplete={handleDragCreateComplete}
			/>
		</div>
	{/if}
</div>

<!-- Modals -->
{#if uiState.shiftModal.isOpen}
	<ShiftModal
		shift={uiState.shiftModal.shift}
		employees={weeklySchedule.employees}
		venues={weeklySchedule.venues}
		mode={uiState.shiftModal.mode}
		employee={uiState.shiftModal.employee}
		date={uiState.shiftModal.shift?.date || null}
		onSave={handleShiftSave}
		onDelete={handleShiftDelete}
		onClose={closeShiftModal}
	/>
{/if}

{#if isBulkDeleteModalOpen}
	<BulkDeleteModal
		isOpen={isBulkDeleteModalOpen}
		employees={weeklySchedule.employees}
		{weekDates}
		onClose={closeBulkDeleteModal}
		onDeleteEmployee={handleBulkDeleteEmployee}
		onDeleteDate={handleBulkDeleteDate}
		onDeleteWeek={handleBulkDeleteWeek}
		{getShiftCountForEmployee}
		{getShiftCountForDate}
		{getTotalShiftCountForWeek}
	/>
{/if}

{#if isDuplicateWeekModalOpen}
	<DuplicateWeekModal
		isOpen={isDuplicateWeekModalOpen}
		currentWeekStart={weeklySchedule.weekStartDate}
		schedule={weeklySchedule}
		organizationId="1101f4b7-7e64-45b9-b690-de9a451426a4"
		onClose={closeDuplicateWeekModal}
		onDuplicate={handleDuplicateWeek}
	/>
{/if}



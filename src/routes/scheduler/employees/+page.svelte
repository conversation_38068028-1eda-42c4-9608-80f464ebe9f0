<script lang="ts">
	/**
	 * Employee Management Page
	 * Tallyca-style employee management with modals and bulk operations
	 * Following Code Complete principles: Clear separation of concerns
	 */
	
	import { onMount } from 'svelte';
	import { employeeService } from '$lib/services/index.js';
	import { <PERSON>ading<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '$lib/components/ui/index.js';
	import EmployeeModal from '$lib/components/EmployeeModal.svelte';
	import BulkEmployeeUpload from '$lib/components/ui/BulkEmployeeUpload.svelte';
	import BulkEmployeeDeleteModal from '$lib/components/BulkEmployeeDeleteModal.svelte';
	import type { Employee } from '$lib/types.js';
	import { formatCurrency } from '$lib/utils.js';
	
	// State
	let isLoading = $state(true);
	let employees: Employee[] = $state([]);
	let showEmployeeModal = $state(false);
	let showBulkUpload = $state(false);
	let showBulkDeleteModal = $state(false);
	let editingEmployee: Employee | null = $state(null);
	let searchTerm = $state('');
	let filterRole = $state('all');
	let showInactive = $state(false);
	
	// Computed
	let filteredEmployees = $derived(() => {
		let filtered = employees;
		
		// Filter by active status
		if (!showInactive) {
			filtered = filtered.filter(emp => emp.isActive);
		}
		
		// Filter by role
		if (filterRole !== 'all') {
			filtered = filtered.filter(emp => emp.role === filterRole);
		}
		
		// Filter by search term
		if (searchTerm.trim()) {
			const term = searchTerm.toLowerCase();
			filtered = filtered.filter(emp =>
				(emp.name || emp.fullName || '').toLowerCase().includes(term) ||
				emp.fullName?.toLowerCase().includes(term) ||
				emp.role.toLowerCase().includes(term)
			);
		}
		
		return filtered;
	});
	
	let uniqueRoles = $derived(() => {
		const roles = [...new Set(employees.map(emp => emp.role))];
		return roles.sort();
	});
	
	// Functions
	async function loadEmployees() {
		try {
			isLoading = true;
			employees = await employeeService.fetchAllEmployees();
		} catch (error) {
			console.error('❌ Failed to load employees:', error);
		} finally {
			isLoading = false;
		}
	}
	
	function openEmployeeModal(employee?: Employee) {
		editingEmployee = employee || null;
		showEmployeeModal = true;
	}
	
	function closeEmployeeModal() {
		showEmployeeModal = false;
		editingEmployee = null;
	}
	
	async function handleEmployeeSave(employee: Employee) {
		try {
			console.log('🔄 EmployeePage: Saving employee:', employee);

			if (editingEmployee) {
				// Update existing employee
				await employeeService.updateEmployee(employee.id, {
					name: employee.name || employee.fullName || '',
					defaultDailyRate: employee.defaultDailyRate,
					role: employee.role,
					isActive: employee.isActive ?? true
				});
				console.log('✅ EmployeePage: Employee updated successfully');
			} else {
				// Create new employee
				await employeeService.createEmployee({
					name: employee.name || employee.fullName || '',
					defaultDailyRate: employee.defaultDailyRate,
					role: employee.role,
					isActive: employee.isActive ?? true
				});
				console.log('✅ EmployeePage: Employee created successfully');
			}

			await loadEmployees();
			closeEmployeeModal();
		} catch (error) {
			console.error('❌ EmployeePage: Failed to save employee:', error);
			// TODO: Show error message to user
		}
	}

	async function handleEmployeeDelete(employeeId: string) {
		try {
			console.log('🔄 EmployeePage: Deleting employee:', employeeId);
			await employeeService.deleteEmployee(employeeId);
			console.log('✅ EmployeePage: Employee deleted successfully');

			await loadEmployees();
			closeEmployeeModal();
		} catch (error) {
			console.error('❌ EmployeePage: Failed to delete employee:', error);
			// TODO: Show error message to user
		}
	}
	
	function openBulkUpload() {
		showBulkUpload = true;
	}
	
	function closeBulkUpload() {
		showBulkUpload = false;
	}

	async function handleBulkUploadComplete() {
		await loadEmployees();
		closeBulkUpload();
	}

	function openBulkDeleteModal() {
		showBulkDeleteModal = true;
	}

	function closeBulkDeleteModal() {
		showBulkDeleteModal = false;
	}

	async function handleBulkEmployeeDelete(employeeIds: string[]): Promise<{ deletedCount: number; message: string }> {
		try {
			console.log('🔄 EmployeePage: Bulk deleting employees:', employeeIds);
			const result = await employeeService.bulkDeleteEmployees(employeeIds);
			console.log('✅ EmployeePage: Employees deleted successfully:', result);

			// Refresh employee list
			await loadEmployees();
			closeBulkDeleteModal();

			return result;
		} catch (error) {
			console.error('❌ EmployeePage: Failed to bulk delete employees:', error);
			throw error; // Re-throw for modal error handling
		}
	}
	
	async function toggleEmployeeStatus(employee: Employee) {
		try {
			await employeeService.toggleEmployeeStatus(employee.id, !employee.isActive);
			await loadEmployees();
		} catch (error) {
			console.error('❌ Failed to toggle employee status:', error);
		}
	}
	
	onMount(() => {
		loadEmployees();
	});
</script>

<!-- Employee Management -->
<div class="p-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between mb-6">
		<div>
			<h1 class="text-2xl font-bold text-gray-900 mb-2">Employee Management</h1>
			<p class="text-gray-600">Manage your restaurant staff and their details</p>
		</div>
		
		<div class="flex items-center space-x-3">
			<Button
				variant="outline"
				onclick={openBulkUpload}
			>
				<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
				</svg>
				Bulk Add
			</Button>

			{#if employees.length > 0}
				<Button
					variant="danger"
					onclick={openBulkDeleteModal}
				>
					<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
					</svg>
					Bulk Delete
				</Button>
			{/if}

			<Button
				onclick={() => openEmployeeModal()}
			>
				<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
				</svg>
				Add Employee
			</Button>
		</div>
	</div>
	
	{#if isLoading}
		<div class="flex items-center justify-center h-64">
			<LoadingSpinner size="lg" message="Loading employees..." />
		</div>
	{:else}
		<!-- Filters -->
		<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
			<div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
				<div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
					<!-- Search -->
					<div class="relative">
						<svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
						</svg>
						<input
							type="text"
							bind:value={searchTerm}
							placeholder="Search employees..."
							class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
						/>
					</div>
					
					<!-- Role Filter -->
					<select
						bind:value={filterRole}
						class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					>
						<option value="all">All Roles</option>
						{#each uniqueRoles() as role}
							<option value={role}>{role}</option>
						{/each}
					</select>
				</div>
				
				<!-- Show Inactive Toggle -->
				<label class="flex items-center space-x-2">
					<input
						type="checkbox"
						bind:checked={showInactive}
						class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
					/>
					<span class="text-sm text-gray-700">Show inactive employees</span>
				</label>
			</div>
		</div>
		
		<!-- Employee List -->
		<div class="bg-white rounded-lg shadow-sm border border-gray-200">
			{#if filteredEmployees().length === 0}
				<div class="p-8 text-center">
					<svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
					</svg>
					<h3 class="text-lg font-medium text-gray-900 mb-2">No employees found</h3>
					<p class="text-gray-500 mb-4">
						{searchTerm || filterRole !== 'all' ? 'Try adjusting your filters' : 'Get started by adding your first employee'}
					</p>
					{#if !searchTerm && filterRole === 'all'}
						<Button onclick={() => openEmployeeModal()}>
							Add Employee
						</Button>
					{/if}
				</div>
			{:else}
				<div class="overflow-hidden">
					<table class="min-w-full divide-y divide-gray-200">
						<thead class="bg-gray-50">
							<tr>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Daily Rate</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
								<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
							</tr>
						</thead>
						<tbody class="bg-white divide-y divide-gray-200">
							{#each filteredEmployees() as employee}
								<tr class="hover:bg-gray-50">
									<td class="px-6 py-4 whitespace-nowrap">
										<div class="flex items-center">
											<div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
												<span class="text-xs font-medium text-blue-700">
													{(employee.name || employee.fullName || '').split(' ').map((n: string) => n[0]).join('')}
												</span>
											</div>
											<div class="ml-4">
												<div class="text-sm font-medium text-gray-900">{employee.fullName || employee.name}</div>
											</div>
										</div>
									</td>
									<td class="px-6 py-4 whitespace-nowrap">
										<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
											{employee.role}
										</span>
									</td>
									<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
										{formatCurrency(employee.defaultDailyRate)}
									</td>
									<td class="px-6 py-4 whitespace-nowrap">
										<button
											onclick={() => toggleEmployeeStatus(employee)}
											class="inline-flex px-2 py-1 text-xs font-semibold rounded-full transition-colors
												{employee.isActive 
													? 'bg-green-100 text-green-800 hover:bg-green-200' 
													: 'bg-red-100 text-red-800 hover:bg-red-200'}"
										>
											{employee.isActive ? 'Active' : 'Inactive'}
										</button>
									</td>
									<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
										<button
											onclick={() => openEmployeeModal(employee)}
											class="text-blue-600 hover:text-blue-900 transition-colors"
										>
											Edit
										</button>
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			{/if}
		</div>
	{/if}
</div>

<!-- Modals -->
{#if showEmployeeModal}
	<EmployeeModal
		employee={editingEmployee}
		mode={editingEmployee ? 'edit' : 'create'}
		onSave={handleEmployeeSave}
		onDelete={handleEmployeeDelete}
		onClose={closeEmployeeModal}
	/>
{/if}

{#if showBulkUpload}
	<BulkEmployeeUpload
		onComplete={handleBulkUploadComplete}
		onClose={closeBulkUpload}
	/>
{/if}

{#if showBulkDeleteModal}
	<BulkEmployeeDeleteModal
		isOpen={showBulkDeleteModal}
		{employees}
		onClose={closeBulkDeleteModal}
		onBulkDelete={handleBulkEmployeeDelete}
	/>
{/if}

<script lang="ts">
	/**
	 * Scheduler Module Layout
	 * Provides shared context and styling for all scheduler pages
	 * Following Code Complete principles: Clear module organization
	 */
	
	let { children } = $props();
</script>

<!-- Scheduler Module Container -->
<div class="scheduler-module">
	{@render children()}
</div>

<style>
	.scheduler-module {
		/* Tallyca-style module container */
		@apply h-full;
	}
</style>

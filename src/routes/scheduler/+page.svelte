<script lang="ts">
	/**
	 * Scheduler Overview Page
	 * Dashboard-style overview of scheduling metrics and quick actions
	 * Following Tallyca design patterns: Cards, metrics, clean layout
	 */
	
	import { onMount } from 'svelte';
	import { employeeService, shiftService } from '$lib/services/index.js';
	import { LoadingSpinner } from '$lib/components/ui/index.js';
	import { formatCurrency } from '$lib/utils.js';

	// State
	let isLoading = $state(true);
	let lastRefresh = $state(new Date());
	let metrics = $state({
		totalEmployees: 0,
		activeEmployees: 0,
		totalShifts: 0,
		unassignedShifts: 0,
		weeklyPayroll: 0,
		unpaidShifts: 0
	});

	// Load overview data
	async function loadOverviewData() {
		try {
			isLoading = true;
			console.log('🔄 SchedulerOverview: Loading overview data...');

			// Load basic metrics
			const [employees, shifts] = await Promise.all([
				employeeService.fetchActiveEmployees(),
				shiftService.fetchShiftsForWeek(new Date())
			]);

			console.log('✅ SchedulerOverview: Data loaded:', {
				employees: employees.length,
				shifts: shifts.length
			});

			// Calculate metrics
			metrics.totalEmployees = employees.length;
			metrics.activeEmployees = employees.filter(e => e.isActive).length;
			metrics.totalShifts = shifts.length;
			metrics.unassignedShifts = shifts.filter(s => !s.employeeId).length;
			metrics.weeklyPayroll = shifts.reduce((sum, shift) => sum + (shift.dailyRate || 0), 0);
			metrics.unpaidShifts = shifts.filter(s => !s.isPaid).length;

			lastRefresh = new Date();

		} catch (error) {
			console.error('❌ SchedulerOverview: Failed to load overview data:', error);
		} finally {
			isLoading = false;
		}
	}

	// Manual refresh function
	async function handleRefresh() {
		await loadOverviewData();
	}

	// Auto-refresh when page becomes visible
	function handleVisibilityChange() {
		if (!document.hidden) {
			// Page became visible, refresh data
			loadOverviewData();
		}
	}

	onMount(() => {
		loadOverviewData();

		// Add visibility change listener for auto-refresh
		document.addEventListener('visibilitychange', handleVisibilityChange);

		// Cleanup
		return () => {
			document.removeEventListener('visibilitychange', handleVisibilityChange);
		};
	});
</script>

<!-- Scheduler Overview -->
<div class="p-6">
	{#if isLoading}
		<div class="flex items-center justify-center h-64">
			<LoadingSpinner size="lg" message="Loading overview..." />
		</div>
	{:else}
		<!-- Page Header -->
		<div class="mb-8">
			<div class="flex justify-between items-start">
				<div>
					<h1 class="text-2xl font-bold text-gray-900 mb-2">Scheduler Overview</h1>
					<p class="text-gray-600">Monitor your restaurant scheduling at a glance</p>
				</div>
				<div class="text-right">
					<button
						onclick={handleRefresh}
						disabled={isLoading}
						class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
					>
						<svg class="w-4 h-4 mr-2 {isLoading ? 'animate-spin' : ''}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
						</svg>
						{isLoading ? 'Refreshing...' : 'Refresh'}
					</button>
					<p class="text-xs text-gray-500 mt-1">
						Last updated: {lastRefresh.toLocaleTimeString()}
					</p>
				</div>
			</div>
		</div>
		
		<!-- Metrics Grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
			<!-- Employee Metrics -->
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-sm font-medium text-gray-600">Active Employees</p>
						<p class="text-2xl font-bold text-gray-900">{metrics.activeEmployees}</p>
						<p class="text-xs text-gray-500">of {metrics.totalEmployees} total</p>
					</div>
					<div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
						<svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
						</svg>
					</div>
				</div>
			</div>
			
			<!-- Shift Metrics -->
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-sm font-medium text-gray-600">This Week's Shifts</p>
						<p class="text-2xl font-bold text-gray-900">{metrics.totalShifts}</p>
						<p class="text-xs text-red-500">{metrics.unassignedShifts} unassigned</p>
					</div>
					<div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
						<svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
						</svg>
					</div>
				</div>
			</div>
			
			<!-- Payroll Metrics -->
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-sm font-medium text-gray-600">Weekly Payroll</p>
						<p class="text-2xl font-bold text-gray-900">{formatCurrency(metrics.weeklyPayroll)}</p>
						<p class="text-xs text-orange-500">{metrics.unpaidShifts} unpaid shifts</p>
					</div>
					<div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
						<svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
						</svg>
					</div>
				</div>
			</div>
		</div>
		
		<!-- Quick Actions -->
		<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
			<h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
			<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
				<a
					href="/scheduler/employees"
					class="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
				>
					<div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
						<svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
						</svg>
					</div>
					<div>
						<p class="font-medium text-gray-900">Manage Employees</p>
						<p class="text-sm text-gray-500">Add, edit, or view staff</p>
					</div>
				</a>
				
				<a
					href="/scheduler/schedule"
					class="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
				>
					<div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
						<svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
						</svg>
					</div>
					<div>
						<p class="font-medium text-gray-900">View Schedule</p>
						<p class="text-sm text-gray-500">Manage shifts and assignments</p>
					</div>
				</a>
				
				<button
					onclick={handleRefresh}
					disabled={isLoading}
					class="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
				>
					<div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
						<svg class="w-5 h-5 text-purple-600 {isLoading ? 'animate-spin' : ''}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
						</svg>
					</div>
					<div>
						<p class="font-medium text-gray-900">{isLoading ? 'Refreshing...' : 'Refresh Data'}</p>
						<p class="text-sm text-gray-500">Update all metrics</p>
					</div>
				</button>
			</div>
		</div>
	{/if}
</div>

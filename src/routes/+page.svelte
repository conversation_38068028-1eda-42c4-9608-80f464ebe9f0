<script lang="ts">
	/**
	 * Root Page - Redirect to Scheduler
	 * Dashboard functionality has been removed, redirecting to main scheduler interface
	 * Following Code Complete principles: Clear navigation, focused functionality
	 */

	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';

	onMount(() => {
		// Redirect to scheduler as the main interface
		goto('/scheduler/schedule', { replaceState: true });
	});
</script>

<!-- Redirecting to Scheduler -->
<div class="flex items-center justify-center h-screen">
	<div class="text-center">
		<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
		<p class="text-gray-600">Redirecting to scheduler...</p>
	</div>
</div>
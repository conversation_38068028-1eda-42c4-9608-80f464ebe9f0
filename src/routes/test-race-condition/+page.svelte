<script lang="ts">
	/**
	 * Test Page for UI Synchronization Race Condition Investigation
	 * Reproduces the specific issue where weekly totals flash correct values then revert
	 */
	
	import { onMount } from 'svelte';
	import { formatCurrency, calculateEmployeeWeeklySummary } from '$lib/utils.js';
	import type { Employee, Shift } from '$lib/types.js';

	// Test employee
	let testEmployee: Employee = {
		id: 'test-employee-1',
		name: '<PERSON>',
		fullName: '<PERSON>',
		role: 'FOH',
		defaultDailyRate: 120,
		defaultDailyWorkingHours: 8,
		defaultHourlyRate: 15,
		isActive: true,
		createdAt: new Date()
	};

	// Test shifts - simulating the scenario
	let testShifts: Shift[] = $state([
		{
			id: 'shift-monday',
			employeeId: 'test-employee-1',
			venueId: 'venue-1',
			locationId: 'venue-1',
			date: new Date('2024-01-15'), // Monday
			startTime: '09:00',
			endTime: '17:00',
			totalHours: 8,
			hoursWorked: 8,
			dailyRate: 120,
			isPaid: false, // Initially unpaid
			advanceDeduction: 0,
			notes: 'Monday shift',
			createdAt: new Date(),
			updatedAt: new Date()
		},
		{
			id: 'shift-tuesday',
			employeeId: 'test-employee-1',
			venueId: 'venue-1',
			locationId: 'venue-1',
			date: new Date('2024-01-16'), // Tuesday
			startTime: '10:00',
			endTime: '18:00',
			totalHours: 8,
			hoursWorked: 8,
			dailyRate: 120,
			isPaid: false, // Initially unpaid
			advanceDeduction: 0,
			notes: 'Tuesday shift',
			createdAt: new Date(),
			updatedAt: new Date()
		}
	]);

	// Simulate the race condition scenario
	let isSimulatingRaceCondition = $state(false);
	let raceConditionLog: Array<{
		timestamp: string;
		action: string;
		totalPay: number;
		paidAmount: number;
		unpaidAmount: number;
	}> = $state([]);

	// Reactive calculation (simulates ScheduleGrid behavior)
	let weeklySummary = $derived(calculateEmployeeWeeklySummary(testEmployee, testShifts));

	// Log every calculation for race condition analysis
	$effect(() => {
		if (isSimulatingRaceCondition) {
			raceConditionLog.push({
				timestamp: new Date().toISOString(),
				action: 'Weekly summary recalculated',
				totalPay: weeklySummary.totalPay,
				paidAmount: weeklySummary.paidAmount,
				unpaidAmount: weeklySummary.unpaidAmount
			});
		}
	});

	// Simulate the exact race condition scenario
	async function simulateRaceCondition() {
		console.log('🔄 Starting race condition simulation...');
		isSimulatingRaceCondition = true;
		raceConditionLog = [];

		// Step 1: Mark Monday as paid (optimistic update)
		console.log('🔄 Step 1: Optimistic update - marking Monday as paid');
		const shiftIndex = testShifts.findIndex(s => s.id === 'shift-monday');
		if (shiftIndex !== -1) {
			const updatedShifts = [...testShifts];
			updatedShifts[shiftIndex] = {
				...updatedShifts[shiftIndex],
				isPaid: true,
				updatedAt: new Date()
			};
			testShifts = updatedShifts;
		}

		// Step 2: Simulate micro-task delay (like in handleShiftSave)
		await new Promise(resolve => setTimeout(resolve, 0));

		// Step 3: Simulate 50ms delay (like in handleShiftSave)
		await new Promise(resolve => setTimeout(resolve, 50));

		// Step 4: Simulate background refresh after 500ms (like in handleShiftSave)
		setTimeout(() => {
			console.log('🔄 Step 4: Background refresh - simulating loadScheduleData()');
			
			// Simulate what happens when loadScheduleData() overwrites the optimistic update
			// This could happen if the database hasn't been updated yet or returns stale data
			const refreshedShifts = [
				{
					id: 'shift-monday',
					employeeId: 'test-employee-1',
					venueId: 'venue-1',
					locationId: 'venue-1',
					date: new Date('2024-01-15'),
					startTime: '09:00',
					endTime: '17:00',
					totalHours: 8,
					hoursWorked: 8,
					dailyRate: 120,
					isPaid: false, // ❌ REVERTED! Database might not have been updated yet
					advanceDeduction: 0,
					notes: 'Monday shift',
					createdAt: new Date(),
					updatedAt: new Date()
				},
				{
					id: 'shift-tuesday',
					employeeId: 'test-employee-1',
					venueId: 'venue-1',
					locationId: 'venue-1',
					date: new Date('2024-01-16'),
					startTime: '10:00',
					endTime: '18:00',
					totalHours: 8,
					hoursWorked: 8,
					dailyRate: 120,
					isPaid: false,
					advanceDeduction: 0,
					notes: 'Tuesday shift',
					createdAt: new Date(),
					updatedAt: new Date()
				}
			];

			console.log('🔄 Background refresh: Overwriting optimistic update with stale data');
			testShifts = refreshedShifts;

			// Step 5: Simulate another background refresh with correct data after database consistency
			setTimeout(() => {
				console.log('🔄 Step 5: Second background refresh - database now consistent');
				const finalShifts = [
					{
						id: 'shift-monday',
						employeeId: 'test-employee-1',
						venueId: 'venue-1',
						locationId: 'venue-1',
						date: new Date('2024-01-15'),
						startTime: '09:00',
						endTime: '17:00',
						totalHours: 8,
						hoursWorked: 8,
						dailyRate: 120,
						isPaid: true, // ✅ NOW CORRECT - database is consistent
						advanceDeduction: 0,
						notes: 'Monday shift',
						createdAt: new Date(),
						updatedAt: new Date()
					},
					{
						id: 'shift-tuesday',
						employeeId: 'test-employee-1',
						venueId: 'venue-1',
						locationId: 'venue-1',
						date: new Date('2024-01-16'),
						startTime: '10:00',
						endTime: '18:00',
						totalHours: 8,
						hoursWorked: 8,
						dailyRate: 120,
						isPaid: false,
						advanceDeduction: 0,
						notes: 'Tuesday shift',
						createdAt: new Date(),
						updatedAt: new Date()
					}
				];

				testShifts = finalShifts;
				
				setTimeout(() => {
					isSimulatingRaceCondition = false;
					console.log('✅ Race condition simulation complete');
				}, 100);
			}, 1000); // Simulate database consistency delay
		}, 500);
	}

	// Reset to initial state
	function resetToInitial() {
		isSimulatingRaceCondition = false;
		raceConditionLog = [];
		
		testShifts = [
			{
				id: 'shift-monday',
				employeeId: 'test-employee-1',
				venueId: 'venue-1',
				locationId: 'venue-1',
				date: new Date('2024-01-15'),
				startTime: '09:00',
				endTime: '17:00',
				totalHours: 8,
				hoursWorked: 8,
				dailyRate: 120,
				isPaid: false,
				advanceDeduction: 0,
				notes: 'Monday shift',
				createdAt: new Date(),
				updatedAt: new Date()
			},
			{
				id: 'shift-tuesday',
				employeeId: 'test-employee-1',
				venueId: 'venue-1',
				locationId: 'venue-1',
				date: new Date('2024-01-16'),
				startTime: '10:00',
				endTime: '18:00',
				totalHours: 8,
				hoursWorked: 8,
				dailyRate: 120,
				isPaid: false,
				advanceDeduction: 0,
				notes: 'Tuesday shift',
				createdAt: new Date(),
				updatedAt: new Date()
			}
		];
	}

	onMount(() => {
		console.log('🔄 Race condition test page mounted');
	});
</script>

<div class="container mx-auto p-6">
	<h1 class="text-3xl font-bold text-gray-900 mb-6">UI Synchronization Race Condition Investigation</h1>
	
	<div class="bg-white rounded-lg shadow p-6 mb-6">
		<h2 class="text-xl font-semibold text-gray-800 mb-4">Race Condition Simulation</h2>
		<p class="text-gray-600 mb-4">
			This test simulates the exact race condition where weekly totals briefly show correct values then revert.
		</p>
		
		<!-- Current Weekly Summary -->
		<div class="bg-gray-50 rounded-lg p-4 mb-6">
			<h3 class="text-lg font-medium text-gray-700 mb-3">Current Weekly Summary</h3>
			<div class="grid grid-cols-2 md:grid-cols-4 gap-4">
				<div class="text-center">
					<div class="text-2xl font-bold text-gray-900">{weeklySummary.shiftsCount}</div>
					<div class="text-sm text-gray-500">Shifts</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-gray-900">{formatCurrency(weeklySummary.totalPay)}</div>
					<div class="text-sm text-gray-500">Total Pay</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-green-600">{formatCurrency(weeklySummary.paidAmount)}</div>
					<div class="text-sm text-gray-500">Paid</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-red-600">{formatCurrency(weeklySummary.unpaidAmount)}</div>
					<div class="text-sm text-gray-500">Unpaid</div>
				</div>
			</div>
		</div>

		<!-- Test Actions -->
		<div class="mb-6">
			<h3 class="text-lg font-medium text-gray-700 mb-3">Test Actions</h3>
			<div class="flex flex-wrap gap-3">
				<button
					onclick={simulateRaceCondition}
					disabled={isSimulatingRaceCondition}
					class="bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors"
				>
					{isSimulatingRaceCondition ? 'Simulating...' : 'Simulate Race Condition'}
				</button>
				<button
					onclick={resetToInitial}
					disabled={isSimulatingRaceCondition}
					class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors"
				>
					Reset to Initial State
				</button>
			</div>
		</div>

		<!-- Individual Shifts -->
		<div class="mb-6">
			<h3 class="text-lg font-medium text-gray-700 mb-3">Individual Shifts</h3>
			<div class="space-y-3">
				{#each testShifts as shift}
					<div class="border border-gray-200 rounded-lg p-4">
						<div class="flex items-center justify-between">
							<div>
								<div class="font-medium text-gray-900">
									{shift.notes} • {shift.date.toLocaleDateString()}
								</div>
								<div class="text-sm text-gray-500">
									{shift.startTime} - {shift.endTime} • {shift.totalHours}h • Rate: {formatCurrency(shift.dailyRate)}
								</div>
							</div>
							<div class="text-right">
								<div class="font-medium text-gray-900">
									Pay: {formatCurrency(120)} {/* Using employee default daily rate */}
								</div>
								<div class="text-sm {shift.isPaid ? 'text-green-600' : 'text-red-600'}">
									{shift.isPaid ? 'PAID' : 'UNPAID'}
								</div>
							</div>
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- Race Condition Log -->
		{#if raceConditionLog.length > 0}
			<div>
				<h3 class="text-lg font-medium text-gray-700 mb-3">Race Condition Log</h3>
				<div class="max-h-96 overflow-y-auto space-y-2">
					{#each raceConditionLog as entry}
						<div class="border border-gray-200 rounded p-3 text-sm">
							<div class="flex justify-between items-center mb-2">
								<span class="font-medium text-gray-900">{entry.action}</span>
								<span class="text-gray-500">{new Date(entry.timestamp).toLocaleTimeString()}</span>
							</div>
							<div class="grid grid-cols-3 gap-4 text-xs">
								<div>
									<span class="text-gray-600">Total:</span> {formatCurrency(entry.totalPay)}
								</div>
								<div>
									<span class="text-gray-600">Paid:</span> {formatCurrency(entry.paidAmount)}
								</div>
								<div>
									<span class="text-gray-600">Unpaid:</span> {formatCurrency(entry.unpaidAmount)}
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/if}
	</div>

	<!-- Analysis -->
	<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
		<h3 class="text-lg font-medium text-yellow-800 mb-2">Race Condition Analysis</h3>
		<ul class="text-sm text-yellow-700 space-y-1">
			<li>• <strong>Step 1:</strong> Optimistic update immediately shows correct values</li>
			<li>• <strong>Step 2:</strong> Background refresh (500ms) overwrites with stale database data</li>
			<li>• <strong>Step 3:</strong> Values revert to incorrect state</li>
			<li>• <strong>Step 4:</strong> Eventually database becomes consistent and values correct again</li>
			<li>• <strong>Root Cause:</strong> Background refresh happens before database transaction commits</li>
		</ul>
	</div>
</div>

<script lang="ts">
	/**
	 * Server-Side Calculation Testing Page
	 * ===================================
	 * Comprehensive testing interface for new server-side RPC calculation functions
	 * Following Code Complete principles: Thorough testing, clear feedback
	 */

	import { onMount } from 'svelte';
	import { serverSideCalculationService } from '$lib/services/serverSideCalculationService.js';
	import { formatCurrency } from '$lib/utils.js';

	// Test state
	let testResults = $state([]);
	let isRunning = $state(false);
	let currentTest = $state('');

	// Sample test data
	const testShiftId = 'test-shift-123';
	const testEmployeeId = 'test-employee-456';
	const testVenueId = 'test-venue-789';
	const testWeekStart = new Date('2024-01-15'); // Monday

	// Test scenarios
	const testScenarios = [
		{
			name: 'Shift Compensation Calculation',
			description: 'Test individual shift pay calculation with server-side RPC',
			test: async () => {
				try {
					const result = await serverSideCalculationService.calculateShiftCompensation(testShiftId);
					return {
						success: true,
						data: result,
						message: `Calculated compensation: ${formatCurrency(result.totalCompensation)} for ${result.hoursWorked}h`
					};
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Shift compensation calculation failed'
					};
				}
			}
		},
		{
			name: 'Pay Rules Validation',
			description: 'Test server-side pay validation with various scenarios',
			test: async () => {
				const scenarios = [
					{ employeeId: testEmployeeId, hours: 8, rate: 120, expected: 'valid' },
					{ employeeId: testEmployeeId, hours: 8, rate: 50, expected: 'below_minimum' },
					{ employeeId: null, hours: 6, rate: 90, expected: 'unassigned_valid' },
					{ employeeId: null, hours: 8, rate: 80, expected: 'below_minimum' }
				];

				const results = [];
				for (const scenario of scenarios) {
					try {
						const validation = await serverSideCalculationService.validateShiftPayRules(
							scenario.employeeId,
							scenario.hours,
							scenario.rate
						);
						results.push({
							scenario: `${scenario.hours}h @ ${formatCurrency(scenario.rate)}`,
							valid: validation.isValid,
							message: validation.validationMessage,
							suggested: formatCurrency(validation.suggestedRate)
						});
					} catch (error) {
						results.push({
							scenario: `${scenario.hours}h @ ${formatCurrency(scenario.rate)}`,
							valid: false,
							message: error.message,
							suggested: 'N/A'
						});
					}
				}

				return {
					success: true,
					data: results,
					message: `Tested ${results.length} validation scenarios`
				};
			}
		},
		{
			name: 'Employee Weekly Totals',
			description: 'Test employee weekly summary calculation',
			test: async () => {
				try {
					const result = await serverSideCalculationService.calculateEmployeeWeeklyTotals(
						testEmployeeId,
						testWeekStart
					);
					return {
						success: true,
						data: result,
						message: `Employee ${result.employeeName}: ${result.totalShifts} shifts, ${formatCurrency(result.totalCompensation)} total`
					};
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Employee weekly calculation failed'
					};
				}
			}
		},
		{
			name: 'Venue Weekly Totals',
			description: 'Test venue weekly summary calculation',
			test: async () => {
				try {
					const result = await serverSideCalculationService.calculateVenueWeeklyTotals(
						testVenueId,
						testWeekStart
					);
					return {
						success: true,
						data: result,
						message: `Venue ${result.venueName}: ${result.totalShifts} shifts, ${result.uniqueEmployees} employees`
					};
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Venue weekly calculation failed'
					};
				}
			}
		},
		{
			name: 'Overall Weekly Summary',
			description: 'Test organization-wide weekly summary calculation',
			test: async () => {
				try {
					const result = await serverSideCalculationService.calculateOverallWeeklySummary(testWeekStart);
					return {
						success: true,
						data: result,
						message: `Organization: ${result.totalShifts} shifts, ${formatCurrency(result.totalCompensation)} total pay`
					};
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Overall weekly calculation failed'
					};
				}
			}
		}
	];

	// Run all tests
	async function runAllTests() {
		isRunning = true;
		testResults = [];

		for (const scenario of testScenarios) {
			currentTest = scenario.name;
			console.log(`🔄 Running test: ${scenario.name}`);

			try {
				const result = await scenario.test();
				testResults.push({
					name: scenario.name,
					description: scenario.description,
					...result,
					timestamp: new Date().toISOString()
				});
			} catch (error) {
				testResults.push({
					name: scenario.name,
					description: scenario.description,
					success: false,
					error: error.message,
					message: 'Test execution failed',
					timestamp: new Date().toISOString()
				});
			}

			// Small delay between tests
			await new Promise(resolve => setTimeout(resolve, 100));
		}

		currentTest = '';
		isRunning = false;
		console.log('✅ All tests completed');
	}

	// Run individual test
	async function runSingleTest(scenario) {
		currentTest = scenario.name;
		console.log(`🔄 Running single test: ${scenario.name}`);

		try {
			const result = await scenario.test();
			
			// Update or add result
			const existingIndex = testResults.findIndex(r => r.name === scenario.name);
			const newResult = {
				name: scenario.name,
				description: scenario.description,
				...result,
				timestamp: new Date().toISOString()
			};

			if (existingIndex >= 0) {
				testResults[existingIndex] = newResult;
			} else {
				testResults.push(newResult);
			}
		} catch (error) {
			console.error(`❌ Test failed: ${scenario.name}`, error);
		}

		currentTest = '';
	}

	// Clear results
	function clearResults() {
		testResults = [];
	}
</script>

<div class="container mx-auto px-4 py-8">
	<div class="max-w-6xl mx-auto">
		<!-- Header -->
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-gray-900 mb-2">Server-Side Calculation Testing</h1>
			<p class="text-gray-600">
				Comprehensive testing interface for new server-side RPC calculation functions.
				This page verifies that all pay calculations are working correctly with the new architecture.
			</p>
		</div>

		<!-- Test Controls -->
		<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
			<h2 class="text-xl font-semibold text-gray-900 mb-4">Test Controls</h2>
			<div class="flex flex-wrap gap-3">
				<button
					onclick={runAllTests}
					disabled={isRunning}
					class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					{isRunning ? 'Running Tests...' : 'Run All Tests'}
				</button>
				<button
					onclick={clearResults}
					disabled={isRunning}
					class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
				>
					Clear Results
				</button>
			</div>
			{#if currentTest}
				<div class="mt-4 text-sm text-blue-600">
					Currently running: <strong>{currentTest}</strong>
				</div>
			{/if}
		</div>

		<!-- Test Scenarios -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
			{#each testScenarios as scenario}
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					<h3 class="text-lg font-semibold text-gray-900 mb-2">{scenario.name}</h3>
					<p class="text-gray-600 text-sm mb-4">{scenario.description}</p>
					<button
						onclick={() => runSingleTest(scenario)}
						disabled={isRunning}
						class="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:opacity-50"
					>
						Run Test
					</button>
				</div>
			{/each}
		</div>

		<!-- Test Results -->
		{#if testResults.length > 0}
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
				<h2 class="text-xl font-semibold text-gray-900 mb-4">Test Results</h2>
				<div class="space-y-4">
					{#each testResults as result}
						<div class="border border-gray-200 rounded-lg p-4 {result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}">
							<div class="flex items-center justify-between mb-2">
								<h3 class="font-semibold {result.success ? 'text-green-800' : 'text-red-800'}">
									{result.success ? '✅' : '❌'} {result.name}
								</h3>
								<span class="text-xs text-gray-500">
									{new Date(result.timestamp).toLocaleTimeString()}
								</span>
							</div>
							<p class="text-sm {result.success ? 'text-green-700' : 'text-red-700'} mb-2">
								{result.message}
							</p>
							{#if result.error}
								<div class="text-xs text-red-600 bg-red-100 p-2 rounded">
									<strong>Error:</strong> {result.error}
								</div>
							{/if}
							{#if result.data}
								<details class="mt-2">
									<summary class="text-xs text-gray-600 cursor-pointer">View Data</summary>
									<pre class="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">{JSON.stringify(result.data, null, 2)}</pre>
								</details>
							{/if}
						</div>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Instructions -->
		<div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
			<h3 class="text-lg font-medium text-blue-800 mb-2">Testing Instructions</h3>
			<ul class="text-sm text-blue-700 space-y-1">
				<li>• Click "Run All Tests" to execute all server-side calculation tests</li>
				<li>• Individual tests can be run by clicking "Run Test" on each scenario</li>
				<li>• Check browser console for detailed RPC function logs</li>
				<li>• Green results indicate successful server-side calculations</li>
				<li>• Red results indicate errors that need investigation</li>
				<li>• All calculations should use Bulgarian Lev (лв) currency formatting</li>
			</ul>
		</div>
	</div>
</div>

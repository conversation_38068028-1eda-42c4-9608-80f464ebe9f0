/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {
      // Custom font family
      fontFamily: {
        'primary': ['-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', 'Oxygen-Sans', 'Ubuntu', 'Cantarell', '"Helvetica Neue"', 'sans-serif'],
      },
      
      // Custom font sizes matching design system
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1.5' }],
        'sm': ['0.875rem', { lineHeight: '1.5' }],
        'md': ['1rem', { lineHeight: '1.5' }],
        'lg': ['1.125rem', { lineHeight: '1.5' }],
        'xl': ['1.25rem', { lineHeight: '1.5' }],
        '2xl': ['1.5rem', { lineHeight: '1.2' }],
        '3xl': ['1.75rem', { lineHeight: '1.2' }],
      },
      
      // Custom colors matching design system
      colors: {
        primary: {
          DEFAULT: '#3b82f6',
          dark: '#0068F8',
          light: '#dbeafe',
        },
        success: {
          DEFAULT: '#10b981',
          light: '#dcfce7',
        },
        warning: {
          DEFAULT: '#f59e0b',
          light: '#fef3c7',
        },
        danger: {
          DEFAULT: '#ef4444',
          light: '#fee2e2',
        },
        purple: {
          DEFAULT: '#8b5cf6',
          light: '#ede9fe',
        },
        teal: '#58CEE1',
        sky: '#7EB3FC',
        text: {
          primary: '#1e293b',
          secondary: '#475569',
          tertiary: '#64748b',
          muted: '#94a3b8',
        },
        bg: {
          primary: '#ffffff',
          secondary: '#fffeff',
          tertiary: '#f1f5f9',
        },
        border: {
          standard: '#e2e8f0',
        },
      },
      
      // Custom spacing scale
      spacing: {
        'xs': '0.5rem',
        'sm': '0.75rem',
        'md': '1rem',
        'lg': '1.5rem',
        'xl': '2rem',
      },
      
      // Custom border radius
      borderRadius: {
        'small': '4px',
        'medium': '8px',
        'large': '12px',
      },
      
      // Layout dimensions
      height: {
        'header': '60px',
      },
      width: {
        'sidebar': '250px',
      },
      
      // Custom transitions
      transitionDuration: {
        'standard': '200ms',
      },
      
      // Custom shadows (none for flat design)
      boxShadow: {
        'none': 'none',
      },
    },
  },
  plugins: [],
}

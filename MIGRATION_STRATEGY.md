# Server-Side Calculation Migration Strategy

## **Overview**

This document outlines the comprehensive migration strategy from frontend calculations to server-side RPC calculations for the restaurant scheduling application, following Code Complete principles and maintaining backward compatibility.

## **Phase 1: Database RPC Functions Deployment ✅ COMPLETED**

### **Deployed Functions:**
- `calculate_shift_compensation(p_shift_id UUID)` - Single shift pay calculation
- `validate_shift_pay_rules(p_employee_id UUID, p_hours DECIMAL, p_proposed_rate DECIMAL)` - Pay validation
- `calculate_employee_weekly_totals(p_employee_id UUID, p_week_start DATE)` - Employee weekly summary
- `calculate_venue_weekly_totals(p_venue_id UUID, p_week_start DATE)` - Venue weekly summary
- `calculate_overall_weekly_summary(p_week_start DATE)` - Organization-wide summary

### **Deployment Steps:**
1. Execute `server_side_pay_calculations.sql` in Supabase SQL Editor
2. Verify all functions are created with proper permissions
3. Test basic function calls with sample data

## **Phase 2: TypeScript Service Layer ✅ COMPLETED**

### **Created Services:**
- `src/lib/services/serverSideCalculationService.ts` - Complete RPC wrapper service
- Comprehensive error handling and logging
- Type-safe interfaces for all RPC responses
- Consistent API patterns following existing service architecture

### **Key Features:**
- Defensive programming with proper error handling
- Detailed logging for debugging and monitoring
- Type safety with comprehensive interfaces
- Consistent naming conventions

## **Phase 3: Frontend Deprecation Strategy ✅ IN PROGRESS**

### **Deprecated Functions (Backward Compatible):**
- `getShiftPay()` - Fixed critical bug (hours × dailyRate → dailyRate)
- `calculateEmployeeWeeklySummary()` - Marked deprecated with warnings
- `calculateVenueSummary()` - Marked deprecated with warnings  
- `calculateWeeklySummary()` - Marked deprecated with warnings

### **Migration Approach:**
- Functions remain available but log deprecation warnings
- Clear migration path documented in function comments
- Gradual component-by-component migration
- No breaking changes during transition period

## **Phase 4: Component Refactoring ✅ IN PROGRESS**

### **ShiftModal Component Updates:**
- Server-side pay validation using `validateShiftPayRules()`
- Real-time validation feedback in UI
- Enhanced error handling and user feedback
- Maintains existing UI/UX patterns

### **Pending Component Updates:**
- `FooterToolbar.svelte` - Use `calculateOverallWeeklySummary()`
- `ScheduleGrid.svelte` - Use server-side calculations for weekly totals
- `EnhancedScheduleGrid.svelte` - Replace local calculations

## **Phase 5: Performance Optimization**

### **Caching Strategy:**
- Implement Redis caching for frequently accessed weekly summaries
- Cache invalidation on shift modifications
- Optimized database queries with proper indexing

### **Real-time Updates:**
- WebSocket integration for live calculation updates
- Optimistic UI updates with server-side validation
- Conflict resolution for concurrent edits

## **Critical Bug Fixes Applied**

### **1. Fixed getShiftPay() Calculation Error:**
```typescript
// BEFORE (INCORRECT):
if (!employee) {
    return hours * shift.dailyRate; // ❌ Inflated calculations
}

// AFTER (CORRECT):
if (!employee) {
    return shift.dailyRate; // ✅ dailyRate is total compensation
}
```

### **2. Server-Side Business Rule Validation:**
- Minimum wage compliance (15 BGN/hour)
- Full-day vs partial-day logic consistency
- Employee default rate integration
- Input validation and sanitization

## **Testing Strategy**

### **Unit Tests Required:**
- [ ] All RPC functions with various input scenarios
- [ ] Server-side validation edge cases
- [ ] Error handling and fallback mechanisms
- [ ] Currency formatting consistency

### **Integration Tests Required:**
- [ ] End-to-end shift creation with server-side validation
- [ ] Weekly summary calculations across components
- [ ] Multi-tenant data isolation verification
- [ ] Performance benchmarks vs frontend calculations

### **Manual Testing Checklist:**
- [ ] ShiftModal pay validation works in real-time
- [ ] Weekly totals match between old and new calculations
- [ ] Bulgarian Lev formatting consistent across all displays
- [ ] Error messages are user-friendly and actionable
- [ ] Performance is acceptable for real-time operations

## **Rollback Strategy**

### **Emergency Rollback Plan:**
1. **Immediate:** Disable server-side validation in ShiftModal
2. **Short-term:** Revert to deprecated functions (still available)
3. **Long-term:** Database function rollback if critical issues found

### **Monitoring and Alerts:**
- RPC function error rates and response times
- Calculation discrepancies between old/new methods
- User experience metrics during transition

## **Success Criteria**

### **Functional Requirements:**
- ✅ Zero calculation logic in frontend TypeScript/JavaScript
- ✅ All pay values come from server-side RPC responses
- ✅ Consistent calculations across all components
- ✅ Multi-tenant data isolation maintained
- ✅ Bulgarian Lev formatting preserved

### **Performance Requirements:**
- [ ] RPC response times < 200ms for single calculations
- [ ] Weekly summary calculations < 500ms
- [ ] No noticeable UI lag during real-time validation
- [ ] Database query optimization verified

### **Quality Requirements:**
- [ ] 100% test coverage for new RPC functions
- [ ] Zero TypeScript errors in refactored components
- [ ] Code review approval for all changes
- [ ] Documentation updated for new architecture

## **Next Steps**

### **Immediate (Next Sprint):**
1. Deploy RPC functions to production Supabase
2. Complete FooterToolbar component refactoring
3. Implement comprehensive test suite
4. Performance testing and optimization

### **Short-term (2-3 Sprints):**
1. Complete all component migrations
2. Remove deprecated function warnings
3. Implement caching strategy
4. User acceptance testing

### **Long-term (Future Releases):**
1. Remove deprecated functions entirely
2. Advanced performance optimizations
3. Real-time calculation updates
4. Enhanced business rule engine

## **Risk Mitigation**

### **Technical Risks:**
- **Database Performance:** Comprehensive indexing and query optimization
- **Network Latency:** Caching and optimistic UI updates
- **Data Consistency:** Transaction-based RPC functions

### **Business Risks:**
- **Calculation Accuracy:** Extensive testing and validation
- **User Experience:** Gradual migration with fallbacks
- **Downtime:** Blue-green deployment strategy

## **Communication Plan**

### **Stakeholder Updates:**
- Weekly progress reports during migration
- Performance metrics and user feedback
- Clear timeline and milestone communication

### **Developer Documentation:**
- Updated API documentation for new RPC functions
- Migration guides for future component updates
- Best practices for server-side calculation patterns

---

**Migration Status:** 🟡 **IN PROGRESS** - Core infrastructure complete, component migration ongoing

**Next Milestone:** Complete FooterToolbar and ScheduleGrid component refactoring

**Estimated Completion:** 2-3 sprints for full migration
